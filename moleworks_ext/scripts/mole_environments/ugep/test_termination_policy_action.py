# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test Termination Condition for Excavatio using the first action from a policy instead of zeros"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse

from isaaclab.app import A<PERSON><PERSON><PERSON>nch<PERSON>
import os
import sys

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import cli_args 
# add argparse arguments
parser = argparse.ArgumentParser(description="Test Termination Condition for Excavation Environment")
parser.add_argument("--cpu", action="store_true", default=False, help="Use CPU pipeline.")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")
# append RSL-RL cli arguments
cli_args.add_rsl_rl_args(parser)
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
args_cli.task = 'Isaac-m545-v0'
args_cli.num_envs = 4096
args_cli.headless = True

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import torch
import traceback

import carb
import pickle
import moleworks_ext.tasks  # noqa: F401
from isaaclab_tasks.utils import get_checkpoint_path, parse_env_cfg, get_run_path
from moleworks_ext.tasks.ugep.excavation_utils.excavation_arg_helper import get_play_args, override_env_cfg_with_args

from isaaclab_tasks.utils import parse_env_cfg
from moleworks_ext.tasks.ugep.excavation_utils.excavation_utils import plot_hist, plot_hist_double
from isaaclab_rl.rsl_rl import (
    RslRlOnPolicyRunnerCfg,
    RslRlVecEnvWrapper,
    export_policy_as_onnx,
)
from rsl_rl.runners import OnPolicyRunner

import matplotlib.pyplot as plt


DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")


def main():
    """Termination test with actions load from a specific policy agent with Orbit environment."""
    #--------- Make the environment
    agent_cfg: RslRlOnPolicyRunnerCfg = cli_args.parse_rsl_rl_cfg(args_cli.task, args_cli)
    # Load paths
    log_root_path = os.path.join("logs", "rsl_rl", agent_cfg.experiment_name)
    log_root_path = os.path.abspath(log_root_path)
    # Override manually # to not get last run/ model
    #agent_cfg.load_run =  '2024-03-16_18-56-54_train' # '2024-03-06_17-29-24_train' #
    # agent_cfg.load_checkpoint = 'model_14999.pt' # 'model_19999' # 
    resume_path = get_checkpoint_path(log_root_path, agent_cfg.load_run, agent_cfg.load_checkpoint)
    run_path = get_run_path(log_root_path, agent_cfg.load_run)


    # Override resume_path here with the right path here to access another Policy (Gym for example)
    # dt = 0.03, decimation = 3
    #resume_path = '/home/<USER>/isaac_ws/m545_isaac/logs/excavation/random_soil/train_24_03_14_16_11_53/model_9999.pt'

    # parse configuration
    env_cfg = parse_env_cfg(args_cli.task, use_gpu=not args_cli.cpu, num_envs=args_cli.num_envs)
    
    # some more overriding
    env_cfg.reset.sample_soil = True
    env_cfg.reset.only_above_soil = False
    env_cfg.send_timeouts = True
    env_cfg.terminations_excavation.disable_negative_termination = False
    env_cfg.limits.infinite_torque = False #args.infinite_torque
    env_cfg.decimation = 4
    env_cfg.sim.dt = 0.0375

    # create environment
    env = gym.make(args_cli.task, cfg=env_cfg)
    # wrap around environment for rsl-rl
    env = RslRlVecEnvWrapper(env)

    # reset environment
    env.unwrapped.curriculum_excavation.set_level_and_update(2000)
    env.reset()

    # load previously trained model
    ppo_runner = OnPolicyRunner(env, agent_cfg.to_dict(), log_dir=None, device=agent_cfg.device)
    ppo_runner.load(resume_path)
    print(f"[INFO]: Loading model checkpoint from: {resume_path}")

    # obtain the trained policy for inference
    policy = ppo_runner.get_inference_policy(device=env.unwrapped.device)

    # export policy to onnx 
    export_model_dir = os.path.join(os.path.dirname(resume_path), "exported")
    export_policy_as_onnx(ppo_runner.alg.actor_critic, export_model_dir, filename="policy.onnx")


    # Termination script 
    num_resets = 100
    num_steps = 1
    total_resets = (num_steps)*(num_resets) * env.num_envs
    done_count = torch.zeros(1, device=env.device)

    # run everything in inference mode 
    with torch.inference_mode():
        for i in range(num_resets):
            env.reset()
            print("Reset: ", i)

            # compute zero actions
            for j in range(num_steps):
                print("Step: ", j)
                # apply actions
                obs, _ = env.get_observations()
                actions = policy(obs)
                obs, rewards, dones, infos = env.step(actions)

                # Live Monitoring of most important termination conditions
                if env.unwrapped.termination_excavation.episode_neg_term_buf['bucket_aoa'].any():
                    print('aoa done at env', torch.nonzero(env.unwrapped.termination_excavation.episode_neg_term_buf['bucket_aoa'], as_tuple= True)[0])
                if env.unwrapped.termination_excavation.episode_neg_term_buf['bucket_vel'].any():
                    print('vel done at env', torch.nonzero(env.unwrapped.termination_excavation.episode_neg_term_buf['bucket_vel'], as_tuple= True)[0])

                done_count += torch.count_nonzero(dones)
                print(dones.sum())

    # close the simulator
    env.close()

    # Print success rate
    print(
    "done/resets: {} / {} ({} %)".format(int(done_count), int(total_resets), 100.0 * float(done_count) / (total_resets))
    )
    # Print Statistics
    for key, value in infos["episode_neg_term_counts"].items():
        print("neg term: " + key + ": " + str(sum(value)))
    for key, value in infos["episode_pos_term_counts"].items():
        print("pos term: " + key + ": " + str(sum(value)))

if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()

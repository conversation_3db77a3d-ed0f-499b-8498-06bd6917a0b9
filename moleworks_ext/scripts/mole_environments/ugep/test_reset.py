# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Script to play a checkpoint if an RL Excavation agent and log data"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import json
# local imports
import os
import sys

import carb
from isaaclab.app import App<PERSON>auncher

# Now you can import cli_args as if it was on the same level
import cli_args  # isort: skip

# add argparse arguments
parser = argparse.ArgumentParser(description="Train an RL agent with RSL-RL.")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")
parser.add_argument("--tags", nargs='+', type=str, default=[], help="Tags used for the run.")
# parser.add_argument("--asset_dir", type=str, default=None, help="Directory containing the asset files")
# parser.add_argument("--asset_name", type=str, default=None, help="Name of the asset file")
# append RSL-RL cli arguments
cli_args.add_rsl_rl_args(parser)
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
args_cli = parser.parse_args()

# Override command line arguments
args_cli.task = 'Isaac-ugep-v0'
args_cli.num_envs = 1500 
args_cli.headless = True


args_cli.device = "cuda"

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app


"""Rest everything follows."""
# this allows for runtime setting of the asset paths
# from omni.isaac.orbit_assets.general_excavator import set_asset_paths, get_default_asset_paths
# # Set the asset paths before creating the environment
# if args_cli.asset_dir and args_cli.asset_name:
#     set_asset_paths(args_cli.asset_dir, args_cli.asset_name)
# else:
#     # Use default paths if not specified
#     default_asset_dir, default_asset_name = get_default_asset_paths()
#     set_asset_paths(default_asset_dir, default_asset_name)

import gymnasium as gym
import os
import pickle
import torch
import traceback
from datetime import datetime
from pprint import pprint

from rsl_rl.runners import OnPolicyRunner

import moleworks_ext.tasks
from moleworks_ext.tasks.ugep.excavation_utils.excavation_arg_helper import get_play_args, override_env_cfg_with_args
from moleworks_ext.tasks.excavation.excavation_utils.excavation_logger import Logger
from isaaclab_tasks.utils import get_checkpoint_path, load_cfg_from_registry, parse_env_cfg
from isaaclab_rl.rsl_rl import RslRlOnPolicyRunnerCfg, RslRlVecEnvWrapper, export_policy_as_jit, export_policy_as_onnx


def main():
    """Play with Excavation agent."""
    #--------- Make the environment
    agent_cfg: RslRlOnPolicyRunnerCfg = cli_args.parse_rsl_rl_cfg(args_cli.task, args_cli)
    # Load paths
    log_root_path = os.path.join("logs", "rsl_rl", agent_cfg.experiment_name)
    log_root_path = os.path.abspath(log_root_path)
    # add year-month-day_hour-min-sec_train-reset 
    reset_log_path = os.path.join(log_root_path, datetime.now().strftime("%Y-%m-%d_%H-%M-%S") + "_reset")
    os.makedirs(reset_log_path, exist_ok=True)

    env_cfg = parse_env_cfg(args_cli.task, num_envs=args_cli.num_envs)

    # More overriding
    env_cfg.decimation = 4
    env_cfg.sim.dt = 0.04
    env_cfg.max_depth_height.theta = 0.5
    env_cfg.reset.sample_soil = True
    env_cfg.send_timeouts = False
    env_cfg.terminations_excavation.disable_negative_termination = False # args.disable_neg_terms
    env_cfg.reset.only_above_soil = True

    # Set reset parameters
    env_cfg.reset.sample_soil = True
    env_cfg.reset.sample_max_depth = True
    env_cfg.reset.sample_pullup_dist = True
    env_cfg.reset.sample_obstacles = False
    env_cfg.reset.only_above_soil = False
    env_cfg.reset.fixed_config = False

    # create isaac environment
    env = gym.make(args_cli.task, cfg=env_cfg)
    # wrap around environment for rsl-rl
    env = RslRlVecEnvWrapper(env)
    # Set curriculum, need to reset after
    env.unwrapped.curriculum_excavation.set_level_and_update(torch.tensor(100.0))

    # Settings
    num_resets = 20
    warmup_resets = 0
    num_steps = 10
    done_count = torch.zeros(1, device=env.device)

    # os.makedirs(play_log_dir)
    robot_index = 0  # which robot is used for logging
    logger = Logger(env.unwrapped, num_steps + 1, robot_index)

    # reset environment, necessary after curriculu
    # m update
    env.reset()
    obs, _ = env.get_observations()
    actions = torch.zeros((args_cli.num_envs, 3), device=env.device)
    depths = torch.zeros(num_resets - warmup_resets, args_cli.num_envs, device=env.device)
    depths_after_step = torch.zeros(num_resets - warmup_resets, args_cli.num_envs, device=env.device)

    # simulate environment
    # run everything in inference mode
    with torch.inference_mode():
        for i in range(num_resets):
            print(50 * "*")
            if i >= warmup_resets:
                depths[i - warmup_resets] = env.unwrapped.soil.bucket_depth.squeeze()

            for j in range(num_steps):
                obs, rewards, dones, infos = env.step(actions)

                if i >= warmup_resets:
                    depths_after_step[i - warmup_resets] = env.unwrapped.soil.bucket_depth.squeeze()
                    in_soil = env.unwrapped.soil.bucket_depth > 0.0
                    print("Percentage of in_soil: ", torch.mean(in_soil.to(torch.float32)))
                if i > warmup_resets:
                    if dones.any():
                        done_count += torch.sum(dones)
            env.reset()

    in_soil = torch.sum(depths > 0.0)
    in_soil_after_step = torch.sum(depths_after_step > 0.0)
    total_resets = (num_resets - warmup_resets) * env.num_envs

    print(
        "resets in soil: {} / {} ({} %)".format(int(in_soil.item()), int(total_resets), 100 * float(in_soil) / total_resets)
    )

    print("resets in soil after step: {} / {} ({} %)".format(int(in_soil_after_step.item()), int(total_resets), 100 * float(in_soil_after_step) / total_resets))

    print(
        "done/resets: {} / {} ({} %)".format(int(done_count), int(total_resets), 100.0 * float(done_count) / (total_resets))
    )

    for key, value in infos["episode_neg_term_counts"].items():
        print("neg term: " + key + ": " + str(sum(value)))

    for key, value in infos["episode_pos_term_counts"].items():
        print("pos term: " + key + ": " + str(sum(value)))

    # Save termination statistics per excavator type
    all_data_to_serialize = []
    failure_rates = []
    total_failures = 0
    total_resets_per_asset = {}

    for asset_id in infos.get("terminations_by_asset_count", {}).keys():
        data_to_serialize = {"asset_id": asset_id}

        # Combine negative terminations, positive terminations, and timeouts
        combined_terminations = {
            **infos["terminations_by_asset_count"][asset_id],
            **infos["positive_terminations_by_asset_count"][asset_id],
            "timeout": infos["timeout_by_asset_count"][asset_id]
        }

        asset_total_terminations = sum(v.sum().item() if isinstance(v, torch.Tensor) else v for v in combined_terminations.values())
        total_failures += asset_total_terminations

        # Calculate total resets for this asset
        asset_resets = infos.get("resets_by_asset_count", {}).get(asset_id, total_resets / len(infos.get("terminations_by_asset_count", {})))
        total_resets_per_asset[asset_id] = asset_resets

        failure_rate = (asset_total_terminations / asset_resets) * 100
        failure_rates.append((asset_id, failure_rate))

        for key, value in combined_terminations.items():
            if isinstance(value, torch.Tensor):
                mean_value = value.sum().item()
                data_to_serialize[key] = mean_value
            elif isinstance(value, dict):
                data_to_serialize[key] = {k: v.mean().item() if isinstance(v, torch.Tensor) else v for k, v in value.items()}
            else:
                data_to_serialize[key] = value

        all_data_to_serialize.append(data_to_serialize)

        # Save the converted dictionary to a file
        asset_filename = f"terminations_asset_{asset_id}.json"
        asset_filepath = os.path.join(reset_log_path, asset_filename)
        with open(asset_filepath, 'w') as file:
            json.dump(data_to_serialize, file)

    # Print failure rates in decreasing order
    print("\nFailure rates per asset:")
    failure_rates.sort(key=lambda x: x[1], reverse=True)
    for asset_id, failure_rate in failure_rates:
        print(f"Asset ID {asset_id}: Failure Rate {failure_rate:.2f}% (Failures: {int(failure_rate * total_resets_per_asset[asset_id] / 100)}, Resets: {int(total_resets_per_asset[asset_id])})")

    # Calculate and print overall failure rate
    overall_failure_rate = (total_failures / total_resets) * 100
    print(f"\nOverall Failure Rate: {overall_failure_rate:.2f}% (Total Failures: {total_failures}, Total Resets: {total_resets})")

    # Save overall failure rate and failure rates by asset to a JSON file
    overall_stats = {
        "overall_failure_rate": overall_failure_rate,
        "total_failures": total_failures,
        "total_resets": total_resets,
        "failure_rates_by_asset": {asset_id: rate for asset_id, rate in failure_rates}
    }
    overall_stats_filepath = os.path.join(reset_log_path, "overall_stats.json")
    with open(overall_stats_filepath, 'w') as file:
        json.dump(overall_stats, file)

    # Plotting the distribution of depths
    import matplotlib.pyplot as plt

    fig, axs = plt.subplots(1, 2, figsize=(20, 6))  # Create a figure with two subplots side by side

    # Plot for initial depths
    axs[0].hist(depths.cpu().numpy().flatten(), bins=50, color='blue', alpha=0.7)
    axs[0].set_title('Distribution of Depths')
    axs[0].set_xlabel('Depth')
    axs[0].set_ylabel('Frequency')
    axs[0].grid(True)

    # Plot for depths after step
    axs[1].hist(depths_after_step.cpu().numpy().flatten(), bins=50, color='blue', alpha=0.7)
    axs[1].set_title('Distribution of Depths after Step')
    axs[1].set_xlabel('Depth')
    # set xlim from -0.5 to 0.5
    axs[1].set_xlim(-0.5, 0.5)
    axs[1].set_ylabel('Frequency')
    axs[1].grid(True)

    plt.show()

    # logger.log_dones()
    # logger.plot_states(show=False)
    # logger.save_for_ros_plotter(play_log_dir)
    # logger.save_plots(play_log_dir, open=True)
    input("hit [ENTER] to exit")
    # close the simulator
    env.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
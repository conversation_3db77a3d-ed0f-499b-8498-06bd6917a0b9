# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Script to play a checkpoint if an RL Excavation agent and log data with fixed soil and reset conditions"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""

import argparse

from isaaclab.app import AppLauncher

# local imports
import os
import sys

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# local imports
import cli_args  # isort: skip

# add argparse arguments
parser = argparse.ArgumentParser(description="Train an RL agent with RSL-RL.")
parser.add_argument("--cpu", action="store_true", default=False, help="Use CPU pipeline.")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")
# append RSL-RL cli arguments
cli_args.add_rsl_rl_args(parser)
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
args_cli = parser.parse_args()
args_cli.task = 'Isaac-m545-v0'
args_cli.num_envs = 1
args_cli.headless = False
args_cli.seed = 22

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""
import gymnasium as gym
import os
import torch
import traceback

import carb
from rsl_rl.runners import OnPolicyRunner
from datetime import datetime
import pickle

import moleworks_ext.tasks  # noqa: F401
from isaaclab.envs import DirectMARLEnv, multi_agent_to_single_agent
from isaaclab_tasks.utils import get_checkpoint_path, parse_env_cfg, get_run_path
from isaaclab_rl.rsl_rl import (
    RslRlOnPolicyRunnerCfg,
    RslRlVecEnvWrapper,
    export_policy_as_onnx,
)
from moleworks_ext.tasks.ugep.excavation_utils.excavation_logger import Logger
from moleworks_ext.tasks.ugep.excavation_utils.excavation_arg_helper import get_play_args, override_env_cfg_with_args
from pprint import pprint

def main():
    """Play with Excavation agent."""
    #--------- Make the environment
    agent_cfg: RslRlOnPolicyRunnerCfg = cli_args.parse_rsl_rl_cfg(args_cli.task, args_cli)
    # Load paths
    log_root_path = os.path.join("logs", "rsl_rl", agent_cfg.experiment_name)
    log_root_path = os.path.abspath(log_root_path)
    # Override manually # to not get last run/ model
    #agent_cfg.load_run =  '2024-03-16_18-56-54_train' # '2024-03-06_17-29-24_train' #
    # agent_cfg.load_checkpoint = 'model_14999.pt' # 'model_19999' # 
    resume_path = get_checkpoint_path(log_root_path, agent_cfg.load_run, agent_cfg.load_checkpoint)
    run_path = get_run_path(log_root_path, agent_cfg.load_run)

    # Override resume_path here with the right path here to access another Policy (Gym for example)
    # dt = 0.03, decimation = 3
    #resume_path = '/home/<USER>/isaac_ws/m545_isaac/logs/excavation/random_soil/train_24_03_14_16_11_53/model_9999.pt'

    # Override Env Cfg with Play Args
    # Load train args
    with open(os.path.join(run_path, "params/env.pkl"), "rb") as f:
        args = pickle.load(f)
    play_args = get_play_args()
    for key, value in vars(play_args).items():
        vars(args)[key] = value
    env_cfg = parse_env_cfg(args_cli.task, use_gpu=not args_cli.cpu, num_envs=args_cli.num_envs)
    env_cfg = override_env_cfg_with_args(env_cfg, args)

    env_cfg.max_depth_height.theta = 0.5


    # More overriding
    env_cfg.decimation = 4
    env_cfg.sim.dt = 0.0375
    env_cfg.soil_height.theta = 0.5
    env_cfg.max_depth_height.theta = 0.5
    # Set all randomization to false
    env_cfg.reset.sample_soil = False
    env_cfg.send_timeouts = False
    env_cfg.reset.only_above_soil = True # This fixed condition is above soil, can be set to false as well
    env_cfg.reset.sample_pullup_dist = False
    env_cfg.terminations_excavation.disable_negative_termination = False #args.disable_neg_terms

    # create isaac environment
    env = gym.make(args_cli.task, cfg=env_cfg)
    # wrap around environment for rsl-rl
    env = RslRlVecEnvWrapper(env)
    # -- Fixed conditions set after next reset
    env_cfg.reset.fixed_config = True
    # Fix the indice after reset
    env_cfg.reset.configs = {"idx": 96369}
    # Set curriculum, need to reset after
    env.unwrapped.curriculum_excavation.set_level_and_update(2000)

    # Fix the soil
    env.unwrapped.pullup_dist = torch.tensor([2.3], device='cuda:0')
    env.unwrapped.soil.max_depth_height.z = torch.tensor([[-3.0000, -2.9878, -2.9735, -2.9603, -2.9495, -2.9428, -2.9413, -2.9455,
         -2.9559, -2.9724, -2.9941, -3.0000, -3.0000, -3.0000, -3.0000, -3.0000,
         -3.0000, -3.0000, -3.0000, -3.0000, -3.0000, -3.0000, -3.0000, -3.0000,
         -3.0000, -3.0000, -3.0000, -3.0000, -3.0000, -2.9849, -2.9670, -2.9490,
         -2.9309, -2.9121, -2.8927, -2.8728, -2.8530, -2.8340, -2.8167, -2.8019,
         -2.7907, -2.7836, -2.7809, -2.7830, -2.7896, -2.8002, -2.8137, -2.8292,
         -2.8457, -2.8618, -2.8762, -2.8884, -2.8976, -2.9030, -2.9044, -2.9021,
         -2.8962, -2.8872, -2.8758, -2.8625, -2.8483, -2.8340, -2.8203, -2.8081,
         -2.7980, -2.7905, -2.7857, -2.7838, -2.7849, -2.7885, -2.7940, -2.8008,
         -2.8082, -2.8151, -2.8212, -2.8255, -2.8277, -2.8279, -2.8261, -2.8223,
         -2.8177]], device='cuda:0').reshape(1,-1)
    env.unwrapped.soil.soil_height.z = torch.tensor([[-1.6049, -1.6422, -1.6778, -1.7100, -1.7368, -1.7568, -1.7693, -1.7735,
         -1.7700, -1.7592, -1.7425, -1.7214, -1.6975, -1.6727, -1.6489, -1.6276,
         -1.6100, -1.5974, -1.5902, -1.5886, -1.5925, -1.6016, -1.6151, -1.6321,
         -1.6516, -1.6724, -1.6933, -1.7133, -1.7313, -1.7465, -1.7582, -1.7661,
         -1.7700, -1.7700, -1.7667, -1.7606, -1.7525, -1.7434, -1.7341, -1.7253,
         -1.7179, -1.7122, -1.7085, -1.7071, -1.7077, -1.7103, -1.7146, -1.7201,
         -1.7268, -1.7345, -1.7431, -1.7525, -1.7629, -1.7743, -1.7869, -1.8008,
         -1.8160, -1.8325, -1.8502, -1.8690, -1.8885, -1.9085, -1.9287, -1.9487,
         -1.9680, -1.9867, -2.0042, -2.0205, -2.0354, -2.0488, -2.0607, -2.0713,
         -2.0805, -2.0882, -2.0945, -2.0994, -2.1028, -2.1043, -2.1040, -2.1013,
         -2.0962]], device='cuda:0').reshape(1,-1)
    env.unwrapped.soil.soil_height.offset = torch.tensor([[-1.7491]], device='cuda:0')
    env.unwrapped.soil.max_depth_height.offset = torch.tensor([[-2.9521]], device='cuda:0')

    # load previously trained model
    ppo_runner = OnPolicyRunner(env, agent_cfg.to_dict(), log_dir=None, device=agent_cfg.device)
    ppo_runner.load(resume_path)

    # obtain the trained policy for inference
    policy = ppo_runner.get_inference_policy(device=env.unwrapped.device)

    # export policy to onnx 
    export_model_dir = os.path.join(os.path.dirname(resume_path), "exported")
    export_policy_as_onnx(ppo_runner.alg.actor_critic, export_model_dir, filename="policy.onnx")

    # Settings
    num_steps = 100 #int(args.time / (env.unwrapped.physics_dt * env.unwrapped.cfg.decimation)) # t = 20

    #Logger
    # create logdir
    play_log_dir = os.path.join(
        run_path,
       "play_"+os.path.splitext(os.path.basename(resume_path))[0],
        datetime.now().strftime("%y_%m_%d_%H_%M_%S_") + "benchmarking",
    )
    os.makedirs(play_log_dir)
    robot_index = 0  # which robot is used for logging
    logger = Logger(env, num_steps + 1, robot_index)

    # reset environment
    env.reset()
    obs, _ = env.get_observations()
    # Check if DOF POS are fine
    print('DOF POS',env.unwrapped.m545_measurements.joint_pos)

    # simulate environment
    # run everything in inference mode
    with torch.inference_mode():
        
        for i in range(num_steps):
            # First step after reset
            logger.log_states(i)
            # agent stepping
            actions = policy(obs)
            # env stepping
            obs, rewards, dones, infos = env.step(actions)
            
            if dones.any():
                print("i: ", i)
                print("reward: ", rewards)
                pprint(infos["episode_pos_term_counts"])
                pprint(infos["episode_neg_term_counts"])
                # Check if DOF POS are still the same after reset
                print('DOF POS',env.unwrapped.m545_measurements.joint_pos)
        # Log last state    
        logger.log_states(i + 1)



    logger.log_dones()
    logger.plot_states(show=False)
    logger.save_for_ros_plotter(play_log_dir)
    logger.save_plots(play_log_dir, open=True)
    logger.save_state_to_csv(file_path='/home/<USER>/Desktop/Data/isaac_sim.csv')
    input("hit [ENTER] to exit")

    # close the simulator
    env.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()


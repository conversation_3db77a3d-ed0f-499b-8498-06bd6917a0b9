import argparse
import os
import pickle
import shutil
import sys
import xml.etree.ElementTree as ET
import yaml
from typing import Any, Dict

import trimesh
import numpy as np
import pandas as pd

from isaaclab.app import AppLauncher

# Modify the parse_arguments function to include all necessary arguments
def parse_arguments():
    parser = argparse.ArgumentParser(description="Generate necessary files from an existing URDF.")
    parser.add_argument("--urdf_path", type=str, required=True, help="Path to the input URDF file.")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory to save the generated files.")
    parser.add_argument("--headless", action="store_true", default=True, help="Run in headless mode")
    args = parser.parse_args()
    return args

# Initialize AppLauncher with headless mode from parsed arguments
args = parse_arguments()
app_launcher = AppLauncher(headless=args.headless)
simulation_app = app_launcher.app

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import cli_args  # isort: skip

from moleworks_ext.assets.general_excavator import Cat323ReferenceValues, GeneralExcavatorReferenceValues
from moleworks_ext.tasks.gep.excavation_utils.generate_ee_contraints import generate_ee_constraints_model
from moleworks_ext.tasks.gep.excavation_utils.helpers import extract_transformations_and_limits


def generate_scale_factors_yaml(output_path: str, scale_factors: Dict[str, list]):
    file_path = os.path.join(output_path, "scale_factors.yaml")
    with open(file_path, 'w') as file:
        yaml.dump(scale_factors, file, default_flow_style=False)
    print(f"Scale factors saved to {file_path}")


def load_urdf(urdf_path: str) -> ET.ElementTree:
    tree = ET.parse(urdf_path)
    return tree


def load_torque_limits(torque_limits_path: str) -> Dict[str, Any]:
    with open(torque_limits_path, 'r') as file:
        torque_limits = yaml.safe_load(file)
    return torque_limits


def extract_scale_factors(urdf_tree: ET.ElementTree, reference_values: GeneralExcavatorReferenceValues) -> Dict[str, list]:
    scale_factors = {}
    root = urdf_tree.getroot()
    for link in root.findall('link'):
        link_name = link.get('name')
        if link_name in ['BASE', 'BOOM', 'DIPPER', 'SHOVEL']:
            # Extract scaling factors based on reference values and some logic
            # Here, as an example, we set scaling factors to 1.0
            # Modify this logic based on your actual scaling requirements
            if link_name == 'BASE':
                scale_x = 1.0  # Example scaling factor
                scale_y = 1.0
                scale_z = 1.0
            elif link_name == 'BOOM':
                scale_x = 1.0  # Example scaling factor
                scale_y = 1.0
                scale_z = 1.0
            elif link_name == 'DIPPER':
                scale_x = 1.0  # Example scaling factor
                scale_y = 1.0
                scale_z = 1.0
            elif link_name == 'SHOVEL':
                scale_x = 1.0  # Example scaling factor
                scale_y = 1.0
                scale_z = 1.0
            scale_factors[link_name] = [scale_x, scale_y, scale_z]
            print(f"Extracted scale factors for {link_name}: {scale_factors[link_name]}")
    return scale_factors


def copy_file(src: str, dest: str):
    shutil.copy(src, dest)
    print(f"Copied {src} to {dest}")


def main():
    args = parse_arguments()

    # Ensure output directory and subdirectories exist
    limits_dir = os.path.join(args.output_dir, 'limits')
    urdf_dir = os.path.join(args.output_dir, 'urdf')
    os.makedirs(limits_dir, exist_ok=True)
    os.makedirs(urdf_dir, exist_ok=True)

    # Load URDF
    urdf_tree = load_urdf(args.urdf_path)
    print(f"Loaded URDF from {args.urdf_path}")

    # Copy the input URDF to the urdf directory
    urdf_filename = os.path.basename(args.urdf_path)
    dest_urdf_path = os.path.join(urdf_dir, urdf_filename)
    copy_file(args.urdf_path, dest_urdf_path)

    # Copy torque_limits.yaml from input directory to output directory
    input_torque_limits_path = os.path.join(os.path.dirname(os.path.dirname(args.urdf_path)), 'limits', 'torque_limits.yaml')
    output_torque_limits_path = os.path.join(limits_dir, 'torque_limits.yaml')
    shutil.copy(input_torque_limits_path, output_torque_limits_path)
    print(f"Copied torque_limits.yaml from {input_torque_limits_path} to {output_torque_limits_path}")

    # Load torque limits from the copied file
    torque_limits = load_torque_limits(output_torque_limits_path)
    print(f"Loaded torque limits from {output_torque_limits_path}")

    # Save torque_limits.yaml to limits directory
    dest_torque_limits_path = os.path.join(limits_dir, 'torque_limits.yaml')
    with open(dest_torque_limits_path, 'w') as file:
        yaml.dump(torque_limits, file, default_flow_style=False)
    print(f"Torque limits saved to {dest_torque_limits_path}")
    # Find and copy torque_factors.pkl from input folder to limits directory
    input_limits_dir = os.path.join(os.path.dirname(os.path.dirname(args.urdf_path)), 'limits')
    pkl_source_path = next((f for f in os.listdir(input_limits_dir) if f.endswith('.pkl')), None)
    if pkl_source_path is None:
        raise FileNotFoundError("No .pkl file found in the input limits folder.")
    pkl_source_full_path = os.path.join(input_limits_dir, pkl_source_path)
    dest_pkl_path = os.path.join(limits_dir, 'torque_factors.pkl')
    copy_file(pkl_source_full_path, dest_pkl_path)

    # Copy w_P_wb.yaml from input directory to urdf directory
    input_w_p_wb_path = os.path.join(os.path.dirname(args.urdf_path), 'w_P_wb.yaml')
    dest_w_p_wb_path = os.path.join(urdf_dir, 'w_P_wb.yaml')
    copy_file(input_w_p_wb_path, dest_w_p_wb_path)

    # Extract transformations and limits from URDF
    joint_names = ['J_BOOM', 'J_DIPPER', 'J_SHOVEL', 'J_ENDEFFECTOR_CONTACT']
    transformations_and_limits = extract_transformations_and_limits(args.urdf_path, joint_names)
    transformations_pickle_path = os.path.join(args.output_dir, "transformations_and_limits.pickle")
    with open(transformations_pickle_path, "wb") as f:
        pickle.dump(transformations_and_limits, f)
    print(f"Transformations and limits saved to {transformations_pickle_path}")

    # Extract scaling factors
    reference_values = Cat323ReferenceValues()
    scale_factors = extract_scale_factors(urdf_tree, reference_values)

    # Save scale_factors.yaml in the main output directory
    generate_scale_factors_yaml(args.output_dir, scale_factors)

    # Generate model_coefficients.json using generate_ee_constraints_model
    # Assuming generate_ee_constraints_model expects the urdf directory as input
    generate_ee_constraints_model(args.output_dir)
    print(f"model_coefficients.json generated in {args.output_dir}")

    # Close the AppLauncher if necessary
    print("All files generated successfully.")


if __name__ == "__main__":
    main()
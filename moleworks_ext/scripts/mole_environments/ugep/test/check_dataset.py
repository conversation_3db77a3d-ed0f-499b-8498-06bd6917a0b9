import seaborn as sns
import matplotlib.pyplot as plt
import pandas as pd

# Load the dataset
file_path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/merged_excavator_info_processed.pkl'
merged_data = pd.read_pickle(file_path)

# Check for duplicates
duplicates = merged_data.duplicated().sum()

# Display basic information about the dataset
info = merged_data.info()

# Display summary statistics
summary_stats = merged_data.describe()

# Plot distributions of key variables
key_variables = ['Engine Power', 'Boom Length', 'Dipper Length', 'Shovel Length', 'Max Dig Depth', 
                 'Max Reach Ground', 'Bucket Capacity', 'Base Mass', 'Track Length', 'Boom Mass', 
                 'Dipper Mass', 'Shovel Mass']

plt.figure(figsize=(15, 10))
for i, var in enumerate(key_variables, 1):
    plt.subplot(4, 3, i)
    sns.histplot(merged_data[var], bins=50, kde=True)
    plt.title(var)

plt.tight_layout()
plt.show()

# Plot pairwise relationships
sns.pairplot(merged_data[key_variables[:5]])  # Plotting only a subset to avoid overload
plt.show()

import os
import shutil


def rename_usd_file(folder_path, current_name, new_name):
    """
    Recursively search for a USD file with the given name and rename it.

    Args:
        folder_path (str): The path to the folder to start searching from.
        current_name (str): The current name of the USD file to find.
        new_name (str): The new name for the USD file.

    Returns:
        bool: True if the file was found and renamed, False otherwise.
    """
    for root, dirs, files in os.walk(folder_path):
        if current_name in files:
            old_path = os.path.join(root, current_name)
            new_path = os.path.join(root, new_name)
            os.rename(old_path, new_path)
            print(f"Renamed '{old_path}' to '{new_path}'")
            return True

    print(f"File '{current_name}' not found in '{folder_path}' or its subfolders.")
    return False

# Example usage:
# rename_usd_file("/path/to/folder", "old_file.usd", "new_file.usd")


def rename_numeric_folders(folder_path, shift):
    # Get all items in the folder
    items = os.listdir(folder_path)
    
    # Filter for numeric folders and sort them in reverse order
    numeric_folders = sorted([f for f in items if f.isdigit()], key=int, reverse=True)
    
    # Create a set of existing folder names for quick lookup
    existing_names = set(items)
    
    for folder in numeric_folders:
        old_number = int(folder)
        new_number = old_number + shift
        
        # Find the next available new number
        while str(new_number) in existing_names:
            new_number += 1 if shift > 0 else -1
        
        old_path = os.path.join(folder_path, folder)
        new_path = os.path.join(folder_path, str(new_number))
        
        # Rename the folder
        shutil.move(old_path, new_path)
        
        # Update the set of existing names
        existing_names.remove(folder)
        existing_names.add(str(new_number))

# Example usage
# rename_numeric_folders('/path/to/folder', 5)
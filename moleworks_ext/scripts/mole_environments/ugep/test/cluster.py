import pandas as pd
import numpy as np
from sklearn.cluster import DBSCAN
from tqdm import tqdm

def cluster_and_sample(data, eps=0.5, min_samples=5, sample_size=10000):
    clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(data)
    labels = clustering.labels_

    # Sample from each cluster
    unique_labels = np.unique(labels)
    sampled_data = []
    cluster_counts = []
    
    for label in tqdm(unique_labels, desc="Clustering and sampling"):
        cluster_data = data[labels == label]
        cluster_counts.append(len(cluster_data))
        if label == -1:  # Noise points
            print(f"Found {len(cluster_data)} noise points")
            continue
        if len(cluster_data) > sample_size:
            sampled_data.append(cluster_data[np.random.choice(len(cluster_data), sample_size, replace=False)])
        else:
            sampled_data.append(cluster_data)
    
    print(f"Number of clusters found: {len(unique_labels) - 1}")  # Subtract 1 to exclude noise cluster
    print(f"Cluster sizes: {cluster_counts}")
    
    if not sampled_data:
        print("No valid clusters found. Returning original data.")
        return data
    
    return np.vstack(sampled_data)

# ... rest of the code remains the same ...

# Load the data
file_path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/merged_excavator_info_processed.csv'
merged_data = pd.read_csv(file_path)

# Select the numeric columns
numeric_columns = merged_data.select_dtypes(include=[np.number]).columns
data_to_sample = merged_data[numeric_columns].values

# Perform clustering and sampling
print("Starting clustering and sampling process...")
sampled_data = cluster_and_sample(data_to_sample, eps=1.0, min_samples=10, sample_size=1000)
print("Clustering and sampling completed.")

# Convert back to DataFrame
sampled_data_df = pd.DataFrame(sampled_data, columns=numeric_columns)

# Save the sampled dataset to a file
sampled_file_path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/clustered_excavator_info_processed.pkl'
sampled_data_df.to_pickle(sampled_file_path)
print(f"Sampled data saved to {sampled_file_path}")

import torch
from typing import <PERSON><PERSON>
import math
import numpy as np # For comparing angles robustly

# --- Helper Function ---
def compare_angles(a1, a2, tol):
    """Compares angles robustly, handling wrapping around +/- pi."""
    diff = a1 - a2
    diff = torch.remainder(diff + math.pi, 2 * math.pi) - math.pi
    return torch.abs(diff) < tol

# --- Kinematic Functions WITH OFFSET HANDLING ---

def inverse_kinematics_analytic_fixed_links_with_offsets(
    x: torch.Tensor,
    z: torch.Tensor,
    alpha: torch.Tensor,
    rel_positions: torch.Tensor, # Input: [J1->J2, J2->EE, EE->Tip]
    joint_offsets: torch.Tensor # Input: [OffsetJ1, OffsetJ2, OffsetJ3]
) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
    """
    Compute the inverse kinematics analytically, accounting for offsets.
    Based on user feedback, assumes rel_positions[0] is J1->J2, [1] is J2->EE, [2] is EE->Tip.
    Calculates controllable angles q1, q2, q3 for joints J1, J2, J3.
    """
    x = x.view(-1, 1)
    z = z.view(-1, 1)
    alpha = alpha.view(-1, 1)
    batch_size = x.shape[0]
    num_joints = joint_offsets.shape[1]

    if rel_positions.shape[1] < 3:
         raise ValueError(f"rel_positions needs at least 3 entries for [J1->J2, J2->EE, EE->Tip]. Got shape {rel_positions.shape}")
    if num_joints != 3:
         raise ValueError(f"This specific analytic IK implementation requires exactly 3 joints, got {num_joints}")

    offset1 = joint_offsets[:, 0, 1:2] # Offset for joint 1
    offset2 = joint_offsets[:, 1, 1:2] # Offset for joint 2
    offset3 = joint_offsets[:, 2, 1:2] # Offset for joint 3

    l0 = torch.norm(rel_positions[:, 0, [0, 2]], dim=1, keepdim=True) # Length(J1->J2)
    l1 = torch.norm(rel_positions[:, 1, [0, 2]], dim=1, keepdim=True) # Length(J2->EE)
    l2 = torch.norm(rel_positions[:, 2, [0, 2]], dim=1, keepdim=True) # Length(EE->Tip)

    l0 = torch.where(l0 == 0, torch.full_like(l0, 1e-9), l0)
    l1 = torch.where(l1 == 0, torch.full_like(l1, 1e-9), l1)

    theta_2 = torch.atan2(rel_positions[:, 1, 2:3], rel_positions[:, 1, 0:1])
    theta_end = torch.atan2(rel_positions[:, 2, 2:3], rel_positions[:, 2, 0:1])

    alpha_end = alpha - theta_end
    p2x = x - l2 * torch.cos(alpha_end)
    p2z = z + l2 * torch.sin(alpha_end) # Original sign convention

    cos_q2_arg = (p2x**2 + p2z**2 - l0**2 - l1**2) / (2 * l0 * l1 + 1e-9)
    cos_q2_arg = torch.clamp(cos_q2_arg, -1.0, 1.0)
    q2_total_geom = torch.acos(cos_q2_arg) # Geometric angle related to J2

    q1_total = -torch.atan2(p2z, p2x) - torch.atan2(l1 * torch.sin(q2_total_geom), l0 + l1 * torch.cos(q2_total_geom))
    q2_total = q2_total_geom + theta_2
    q3_total = alpha - q1_total - q2_total

    q1_ctrl = q1_total - offset1
    q2_ctrl = q2_total - offset2
    q3_ctrl = q3_total - offset3

    q1_ctrl = torch.remainder(q1_ctrl + math.pi, 2 * math.pi) - math.pi
    q2_ctrl = torch.remainder(q2_ctrl + math.pi, 2 * math.pi) - math.pi
    q3_ctrl = torch.remainder(q3_ctrl + math.pi, 2 * math.pi) - math.pi

    return q1_ctrl, q2_ctrl, q3_ctrl


def forward_kinematics_2D_batch_with_offsets(
    rel_positions: torch.Tensor,    # Input: [J1->J2, J2->EE, EE->Tip]
    joint_angles_ctrl: torch.Tensor,# Input: [q1_ctrl, q2_ctrl, q3_ctrl]
    joint_offsets: torch.Tensor     # Input: [OffsetJ1, OffsetJ2, OffsetJ3]
) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    Perform forward kinematics, accounting for offsets.
    Assumes rel_positions[i] is rotated by (joint_angles_ctrl[i] + offset[i]).
    """
    batch_size = rel_positions.shape[0]
    num_segments = rel_positions.shape[1]
    num_angles = joint_angles_ctrl.shape[1]

    if num_segments != num_angles:
        raise ValueError(f"Mismatch between rel_positions segments ({num_segments}) and joint angles ({num_angles})")
    if joint_offsets.shape[1] != num_angles:
        raise ValueError(f"Mismatch between joint_offsets ({joint_offsets.shape[1]}) and joint angles ({num_angles})")

    num_joints = num_angles
    joint_positions = torch.zeros(batch_size, num_joints + 1, 2, device=rel_positions.device, dtype=rel_positions.dtype)
    alpha_cumulative = torch.zeros((batch_size, 1), device=rel_positions.device, dtype=rel_positions.dtype)
    x = torch.zeros((batch_size, 1), device=rel_positions.device, dtype=rel_positions.dtype)
    z = torch.zeros((batch_size, 1), device=rel_positions.device, dtype=rel_positions.dtype)

    for i in range(num_joints):
        q_ctrl = joint_angles_ctrl[:, i:i+1]
        offset = joint_offsets[:, i, 1:2]
        q_total = q_ctrl + offset
        alpha_cumulative += q_total

        dx = rel_positions[:, i, 0:1]
        dz = rel_positions[:, i, 2:3]
        # Original rotation convention
        new_dx = dx * torch.cos(alpha_cumulative) + dz * torch.sin(alpha_cumulative)
        new_dz = -dx * torch.sin(alpha_cumulative) + dz * torch.cos(alpha_cumulative)
        x += new_dx
        z += new_dz
        joint_positions[:, i+1, 0] = x.squeeze(1)
        joint_positions[:, i+1, 1] = z.squeeze(1)

    alpha_cumulative = torch.remainder(alpha_cumulative + math.pi, 2 * math.pi) - math.pi
    end_effector = torch.cat([x, z, alpha_cumulative], dim=1) # This is the TIP pose

    return joint_positions, end_effector


# --- Jacobian Function WITH OFFSET HANDLING ---

def compute_jacobian_torch_with_offsets(
    rel_positions: torch.Tensor, # Input: [J1->J2, J2->EE, EE->Tip]
    q_ctrl: torch.Tensor,        # Input: [q1_ctrl, q2_ctrl, q3_ctrl]
    joint_offsets: torch.Tensor  # Input: [OffsetJ1, OffsetJ2, OffsetJ3]
) -> torch.Tensor:
    """
    Compute numerical Jacobian d(EndEffectorPose)/d(q_ctrl), accounting for offsets.
    Assumes rel_positions[i] is rotated by (q_ctrl[i] + offset[i]).
    """
    batch_size = rel_positions.shape[0]
    num_joints = q_ctrl.shape[1] # Should be 3

    if rel_positions.shape[1] < num_joints:
         raise ValueError(f"rel_positions needs at least {num_joints} entries. Got shape {rel_positions.shape}")
    if joint_offsets.shape[1] < num_joints:
         raise ValueError(f"joint_offsets needs at least {num_joints} entries.")
    if num_joints != 3:
         raise ValueError(f"This Jacobian implementation requires exactly 3 joints, got {num_joints}")

    dx = rel_positions[:, :num_joints, 0] # Shape (N, 3)
    dz = rel_positions[:, :num_joints, 2] # Shape (N, 3)
    offsets = joint_offsets[:, :num_joints, 1] # Shape (N, 3)
    q_total = q_ctrl + offsets # Shape (N, 3)
    alpha_cumulative = torch.cumsum(q_total, dim=1) # Shape (N, 3)

    cos_alpha = torch.cos(alpha_cumulative) # Shape (N, 3)
    sin_alpha = torch.sin(alpha_cumulative) # Shape (N, 3)

    cos_a0 = cos_alpha[:, 0]
    sin_a0 = sin_alpha[:, 0]
    cos_a1 = cos_alpha[:, 1]
    sin_a1 = sin_alpha[:, 1]
    cos_a2 = cos_alpha[:, 2]
    sin_a2 = sin_alpha[:, 2]

    dx0, dx1, dx2 = dx[:, 0], dx[:, 1], dx[:, 2]
    dz0, dz1, dz2 = dz[:, 0], dz[:, 1], dz[:, 2]

    jacobian = torch.zeros((batch_size, 3, num_joints), dtype=dx.dtype, device=dx.device)

    jacobian[:, 0, 0] = (-dx0 * sin_a0 + dz0 * cos_a0) + (-dx1 * sin_a1 + dz1 * cos_a1) + (-dx2 * sin_a2 + dz2 * cos_a2)
    jacobian[:, 1, 0] = (-dx0 * cos_a0 - dz0 * sin_a0) + (-dx1 * cos_a1 - dz1 * sin_a1) + (-dx2 * cos_a2 - dz2 * sin_a2)
    jacobian[:, 2, 0] = 1.0

    jacobian[:, 0, 1] = (-dx1 * sin_a1 + dz1 * cos_a1) + (-dx2 * sin_a2 + dz2 * cos_a2)
    jacobian[:, 1, 1] = (-dx1 * cos_a1 - dz1 * sin_a1) + (-dx2 * cos_a2 - dz2 * sin_a2)
    jacobian[:, 2, 1] = 1.0

    jacobian[:, 0, 2] = (-dx2 * sin_a2 + dz2 * cos_a2)
    jacobian[:, 1, 2] = (-dx2 * cos_a2 - dz2 * sin_a2)
    jacobian[:, 2, 2] = 1.0

    return jacobian


# --- Finite Difference Test Function ---

def test_jacobian_with_offsets(rel_positions, q_ctrl_batch, offsets_batch, delta=1e-5, tolerance=1e-4):
    """
    Tests the analytical Jacobian against finite differences.
    Uses forward_kinematics_2D_batch_with_offsets for numerical differentiation.
    """
    print("\n--- Testing Jacobian Calculation (Finite Differences) ---")
    passed = True
    batch_size, num_joints = q_ctrl_batch.shape
    tensor_dtype = q_ctrl_batch.dtype # Get dtype from input

    # 1. Calculate Analytical Jacobian
    try:
        J_analytical = compute_jacobian_torch_with_offsets(rel_positions, q_ctrl_batch, offsets_batch)
    except Exception as e:
        print(f"ERROR calculating analytical Jacobian: {e}")
        return None, False # Cannot proceed

    # 2. Calculate Numerical Jacobian via Finite Differences
    J_numerical = torch.zeros_like(J_analytical)
    for j in range(num_joints):
        q_plus = q_ctrl_batch.clone()
        q_minus = q_ctrl_batch.clone()
        # Ensure delta has the correct dtype
        delta_tensor = torch.tensor(delta, dtype=tensor_dtype, device=q_ctrl_batch.device)
        q_plus[:, j] += delta_tensor
        q_minus[:, j] -= delta_tensor

        try:
            _, pose_plus = forward_kinematics_2D_batch_with_offsets(rel_positions, q_plus, offsets_batch)
            _, pose_minus = forward_kinematics_2D_batch_with_offsets(rel_positions, q_minus, offsets_batch)
        except Exception as e:
            print(f"ERROR during FK in finite difference for joint {j}: {e}")
            return J_analytical, False

        pose_diff = pose_plus - pose_minus
        angle_diff = pose_diff[:, 2]
        wrapped_angle_diff = torch.remainder(angle_diff + math.pi, 2 * math.pi) - math.pi
        pose_diff[:, 2] = wrapped_angle_diff

        J_numerical[:, :, j] = pose_diff / (2 * delta_tensor)

    # 3. Compare Analytical vs Numerical Jacobian
    if not torch.allclose(J_analytical, J_numerical, atol=tolerance, rtol=tolerance):
        passed = False
        print(f"Result: FAILED")
        diff = torch.abs(J_analytical - J_numerical)
        max_diff_val, max_diff_idx = torch.max(diff.view(-1), dim=0)
        fail_batch_idx = (max_diff_idx // (3 * num_joints)).item()
        print(f"Max difference {max_diff_val:.6f} found in batch index {fail_batch_idx}")
        print("Analytical Jacobian (example):\n", J_analytical[fail_batch_idx].cpu().numpy())
        print("Numerical Jacobian (example):\n", J_numerical[fail_batch_idx].cpu().numpy())
        print("Difference (example):\n", (J_analytical - J_numerical)[fail_batch_idx].cpu().numpy())
    else:
        print(f"Result: PASSED")

    return J_analytical, passed

# --- Inverse Jacobian Test Function ---

def test_inverse_jacobian(J_analytical, tolerance=1e-5):
    """Tests if J @ J_inv is close to the identity matrix."""
    print("\n--- Testing Inverse Jacobian Calculation (J @ J_inv = I) ---")
    batch_size = J_analytical.shape[0]
    identity = torch.eye(3, device=J_analytical.device, dtype=J_analytical.dtype).unsqueeze(0).repeat(batch_size, 1, 1)
    passed = True

    try:
        J_inv = torch.linalg.inv(J_analytical)
        identity_check = J_analytical @ J_inv

        if not torch.allclose(identity_check, identity, atol=tolerance, rtol=tolerance):
            passed = False
            print(f"Result: FAILED")
            diff = torch.abs(identity_check - identity)
            max_diff_val, max_diff_idx = torch.max(diff.view(-1), dim=0)
            fail_batch_idx = (max_diff_idx // 9).item()
            print(f"Max difference from Identity {max_diff_val:.6f} found in batch index {fail_batch_idx}")
            print("J @ J_inv (example):\n", identity_check[fail_batch_idx].cpu().numpy())
            print("Identity:\n", identity[fail_batch_idx].cpu().numpy())
            print("Difference (example):\n", (identity_check - identity)[fail_batch_idx].cpu().numpy())
        else:
            print(f"Result: PASSED")

    except torch.linalg.LinAlgError as e:
        print(f"ERROR during Inverse Jacobian calculation: {e}. Jacobian likely singular for some inputs.")
        try:
            dets = torch.linalg.det(J_analytical)
            singular_indices = torch.where(torch.abs(dets) < 1e-6)[0]
            if len(singular_indices) > 0:
                print(f"Jacobian determinant near zero for batch indices: {singular_indices.cpu().numpy()}")
                print(f"Determinants (sample): {dets[singular_indices[:5]].cpu().numpy()}")
        except Exception:
            pass
        passed = False

    return passed

# ========================================
#            MAIN TEST EXECUTION
# ========================================

# --- Test Setup ---

if torch.cuda.is_available():
    device = torch.device('cuda:0')
    print("Using device: CUDA")
else:
    device = torch.device('cpu')
    print("Using device: CPU")

# Define data type for precision test
# tensor_dtype = torch.float64  # <<< Option 1: Use float64
tensor_dtype = torch.float32  # <<< Option 2: Keep float32
print(f"Using dtype: {tensor_dtype}")

# Define link relative positions (Assumed J1->J2, J2->EE, EE->Tip)
rel_positions_single = torch.tensor([[[2.8400, 0.0000, 0.0000],   # J1->J2
                                      [3.5266, 0.0000, 0.3690],   # J2->EE
                                      [1.4180, 0.0000, 0.0530]]], # EE->Tip
                                    dtype=tensor_dtype, device=device)

# Define joint offsets (pitch only is used) - Corresponds to J1, J2, J3
offsets_single = torch.tensor([[[0.0000,  0.000, 0.0000],   # Offset J1
                                [0.0000, -0.024, 0.0000],   # Offset J2
                                [0.0000, -0.280, 0.0000]]], # Offset J3
                              dtype=tensor_dtype, device=device)

# Generate Batch Data
N = 50
rel_positions = rel_positions_single.repeat(N, 1, 1)
offsets = offsets_single.repeat(N, 1, 1)

# Define joint limits
q_limit_upper = torch.tensor([0.4, 2.76, 2.32], device=device, dtype=tensor_dtype)
q_limit_lower = torch.tensor([-1.29, 0.54, -0.59], device=device, dtype=tensor_dtype)

# Generate random CONTROLLABLE joint angles within limits
q_ctrl_test_batch = torch.rand(N, 3, device=device, dtype=tensor_dtype) * (q_limit_upper - q_limit_lower) + q_limit_lower
print(f"\nGenerated {N} sets of random controllable joint angles within limits.")

# --- Run FK/IK Consistency Test ---
print("\n--- Running Kinematics Consistency Test Batch WITH OFFSETS ---")
fk_ik_passed = True
tolerance_fk_ik = 1e-4 if tensor_dtype == torch.float32 else 1e-7 # Adjust tolerance based on dtype
# 1. FK
try:
    _, end_effector_fk_batch = forward_kinematics_2D_batch_with_offsets(rel_positions, q_ctrl_test_batch, offsets)
    x_target_batch = end_effector_fk_batch[:, 0:1]
    z_target_batch = end_effector_fk_batch[:, 1:2]
    alpha_target_batch = end_effector_fk_batch[:, 2:3]
except Exception as e:
    print(f"FK Error: {e}"); fk_ik_passed = False; exit()
# 2. IK
if fk_ik_passed:
    try:
        q1_ik_batch, q2_ik_batch, q3_ik_batch = inverse_kinematics_analytic_fixed_links_with_offsets(x_target_batch, z_target_batch, alpha_target_batch, rel_positions, offsets)
        q_ik_result_batch = torch.cat([q1_ik_batch, q2_ik_batch, q3_ik_batch], dim=1)
    except Exception as e:
        print(f"IK Error: {e}"); fk_ik_passed = False; exit()
# 3. Compare
if fk_ik_passed:
    angles_close_batch = compare_angles(q_ctrl_test_batch, q_ik_result_batch, tolerance_fk_ik)
    if not torch.all(angles_close_batch):
        fk_ik_passed = False
        print("FK/IK Consistency Test: FAILED (Stopping further tests)")
        fail_indices_fkik = torch.where(~torch.all(angles_close_batch, dim=1))[0]
        first_fail_idx_fkik = fail_indices_fkik[0].item()
        q_orig_fail_fkik = q_ctrl_test_batch[first_fail_idx_fkik]
        q_ik_fail_fkik = q_ik_result_batch[first_fail_idx_fkik]
        diff_fail_fkik = q_orig_fail_fkik - q_ik_fail_fkik
        diff_fail_wrapped_fkik = torch.remainder(diff_fail_fkik + math.pi, 2 * math.pi) - math.pi
        print(f"\n--- First Failing FK/IK Example (Index {first_fail_idx_fkik}) ---")
        print(f"Input Ctrl Angles:    {q_orig_fail_fkik.cpu().numpy()}")
        print(f"IK Result Ctrl Angles:{q_ik_fail_fkik.cpu().numpy()}")
        print(f"Wrapped Difference:   {diff_fail_wrapped_fkik.cpu().numpy()}")
    else:
        print("FK/IK Consistency Test: PASSED")

# --- Run Jacobian and Inverse Jacobian Tests (only if FK/IK passed) ---
jacobian_passed = False
inverse_jacobian_passed = False
if fk_ik_passed:
    # Test Jacobian using Finite Differences
    # Adjust delta and tolerance based on dtype
    fd_delta = 1e-2 if tensor_dtype == torch.float32 else 1e-7
    fd_tolerance = 1e-2 if tensor_dtype == torch.float32 else 1e-6

    print(f"\nRunning Jacobian test with delta={fd_delta}, tolerance={fd_tolerance}")
    jacobian_analytical, jacobian_passed = test_jacobian_with_offsets(
        rel_positions, q_ctrl_test_batch, offsets,
        delta=fd_delta,
        tolerance=fd_tolerance
    )

    # Test Inverse Jacobian (only if Jacobian calculation seems correct)
    if jacobian_passed and jacobian_analytical is not None:
        inv_tolerance = 1e-5 if tensor_dtype == torch.float32 else 1e-8
        print(f"\nRunning Inverse Jacobian test with tolerance={inv_tolerance}")
        inverse_jacobian_passed = test_inverse_jacobian(jacobian_analytical, tolerance=inv_tolerance)

# --- Overall Test Summary ---
print("\n--- Overall Test Summary ---")
print(f"Using dtype: {tensor_dtype}")
print(f"FK/IK Consistency: {'PASSED' if fk_ik_passed else 'FAILED'}")
if fk_ik_passed: # Only report downstream tests if FK/IK worked
    print(f"Jacobian vs Finite Diff: {'PASSED' if jacobian_passed else 'FAILED'}")
    if jacobian_passed:
        print(f"Inverse Jacobian (J @ J_inv = I): {'PASSED' if inverse_jacobian_passed else 'FAILED'}")
    else:
         print(f"Inverse Jacobian (J @ J_inv = I): SKIPPED (Jacobian test failed)")
else:
    print(f"Jacobian vs Finite Diff: SKIPPED (FK/IK test failed)")
    print(f"Inverse Jacobian (J @ J_inv = I): SKIPPED (FK/IK test failed)")
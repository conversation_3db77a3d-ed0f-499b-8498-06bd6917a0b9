# data_aggregation.py

import os
import pickle
import yaml
import pandas as pd

def collect_data(root_dir):
    """
    Collects geometry and torque limit data from robot models.

    Args:
        root_dir (str): Root directory containing robot model folders.

    Returns:
        df_geometry (DataFrame): Aggregated geometry data.
        df_torque (DataFrame): Aggregated torque limits data.
    """
    geometry_data = []
    torque_limits_data = []

    for subdir in os.listdir(root_dir):
        subdir_path = os.path.join(root_dir, subdir)
        if os.path.isdir(subdir_path):
            transform_file = os.path.join(subdir_path, 'transformations_and_limits.pickle')
            limits_file = os.path.join(subdir_path, 'limits', 'torque_limits.yaml')
            
            # Extract geometry data
            try:
                with open(transform_file, 'rb') as f:
                    transformations = pickle.load(f)
                
                for joint, data in transformations.items():
                    if joint != 'w_P_wb':
                        geometry_entry = {
                            'model_id': subdir,
                            'joint': joint,
                            'xyz_x': data['xyz'][0],
                            'xyz_y': data['xyz'][1],
                            'xyz_z': data['xyz'][2],
                            'rpy_roll': data['rpy'][0],
                            'rpy_pitch': data['rpy'][1],
                            'rpy_yaw': data['rpy'][2],
                            'effort': data.get('effort'),
                            'min_val': data.get('min_val'),
                            'max_val': data.get('max_val')
                        }
                        geometry_data.append(geometry_entry)
            except Exception as e:
                print(f"Error reading {transform_file}: {e}")
            
            # Extract torque limits data
            try:
                with open(limits_file, 'r') as f:
                    limits = yaml.safe_load(f)
                
                for joint, limit_data in limits.items():
                    torque_entry = {
                        'model_id': subdir,
                        'joint': joint,
                        'degree': limit_data.get('degree'),
                        'max_limit': limit_data.get('max'),
                        'min_limit': limit_data.get('min')
                    }
                    torque_limits_data.append(torque_entry)
            except Exception as e:
                print(f"Error reading {limits_file}: {e}")

    # Convert lists to DataFrames
    df_geometry = pd.DataFrame(geometry_data)
    df_torque = pd.DataFrame(torque_limits_data)

    return df_geometry, df_torque

def save_dataframes(df_geometry, df_torque, output_dir):
    """
    Saves DataFrames to CSV files in the specified directory.

    Args:
        df_geometry (DataFrame): Geometry data.
        df_torque (DataFrame): Torque limits data.
        output_dir (str): Directory to save CSV files.
    """
    geometry_csv = os.path.join(output_dir, 'geometry_data.csv')
    torque_csv = os.path.join(output_dir, 'torque_limits_data.csv')
    df_geometry.to_csv(geometry_csv, index=False)
    df_torque.to_csv(torque_csv, index=False)
    print(f"DataFrames saved to {geometry_csv} and {torque_csv}")


if __name__ == '__main__':
    ROOT_DIR = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_assets/data/Robots/Gravis/sampled_usds_100_filtered'  # Root directory (current directory)
    OUTPUT_DIR = '.'  # Output directory (same as script)
    df_geometry, df_torque = collect_data(ROOT_DIR)
    save_dataframes(df_geometry, df_torque, OUTPUT_DIR)

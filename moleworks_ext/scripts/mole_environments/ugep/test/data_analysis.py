# data_analysis.py

import pandas as pd
import os

def analyze_geometry(df_geometry):
    """
    Computes aggregated statistics for geometry data per joint.

    Args:
        df_geometry (DataFrame): Geometry data.

    Returns:
        geometry_stats (DataFrame): Aggregated geometry statistics.
    """
    geometry_stats = df_geometry.groupby('joint').agg({
        'xyz_x': ['mean', 'std', 'min', 'max'],
        'xyz_y': ['mean', 'std', 'min', 'max'],
        'xyz_z': ['mean', 'std', 'min', 'max'],
        'rpy_roll': ['mean', 'std', 'min', 'max'],
        'rpy_pitch': ['mean', 'std', 'min', 'max'],
        'rpy_yaw': ['mean', 'std', 'min', 'max'],
        'effort': ['mean', 'std', 'min', 'max'],
        'min_val': ['mean', 'std', 'min', 'max'],
        'max_val': ['mean', 'std', 'min', 'max']
    })
    return geometry_stats

def analyze_torque(df_torque):
    """
    Computes aggregated statistics for torque limit data per joint.

    Args:
        df_torque (DataFrame): Torque limits data.

    Returns:
        torque_stats (DataFrame): Aggregated torque statistics.
    """
    torque_stats = df_torque.groupby('joint').agg({
        'degree': ['mean', 'std', 'min', 'max'],
        'max_limit': ['mean', 'std', 'min', 'max'],
        'min_limit': ['mean', 'std', 'min', 'max']
    })
    return torque_stats

def save_stats(geometry_stats, torque_stats, output_dir):
    """
    Saves aggregated statistics to CSV files.

    Args:
        geometry_stats (DataFrame): Geometry statistics.
        torque_stats (DataFrame): Torque statistics.
        output_dir (str): Directory to save CSV files.
    """
    geometry_stats_file = os.path.join(output_dir, 'geometry_stats.csv')
    torque_stats_file = os.path.join(output_dir, 'torque_stats.csv')
    geometry_stats.to_csv(geometry_stats_file)
    torque_stats.to_csv(torque_stats_file)
    print(f"Statistics saved to {geometry_stats_file} and {torque_stats_file}")

if __name__ == '__main__':
    OUTPUT_DIR = '.'
    df_geometry = pd.read_csv(os.path.join(OUTPUT_DIR, 'geometry_data.csv'))
    df_torque = pd.read_csv(os.path.join(OUTPUT_DIR, 'torque_limits_data.csv'))
    geometry_stats = analyze_geometry(df_geometry)
    torque_stats = analyze_torque(df_torque)
    save_stats(geometry_stats, torque_stats, OUTPUT_DIR)

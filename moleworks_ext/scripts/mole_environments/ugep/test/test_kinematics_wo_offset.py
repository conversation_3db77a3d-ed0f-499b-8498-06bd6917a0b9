import torch
from typing import <PERSON><PERSON>
import math
import numpy as np # For comparing angles robustly

# --- Original Kinematic Functions (Before Offset Modifications) ---

def forward_kinematics_2D_batch(
    rel_positions_fk: torch.Tensor, # Input: [J1->J2, J2->EE, EE->Tip]
    joint_angles: torch.Tensor      # Input: [q1, q2, q3]
) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    Perform forward kinematics for 2D batch data.
    Assumes rel_positions_fk[i] is the vector rotated by joint_angles[i].
    """
    batch_size, num_segments, _ = rel_positions_fk.shape
    num_joints = joint_angles.shape[1]

    if num_segments != num_joints:
        raise ValueError(f"Mismatch between rel_positions_fk segments ({num_segments}) and joint angles ({num_joints})")

    joint_positions = torch.zeros(batch_size, num_joints + 1, 2, device=rel_positions_fk.device, dtype=rel_positions_fk.dtype)
    alpha = torch.zeros((batch_size, 1), device=rel_positions_fk.device, dtype=rel_positions_fk.dtype)
    x = torch.zeros((batch_size, 1), device=rel_positions_fk.device, dtype=rel_positions_fk.dtype)
    z = torch.zeros((batch_size, 1), device=rel_positions_fk.device, dtype=rel_positions_fk.dtype)

    for i in range(num_joints):
        alpha += joint_angles[:, i:i+1]
        dx = rel_positions_fk[:, i, 0:1]
        dz = rel_positions_fk[:, i, 2:3]
        # Original rotation convention
        new_dx = dx * torch.cos(alpha) + dz * torch.sin(alpha)
        new_dz = - dx * torch.sin(alpha) + dz * torch.cos(alpha)
        x += new_dx
        z += new_dz
        joint_positions[:, i+1, 0] = x.squeeze(1)
        joint_positions[:, i+1, 1] = z.squeeze(1)

    alpha = torch.remainder(alpha + math.pi, 2 * math.pi) - math.pi
    end_effector = torch.cat([x, z, alpha], dim=1)
    return joint_positions, end_effector


def inverse_kinematics_analytic_fixed_links(
    x: torch.Tensor,
    z: torch.Tensor,
    alpha: torch.Tensor,
    rel_positions_ik: torch.Tensor # Input: [Base->J1, J1->J2, J2->EE]
) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
    """
    Compute the inverse kinematics analytically for fixed link robot arm.
    Assumes rel_positions_ik = [Base->J1, J1->J2, J2->EE]
    """
    x = x.view(-1, 1)
    z = z.view(-1, 1)
    alpha = alpha.view(-1, 1)
    num_joints_expected = 3

    if rel_positions_ik.shape[1] < num_joints_expected:
         raise ValueError(f"rel_positions_ik needs at least {num_joints_expected} entries. Got shape {rel_positions_ik.shape}")

    l0 = torch.norm(rel_positions_ik[:, 0, [0, 2]], dim=1, keepdim=True)
    l1 = torch.norm(rel_positions_ik[:, 1, [0, 2]], dim=1, keepdim=True)
    l2 = torch.norm(rel_positions_ik[:, 2, [0, 2]], dim=1, keepdim=True)
    l0 = torch.where(l0 == 0, torch.full_like(l0, 1e-9), l0)
    l1 = torch.where(l1 == 0, torch.full_like(l1, 1e-9), l1)

    theta_2 = torch.atan2(rel_positions_ik[:, 1, 2:3], rel_positions_ik[:, 1, 0:1])
    theta_end = torch.atan2(rel_positions_ik[:, 2, 2:3], rel_positions_ik[:, 2, 0:1])

    alpha_end = alpha - theta_end
    p2x = x - l2 * torch.cos(alpha_end)
    p2z = z + l2 * torch.sin(alpha_end) # Original sign convention

    cos_q2_arg = (p2x**2 + p2z**2 - l0**2 - l1**2) / (2 * l0 * l1 + 1e-9)
    cos_q2_arg = torch.clamp(cos_q2_arg, -1.0, 1.0)

    # Original calculation sequence
    q2_calc_for_q1 = torch.acos(cos_q2_arg)
    q1 = -torch.atan2(p2z, p2x) - torch.atan2(l1 * torch.sin(q2_calc_for_q1), l0 + l1 * torch.cos(q2_calc_for_q1))
    q2 = q2_calc_for_q1 + theta_2
    q3 = alpha - q1 - q2

    q1 = torch.remainder(q1 + math.pi, 2 * math.pi) - math.pi
    q2 = torch.remainder(q2 + math.pi, 2 * math.pi) - math.pi
    q3 = torch.remainder(q3 + math.pi, 2 * math.pi) - math.pi

    return q1, q2, q3


# --- Test Setup ---

# Try to use CUDA if available, otherwise CPU
if torch.cuda.is_available():
    device = torch.device('cuda:0')
    print("Using device: CUDA")
else:
    device = torch.device('cpu')
    print("Using device: CPU")

# Define link relative positions AS PROVIDED BY USER (Assumed J1->J2, J2->EE, EE->Tip)
# This goes into FK
rel_positions_fk_input_single = torch.tensor([[[2.8400, 0.0000, 0.0000],   # Link 1 (J1->J2)
                                               [3.5266, 0.0000, 0.3690],   # Link 2 (J2->EE)
                                               [1.4180, 0.0000, 0.0530]]], # Link 3 (EE->Tip?) - Rotated by q3
                                             dtype=torch.float32, device=device)

# Construct the rel_positions needed for IK: [Base->J1, J1->J2, J2->EE]
# Assume Base->J1 is [0, 0, 0]
# base_to_j1_single = torch.tensor([[[0.0, 0.0, 0.0]]], dtype=torch.float32, device=device)
# Take the first two links from the user input for J1->J2, J2->EE
# rel_positions_ik_input_single = torch.cat((base_to_j1_single, rel_positions_fk_input_single[:, :2, :]), dim=1)
rel_positions_ik_input_single = rel_positions_fk_input_single

# Generate Batch Data
N = 10  # Batch size
rel_positions_fk_input = rel_positions_fk_input_single.repeat(N, 1, 1)
rel_positions_ik_input = rel_positions_ik_input_single.repeat(N, 1, 1)

# Define joint limits
q_limit_upper = torch.tensor([0.4, 2.76, 2.32], device=device)
q_limit_lower = torch.tensor([-1.29, 0.54, -0.59], device=device)

# Generate random joint angles within limits
q_test_batch = torch.rand(N, 3, device=device) * (q_limit_upper - q_limit_lower) + q_limit_lower
print(f"\nGenerated {N} sets of random joint angles within limits.")
# print("Sample Input Angles (q1, q2, q3):\n", q_test_batch.cpu().numpy())


# Function to compare angles robustly (handles wrapping)
def compare_angles(a1, a2, tol):
    diff = a1 - a2
    diff = torch.remainder(diff + math.pi, 2 * math.pi) - math.pi
    return torch.abs(diff) < tol

# --- Run Test on Batch ---
print("\n--- Running ORIGINAL Kinematics Consistency Test Batch (NO OFFSETS) ---")
all_passed = True
tolerance = 1e-4 # Tolerance for comparison

# 1. Forward Kinematics (Batch)
try:
    # Use FK links (J1->J2, J2->EE, EE->Tip)
    _, end_effector_fk_batch = forward_kinematics_2D_batch(
        rel_positions_fk_input, q_test_batch
    )
    x_target_batch = end_effector_fk_batch[:, 0:1]
    z_target_batch = end_effector_fk_batch[:, 1:2]
    alpha_target_batch = end_effector_fk_batch[:, 2:3] # FK returns angle wrapped to [-pi, pi]
    # print("\nSample FK Results (x, z, alpha):\n", end_effector_fk_batch.cpu().numpy())

except Exception as e:
    print(f"\nERROR during Forward Kinematics Batch: {e}")
    all_passed = False
    # Exit or prevent further execution if FK fails catastrophically
    exit()


# 2. Inverse Kinematics (Batch)
try:
    # Use IK links (Base->J1, J1->J2, J2->EE)
    q1_ik_batch, q2_ik_batch, q3_ik_batch = inverse_kinematics_analytic_fixed_links(
        x_target_batch, z_target_batch, alpha_target_batch, rel_positions_ik_input
    )
    q_ik_result_batch = torch.cat([q1_ik_batch, q2_ik_batch, q3_ik_batch], dim=1)
    # IK function wraps angles to [-pi, pi]
    # print("\nSample IK Results (q1, q2, q3):\n", q_ik_result_batch.cpu().numpy())

except Exception as e:
    print(f"\nERROR during Inverse Kinematics Batch: {e}")
    all_passed = False
     # Exit or prevent further execution if IK fails catastrophically
    exit()

# 3. Compare (Batch)
# Use the robust angle comparison function element-wise
angles_close_batch = compare_angles(q_test_batch, q_ik_result_batch, tolerance)

# Check if ALL angles in ALL batches passed
if torch.all(angles_close_batch):
    print(f"\nResult: PASSED (All {N} tests)")
else:
    all_passed = False
    print(f"\nResult: FAILED")
    # Find the first failing example for detailed output
    fail_indices = torch.where(~torch.all(angles_close_batch, dim=1))[0]
    first_fail_idx = fail_indices[0].item()

    q_orig_fail = q_test_batch[first_fail_idx]
    q_ik_fail = q_ik_result_batch[first_fail_idx]
    diff_fail = q_orig_fail - q_ik_fail
    diff_fail_wrapped = torch.remainder(diff_fail + math.pi, 2 * math.pi) - math.pi

    print(f"\n--- First Failing Example (Index {first_fail_idx}) ---")
    print(f"Input Angles:         {q_orig_fail.cpu().numpy()}")
    print(f"FK Pose (x, z, alpha): {end_effector_fk_batch[first_fail_idx].cpu().numpy()}")
    print(f"IK Result Angles:     {q_ik_fail.cpu().numpy()}")
    print(f"Wrapped Difference:   {diff_fail_wrapped.cpu().numpy()}")
    # Calculate max difference across the failing example
    max_diff_fail = torch.max(torch.abs(diff_fail_wrapped)).item()
    print(f"Max Abs Wrapped Diff: {max_diff_fail:.6f}")

print("\n--- Test Summary ---")
if all_passed:
    print("Original functions test with random angles: All test cases passed!")
else:
    print("Original functions test with random angles: Some test cases failed.")
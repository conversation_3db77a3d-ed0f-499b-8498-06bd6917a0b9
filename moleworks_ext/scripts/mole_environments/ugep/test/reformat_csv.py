import pandas as pd
import pickle

# Load the CSV file
# Load the data
file_path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/merged_excavator_info_processed.csv'
df = pd.read_csv(file_path)

# Optional: Perform any restructuring if needed
# For example, you might want to drop the 'Unnamed: 0' column if it's just an index
df = df.drop(columns=['Unnamed: 0'])

# Save the DataFrame to a binary format
binary_file = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/merged_excavator_info_processed.pkl'
with open(binary_file, 'wb') as f:
    pickle.dump(df, f, protocol=pickle.HIGHEST_PROTOCOL)

print(f"Data saved to {binary_file} in binary format.")

import os
import json
import argparse
import shutil
from pathlib import Path

def parse_arguments():
    parser = argparse.ArgumentParser(
        description="Remove numerical folders where x_max in model_coefficients.json is below a threshold."
    )
    parser.add_argument(
        "base_folder",
        type=str,
        help="Path to the base folder containing numerical subfolders."
    )
    parser.add_argument(
        "--threshold",
        type=float,
        default=7.0,
        help="Threshold value for x_max. Folders with x_max below this will be removed. Default is 7."
    )
    parser.add_argument(
        "--dry-run",
        action='store_true',
        help="Perform a dry run without deleting any folders. Useful for testing."
    )
    return parser.parse_args()

def is_numerical_folder(folder_name):
    return folder_name.isdigit()

def get_x_max(json_path):
    try:
        with open(json_path, 'r') as file:
            data = json.load(file)
            x_max = data.get("x_max")
            if isinstance(x_max, (int, float)):
                return x_max
            else:
                print(f"Warning: 'x_max' is not a number in {json_path}. Skipping this folder.")
                return None
    except (json.JSONDecodeError, FileNotFoundError) as e:
        print(f"Error reading {json_path}: {e}. Skipping this folder.")
        return None

def remove_folder(folder_path, dry_run=False):
    if dry_run:
        print(f"[Dry Run] Would remove folder: {folder_path}")
    else:
        try:
            shutil.rmtree(folder_path)
            print(f"Removed folder: {folder_path}")
        except Exception as e:
            print(f"Error removing {folder_path}: {e}")

def main():
    args = parse_arguments()
    base_folder = Path(args.base_folder)

    if not base_folder.exists() or not base_folder.is_dir():
        print(f"Error: The base folder '{base_folder}' does not exist or is not a directory.")
        return

    folders = [f for f in base_folder.iterdir() if f.is_dir() and is_numerical_folder(f.name)]

    if not folders:
        print("No numerical folders found in the base directory.")
        return

    removed_count = 0
    for folder in folders:
        json_path = folder / "model_coefficients.json"
        x_max = get_x_max(json_path)
        if x_max is not None and x_max < args.threshold:
            remove_folder(folder, dry_run=args.dry_run)
            removed_count += 1

    print(f"\nProcess completed. Total folders {'would be removed' if args.dry_run else 'removed'}: {removed_count}")

if __name__ == "__main__":
    main()

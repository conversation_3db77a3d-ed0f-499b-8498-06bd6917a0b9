import torch
from typing import <PERSON><PERSON>
# fix the seed
torch.manual_seed(42)

def forward_kinematics_2D_batch(links: torch.Tensor, joint_angles: torch.Tensor) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:
    """
    Perform forward kinematics for 2D batch data. The links have size (N, 3) and the joint angles have size (N, 3)
    """
    batch_size, num_joints, _ = links.shape
    
    # Initialize joint_positions tensor with the origin (0, 0)
    joint_positions = torch.zeros(batch_size, num_joints + 1, 2, device=links.device)

    # Initialize orientation alpha and global position to zeros
    alpha = torch.zeros((batch_size, 1), device=links.device)
    x, z = torch.zeros((batch_size, 1), device=links.device), torch.zeros((batch_size, 1), device=links.device)
    
    for i in range(num_joints):
        # non fixed joints
        alpha += joint_angles[:, i:i+1]  # Slice to keep dimensions
            
        # Rotate the relative vector by the current orientation alpha
        dx, dz = links[:, i, 0:1], links[:, i, 2:3]  # Slice to keep dimensions
        
        new_dx = dx * torch.cos(alpha) + dz * torch.sin(alpha)
        new_dz = - dx * torch.sin(alpha) + dz * torch.cos(alpha)

        # Update global position
        x += new_dx
        z += new_dz

        joint_positions[:, i+1, 0] = x.squeeze(1)
        joint_positions[:, i+1, 1] = z.squeeze(1)

    end_effector = torch.cat([x, z, alpha], dim=1)
    return joint_positions, end_effector


def compute_jacobian_torch_old(dx, dz, q):
    """
    Compute the Jacobian matrix based on given dx, dz, and q tensors.
    
    Args:
    - dx (Tensor): Tensor of shape (N, 5) representing dx values.
    - dz (Tensor): Tensor of shape (N, 5) representing dz values.
    - q (Tensor): Tensor of shape (N, 3) representing joint angles q0, q1, and q3.
    
    Returns:
    - Tensor: The Jacobian matrix of shape (N, 3, 3).
    """
    # Precompute sin and cos for efficiency
    q0 = q[:, 0].unsqueeze(1)  # Shape (N, 1)
    q1 = q[:, 1].unsqueeze(1)  # Shape (N, 1)
    q2 = q[:, 2].unsqueeze(1)  # Shape (N, 1)
    
    # Define variables
    sin_q0 = torch.sin(q0).squeeze()
    cos_q0 = torch.cos(q0).squeeze()
    sin_q0_q1 = torch.sin(q0 + q1).squeeze()
    cos_q0_q1 = torch.cos(q0 + q1).squeeze()
    sin_q0_q1_q2 = torch.sin(q0 + q1 + q2).squeeze()
    cos_q0_q1_q2 = torch.cos(q0 + q1 + q2).squeeze()
    
    # Compute the Jacobian components
    jacobian = torch.zeros((dx.shape[0], 3, 3), dtype=dx.dtype, device=dx.device)

    # Define the elements of the matrix
    jacobian[:, 0, 0] = -dx[:, 0] * sin_q0 - dx[:, 1] * sin_q0_q1 - dx[:, 2] * sin_q0_q1_q2 + dz[:, 0] * cos_q0 + dz[:, 1] * cos_q0_q1 + dz[:, 2] * cos_q0_q1_q2
    jacobian[:, 0, 1] = -dx[:, 1] * sin_q0_q1 - dx[:, 2] * sin_q0_q1_q2 + dz[:, 1] * cos_q0_q1 + dz[:, 2] * cos_q0_q1_q2
    jacobian[:, 0, 2] = -dx[:, 2] * sin_q0_q1_q2 + dz[:, 2] * cos_q0_q1_q2
    jacobian[:, 1, 0] = -dx[:, 0] * cos_q0 - dx[:, 1] * cos_q0_q1 - dx[:, 2] * cos_q0_q1_q2 - dz[:, 0] * sin_q0 - dz[:, 1] * sin_q0_q1 - dz[:, 2] * sin_q0_q1_q2
    jacobian[:, 1, 1] = -dx[:, 1] * cos_q0_q1 - dx[:, 2] * cos_q0_q1_q2 - dz[:, 1] * sin_q0_q1 - dz[:, 2] * sin_q0_q1_q2
    jacobian[:, 1, 2] = -dx[:, 2] * cos_q0_q1_q2 - dz[:, 2] * sin_q0_q1_q2
    jacobian[:, 2, :] = torch.tensor([1, 1, 1], dtype=dx.dtype, device=dx.device)
    return jacobian


def compute_jacobian_torch(dx, dz, q):
    """
    Compute the Jacobian matrix based on given dx, dz, and q tensors.
    
    Args:
    - dx (Tensor): Tensor of shape (N, 5) representing dx values.
    - dz (Tensor): Tensor of shape (N, 5) representing dz values.
    - q (Tensor): Tensor of shape (N, 3) representing joint angles q0, q1, and q2.
    
    Returns:
    - Tensor: The Jacobian matrix of shape (N, 3, 3).
    """
    N = q.shape[0]
    q0 = q[:, 0]
    q1 = q[:, 1]
    q2 = q[:, 2]

    # Precompute sin and cos values
    sin_q0 = torch.sin(q0)
    cos_q0 = torch.cos(q0)
    sin_q0_q1 = torch.sin(q0 + q1)
    cos_q0_q1 = torch.cos(q0 + q1)
    sin_q0_q1_q2 = torch.sin(q0 + q1 + q2)
    cos_q0_q1_q2 = torch.cos(q0 + q1 + q2)

    # Initialize Jacobian matrix
    jacobian = torch.zeros((N, 3, 3), device=q.device, dtype=q.dtype)

    # Fill Jacobian matrix entries
    jacobian[:, 0, 0] = -sin_q0 * dx[:, 0] - sin_q0_q1 * dx[:, 1] - sin_q0_q1_q2 * dx[:, 2]
    jacobian[:, 0, 1] = -sin_q0_q1 * dx[:, 1] - sin_q0_q1_q2 * dx[:, 2]
    jacobian[:, 0, 2] = -sin_q0_q1_q2 * dx[:, 2]

    jacobian[:, 1, 0] = cos_q0 * dz[:, 0] + cos_q0_q1 * dz[:, 1] + cos_q0_q1_q2 * dz[:, 2]
    jacobian[:, 1, 1] = cos_q0_q1 * dz[:, 1] + cos_q0_q1_q2 * dz[:, 2]
    jacobian[:, 1, 2] = cos_q0_q1_q2 * dz[:, 2]

    jacobian[:, 2, 0] = 1
    jacobian[:, 2, 1] = 1
    jacobian[:, 2, 2] = 1

    return jacobian


def finite_difference_test(dx, dz, q, epsilon=1e-5):
    """
    Test the Jacobian function using finite difference approximation.
    
    Args:
    - dx, dz (Tensor): Tensors representing the relative positions dx and dz.
    - q (Tensor): Tensor of joint angles.
    - epsilon (float): Small perturbation for finite differences.
    
    Returns:
    - bool: Whether the Jacobian is correctly implemented.
    """
    # Compute the Jacobian using the provided function
    jac_estimated = compute_jacobian_torch_old(dx, dz, q)
    
    # Initialize approximate Jacobian using finite differences
    jac_approx = torch.zeros_like(jac_estimated)
    
    for i in range(q.shape[1]):  # For each joint angle
        q_perturbed = q.clone()
        q_perturbed[:, i] += epsilon  # Perturb each joint angle slightly
        
        # Recompute positions and orientations after perturbation
        positions_perturbed, ee_perturbed = forward_kinematics_2D_batch(torch.stack([dx, torch.zeros_like(dx), dz], dim=-1), q_perturbed)
        positions_original, ee_original = forward_kinematics_2D_batch(torch.stack([dx, torch.zeros_like(dx), dz], dim=-1), q)
        
        # Compute finite differences
        jac_approx[:, :, i] = (ee_perturbed - ee_original) / epsilon
    
    # Compare the estimated Jacobian to the approximate Jacobian
    diff = torch.abs(jac_estimated - jac_approx)
    correct = torch.all(diff < 1e-4)
    if correct:
        print("Jacobian implementation is correct.")
    else:
        print("Jacobian implementation may be incorrect.")
        print("Max difference:", torch.max(diff).item())
    
    return correct

# Define the dx, dz, q for testing
N = 10  # Batch size
links = torch.tensor([[[2.8400, 0.0000, 0.0000],
         [3.5266, 0.0000, 0.3690],
         [1.4180, 0.0000, 0.0530]]], device='cuda:0')
batched_links = links.repeat(N, 1, 1)
q_limit_upper = torch.tensor([0.4, 2.76, 2.32], device='cuda:0')
q_limit_lower = torch.tensor([-1.29, 0.54, -0.59], device='cuda:0')
# sample within limits 
q = torch.rand(N, 3, device='cuda:0') * (q_limit_upper - q_limit_lower) + q_limit_lower
print("q: ", q)

# Run the finite difference test
finite_difference_test(batched_links[:, 0], batched_links[:, 2], q)
import os
import pickle


def process_transformation_file(file_path):
    with open(file_path, "rb") as f:
        transformations = pickle.load(f)
    print(f"Processing: {file_path}")

    if 'w_P_wb' not in transformations:
        transformations['w_P_wb'] = {'rpy': [0.0, 0.0, 0.0], 'xyz': [0.0, 0.0, 0.0]}
        print("Added w_P_wb with default values")
    
    print(f"Before: {transformations['w_P_wb']['xyz']}")
    # 0.704
    transformations['w_P_wb']['xyz'] = [0.0, 0.0, 0.01]

    print(f"After: {transformations['w_P_wb']['xyz']}")

    os.rename(file_path, file_path.replace(".pickle", "_old.pickle"))
    with open(file_path, "wb") as f:
        pickle.dump(transformations, f)

def read_transform(file_path):
    with open(file_path, "rb") as f:
        transformations = pickle.load(f)
    
    print(f"Transform: {transformations['w_P_wb']['xyz']}")


def read_all_transforms(file_path):
    with open(file_path, "rb") as f:
        transformations = pickle.load(f)
    
    for key, value in transformations.items():
        print(f"{key}: {value}")


def recursive_process(file_path, function):
    if os.path.exists(file_path):
        function(file_path)


def overwrite_entry(file_path, key, new_entry):
    with open(file_path, "rb") as f:
        transformations = pickle.load(f)
    
    transformations[key] = new_entry
    
    os.rename(file_path, file_path.replace(".pickle", "_old.pickle"))
    with open(file_path, "wb") as f:
        pickle.dump(transformations, f)


# Set the file path
file_path = "/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_assets/data/Robots/Gravis/sampled_usds_10_new/0/transformations_and_limits.pickle"

file_path_2 = "/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_assets/data/Robots/Gravis/cat_and_m545_visual/1/transformations_and_limits.pickle"

# Run the recursive processing
recursive_process(file_path, read_all_transforms)
recursive_process(file_path_2, read_all_transforms)
# overwrite with J_ENDEFFECTOR_CONTACT:  {'xyz': [1.7983918346113832, 0.0, -0.3446470601104926], 'rpy': [0.0, -0.42359171, 0.0], 'effort': 0.0, 'min_val': 0.0, 'max_val': 0.0}
# overwrite_entry(file_path, "J_ENDEFFECTOR_CONTACT", {'xyz': [1.7983918346113832, 0.0, -0.3446470601104926], 'rpy': [0.0, -0.42359171, 0.0], 'effort': 0.0, 'min_val': 0.0, 'max_val': 0.0})

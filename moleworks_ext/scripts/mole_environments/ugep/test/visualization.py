# visualization.py

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
import plotly.express as px

def visualize_geometry(df_geometry, output_dir):
    """
    Generates per-joint distribution plots for geometry data.
    Creates separate histograms for each variable to avoid multivariate KDE issues.
    
    Args:
        df_geometry (DataFrame): Geometry data.
        output_dir (str): Directory to save plots.
    """
    joints = df_geometry['joint'].unique()
    for joint in joints:
        joint_data = df_geometry[df_geometry['joint'] == joint]
        
        # Check if there is enough data to plot
        if joint_data.empty:
            print(f"No data available for joint: {joint}. Skipping plots.")
            continue
        
        # XYZ Distribution - Individual Plots
        for axis in ['xyz_x', 'xyz_y', 'xyz_z']:
            plt.figure(figsize=(8, 6))
            sns.histplot(joint_data[axis], bins=30, kde=True)
            plt.title(f'Distribution of {axis} for {joint}')
            plt.xlabel(axis)
            plt.ylabel('Frequency')
            vis_dir = os.path.join(output_dir, 'vis')
            os.makedirs(vis_dir, exist_ok=True)
            filename = os.path.join(vis_dir, f'{joint}_{axis}_distribution.png')
            plt.savefig(filename)
            plt.close()
        
        # RPY Distribution - Individual Plots
        for angle in ['rpy_roll', 'rpy_pitch', 'rpy_yaw']:
            plt.figure(figsize=(8, 6))
            sns.histplot(joint_data[angle], bins=30, kde=True)
            plt.title(f'Distribution of {angle} for {joint}')
            plt.xlabel(angle)
            plt.ylabel('Frequency')
            vis_dir = os.path.join(output_dir, 'vis')
            os.makedirs(vis_dir, exist_ok=True)
            filename = os.path.join(vis_dir, f'{joint}_{angle}_distribution.png')
            plt.savefig(filename)
            plt.close()

def visualize_torque(df_torque, output_dir):
    """
    Generates per-joint distribution plots for torque limits.
    Creates separate histograms for max and min torque limits.
    
    Args:
        df_torque (DataFrame): Torque limits data.
        output_dir (str): Directory to save plots.
    """
    joints = df_torque['joint'].unique()
    for joint in joints:
        joint_data = df_torque[df_torque['joint'] == joint]
        
        if joint_data.empty:
            print(f"No torque data available for joint: {joint}. Skipping plots.")
            continue
        
        # Max Torque Limits
        plt.figure(figsize=(8, 6))
        sns.histplot(joint_data['max_limit'], bins=30, kde=True, color='green')
        plt.title(f'Distribution of Max Torque Limits for {joint}')
        plt.xlabel('Max Torque Limit')
        plt.ylabel('Frequency')
        vis_dir = os.path.join(output_dir, 'vis')
        os.makedirs(vis_dir, exist_ok=True)
        filename = os.path.join(vis_dir, f'{joint}_max_torque_limits_distribution.png')
        plt.savefig(filename)
        plt.close()
        
        # Min Torque Limits
        plt.figure(figsize=(8, 6))
        sns.histplot(joint_data['min_limit'], bins=30, kde=True, color='red')
        plt.title(f'Distribution of Min Torque Limits for {joint}')
        plt.xlabel('Min Torque Limit')
        plt.ylabel('Frequency')
        vis_dir = os.path.join(output_dir, 'vis')
        os.makedirs(vis_dir, exist_ok=True)
        filename = os.path.join(vis_dir, f'{joint}_min_torque_limits_distribution.png')
        plt.savefig(filename)
        plt.close()

def perform_pca(df_geometry, output_dir):
    """
    Performs PCA on geometry data and saves a 3D scatter plot.
    
    Args:
        df_geometry (DataFrame): Geometry data.
        output_dir (str): Directory to save plot.
    """
    features = ['xyz_x', 'xyz_y', 'xyz_z', 'rpy_roll', 'rpy_pitch', 'rpy_yaw']
    # Drop rows with missing values
    df_pca_input = df_geometry[features].dropna()
    
    if df_pca_input.empty:
        print("No data available for PCA. Skipping PCA plot.")
        return
    
    x = df_pca_input.values
    pca = PCA(n_components=3)
    principal_components = pca.fit_transform(x)
    df_pca = pd.DataFrame(data=principal_components, columns=['PC1', 'PC2', 'PC3'])
    df_pca['joint'] = df_geometry.loc[df_pca_input.index, 'joint'].values
    
    # 3D Scatter Plot
    fig = px.scatter_3d(df_pca, x='PC1', y='PC2', z='PC3', color='joint',
                        title='PCA of Robot Geometries')
    vis_dir = os.path.join(output_dir, 'vis')
    os.makedirs(vis_dir, exist_ok=True)
    filename = os.path.join(vis_dir, 'pca_3d_scatter.html')
    fig.write_html(filename)
    print(f"PCA 3D scatter plot saved to {filename}")

def perform_kmeans(df_geometry, output_dir, n_clusters=5):
    """
    Performs KMeans clustering on geometry data and saves a 3D scatter plot.
    
    Args:
        df_geometry (DataFrame): Geometry data.
        output_dir (str): Directory to save plot.
        n_clusters (int): Number of clusters for KMeans.
    """
    features = ['xyz_x', 'xyz_y', 'xyz_z']
    df_kmeans_input = df_geometry[features].dropna()
    
    if df_kmeans_input.empty:
        print("No data available for KMeans clustering. Skipping KMeans plot.")
        return
    
    x = df_kmeans_input.values
    kmeans = KMeans(n_clusters=n_clusters, random_state=0)
    clusters = kmeans.fit_predict(x)
    df_geometry.loc[df_kmeans_input.index, 'cluster'] = clusters
    
    # 3D Scatter Plot
    fig = px.scatter_3d(df_geometry, x='xyz_x', y='xyz_y', z='xyz_z',
                        color='cluster', symbol='joint',
                        title='KMeans Clustering of Robot Geometries',
                        hover_data=['joint'])
    vis_dir = os.path.join(output_dir, 'vis')
    os.makedirs(vis_dir, exist_ok=True)
    filename = os.path.join(vis_dir, 'kmeans_3d_scatter.html')
    fig.write_html(filename)
    print(f"KMeans 3D scatter plot saved to {filename}")

def correlation_analysis(df_geometry, df_torque, output_dir):
    """
    Performs correlation analysis between geometry and torque limits.
    Saves a heatmap of the correlation matrix.
    
    Args:
        df_geometry (DataFrame): Geometry data.
        df_torque (DataFrame): Torque limits data.
        output_dir (str): Directory to save plot.
    """
    # Merge geometry and torque data on model_id and joint
    df_merged = pd.merge(df_geometry, df_torque, on=['model_id', 'joint'])
    
    # Select relevant numerical columns
    numeric_cols = ['xyz_x', 'xyz_y', 'xyz_z', 'rpy_roll', 'rpy_pitch', 'rpy_yaw',
                    'effort', 'min_val', 'max_val', 'degree', 'max_limit', 'min_limit']
    
    df_corr = df_merged[numeric_cols].dropna()
    
    if df_corr.empty:
        print("No data available for correlation analysis. Skipping correlation plot.")
        return
    
    # Compute correlation matrix
    corr = df_corr.corr()
    
    # Heatmap of correlations
    plt.figure(figsize=(12, 10))
    sns.heatmap(corr, annot=True, cmap='coolwarm', fmt=".2f")
    plt.title('Correlation Matrix between Geometry and Torque Limits')
    vis_dir = os.path.join(output_dir, 'vis')
    os.makedirs(vis_dir, exist_ok=True)
    filename = os.path.join(vis_dir, 'correlation_matrix.png')
    plt.savefig(filename)
    plt.close()
    print(f"Correlation matrix heatmap saved to {filename}")

if __name__ == '__main__':
    OUTPUT_DIR = '.'  # Current directory
    geometry_csv = os.path.join(OUTPUT_DIR, 'geometry_data.csv')
    torque_csv = os.path.join(OUTPUT_DIR, 'torque_limits_data.csv')
    
    # Check if CSV files exist
    if not os.path.isfile(geometry_csv):
        print(f"Geometry data file not found at {geometry_csv}. Please run data_aggregation.py first.")
        exit(1)
    if not os.path.isfile(torque_csv):
        print(f"Torque limits data file not found at {torque_csv}. Please run data_aggregation.py first.")
        exit(1)
    
    # Load data
    df_geometry = pd.read_csv(geometry_csv)
    df_torque = pd.read_csv(torque_csv)
    
    # Generate Visualizations
    visualize_geometry(df_geometry, OUTPUT_DIR)
    visualize_torque(df_torque, OUTPUT_DIR)
    perform_pca(df_geometry, OUTPUT_DIR)
    perform_kmeans(df_geometry, OUTPUT_DIR)
    correlation_analysis(df_geometry, df_torque, OUTPUT_DIR)
    
    print("Visualization tasks completed successfully.")

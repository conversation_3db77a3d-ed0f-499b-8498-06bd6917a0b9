# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause


"""
Script to play a benchmark an Excavation RL agent from RSL-RL and logg a lot of aditional informations for debugging.
init only above soil
use randomized scenario
stats:
1) % hist with termination cause
2) disable negative termination, -> timeout & positive still active
2.1) report violation of termination criteria
- max vel
- bucket aoa
- base vel 
- joint vel
- bucket height (probably spilling)
- invalid soil model (check it externally without terminating)
- self_collision -> not checked
- max depth
- pullup
- spilling
3) ep length

many environments
do logging manually not with logger, or stripped down logger
"""

import argparse

from isaaclab.app import AppLauncher

# local imports
import os
import sys

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Now you can import cli_args as if it was on the same level
import cli_args  # isort: skip
# add argparse arguments
parser = argparse.ArgumentParser(description="Train an RL agent with RSL-RL.")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")
# append RSL-RL cli arguments
cli_args.add_rsl_rl_args(parser)
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
args_cli = parser.parse_args()
args_cli.task = 'Isaac-m545-v0'
args_cli.num_envs = 4096
args_cli.headless = True
args_cli.seed = 2

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

import gymnasium as gym
import os
import torch
import traceback
import carb
from rsl_rl.runners import OnPolicyRunner
from datetime import datetime

import moleworks_ext.tasks  # noqa: F401
from isaaclab_tasks.utils import get_checkpoint_path, parse_env_cfg, get_run_path
from isaaclab_rl.rsl_rl import (
    RslRlOnPolicyRunnerCfg,
    RslRlVecEnvWrapper,
    export_policy_as_onnx,
)
from moleworks_ext.tasks.ugep.excavation_utils.excavation_arg_helper import get_play_args, override_env_cfg_with_args
from moleworks_ext.tasks.ugep.excavation_utils.excavation_utils import multipage
from moleworks_ext.tasks.ugep.excavation_utils.visualization_excavation import define_markers


import matplotlib
import matplotlib.pyplot as plt
import pickle
from collections import deque
import statistics
import numpy as np

import isaaclab.debug_draw as isaaclab_debug_draw



def main():
    #--------- Make the environment
    agent_cfg: RslRlOnPolicyRunnerCfg = cli_args.parse_rsl_rl_cfg(args_cli.task, args_cli)
    # Load paths
    log_root_path = os.path.join("logs", "rsl_rl", agent_cfg.experiment_name)
    log_root_path = os.path.abspath(log_root_path)
    # Override manually # to not get last run/ model
    #agent_cfg.load_run =  '2024-03-16_18-56-54_train' # '2024-03-06_17-29-24_train' #
    # agent_cfg.load_checkpoint = 'model_14999.pt' # 'model_19999' # 
    resume_path = get_checkpoint_path(log_root_path, agent_cfg.load_run, agent_cfg.load_checkpoint)
    run_path = get_run_path(log_root_path, agent_cfg.load_run)

    # Override resume_path here with the right path here to access another Policy (Gym for example)
    # dt = 0.03, decimation = 3
    #resume_path = '/home/<USER>/isaac_ws/m545_isaac/logs/excavation/random_soil/train_24_03_14_16_11_53/model_9999.pt'

    # Load train args
    with open(os.path.join(run_path, "params/env.pkl"), "rb") as f:
        args = pickle.load(f)
    
    # override args from play_args
    play_args = get_play_args()
    for key, value in vars(play_args).items():
        vars(args)[key] = value

    # parse configuration
    env_cfg = parse_env_cfg(args_cli.task, use_gpu=not args_cli.cpu, num_envs=args_cli.num_envs)
    env_cfg = override_env_cfg_with_args(env_cfg, args)
    
    # some more overriding
    env_cfg.reset.sample_soil = True
    env_cfg.reset.only_above_soil = True
    env_cfg.send_timeouts = True
    env_cfg.terminations_excavation.disable_negative_termination = False
    env_cfg.limits.infinite_torque = False #args.infinite_torque
    env_cfg.decimation = 4
    env_cfg.sim.dt = 0.0375
    
    # create isaac environment
    env = gym.make(args_cli.task, cfg=env_cfg)
    # wrap around environment for rsl-rl
    env = RslRlVecEnvWrapper(env)
    # Set Curriculum
    env.unwrapped.curriculum_excavation.set_level_and_update(2000)
    env.unwrapped.curriculum_excavation.theta = args.rbf_theta
    env.unwrapped.soil.soil_height.compute_norm_transform(theta=env.unwrapped.curriculum_excavation.theta)
    env.unwrapped.soil.max_depth_height.compute_norm_transform(theta=env.unwrapped.curriculum_excavation.theta)

    #--------- Load Policy
    # load previously trained model
    ppo_runner = OnPolicyRunner(env, agent_cfg.to_dict(), log_dir=None, device=agent_cfg.device)
    ppo_runner.load(resume_path)
    print(f"[INFO]: Loading model checkpoint from: {resume_path}")
    # obtain the trained policy for inference
    policy = ppo_runner.get_inference_policy(device=env.unwrapped.device)

    #--------- Log data
    # create logdir
    play_log_dir = os.path.join(
        run_path,
       "play_"+os.path.splitext(os.path.basename(resume_path))[0],
        datetime.now().strftime("%y_%m_%d_%H_%M_%S_") + "benchmarking",
    )
    os.makedirs(play_log_dir)


    # prepare experiment data
    num_steps = int(args.time / (env.unwrapped.physics_dt * env.unwrapped.cfg.decimation))
    num_data = args_cli.num_envs * num_steps

    env_initial_indices = env.unwrapped.sampled.clone()

    bucket_aoa = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    bucket_vel = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    base_vel = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    max_depth = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    pullup_dist = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    in_soil = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    bucket_x = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    bucket_z = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    fill_ratios = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    curls = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    ep_min_bucket_height = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    bucket_force_com = torch.zeros(args_cli.num_envs, num_steps, 3, device=env.device)

    # need to keep track of it manually, because step() resets if done but we only log after step()
    ep_lens = torch.zeros(args_cli.num_envs, device=env.device)

    ssp_alpha = torch.zeros(args_cli.num_envs, num_steps, device=env.device)



    soil_params = torch.zeros(args_cli.num_envs, num_steps, env.unwrapped.soil.soil_parameters.n_params, device=env.device)
    soil_params_pre = env.unwrapped.soil.soil_parameters.params.clone()
    pullup_dist_pre = env.unwrapped.pullup_dist.clone()
    max_depth_height_z_pre = env.unwrapped.soil.max_depth_height.z.clone()
    soil_height_z_pre = env.unwrapped.soil.soil_height.z.clone()
    soil_height_offset_pre = env.unwrapped.soil.soil_height.offset.clone()
    max_depth_height_offset_pre = env.unwrapped.soil.max_depth_height.offset.clone()

    ep_len_counts = {}
    ep_len_counts["timeout"] = deque()
    for name in env.unwrapped.termination_excavation.neg_term_names:
        ep_len_counts["neg_" + name] = deque()
    ep_len_counts["close"] = deque()
    ep_len_counts["full"] = deque()



    #--------- Run Experiment
    # reset environment

    episode_count = 0
    env.reset()
    initial_dofs = env.unwrapped.m545_measurements.joint_pos.clone()
    joint_pos_bucket_vel = []

    
    # simulate environment
    #while simulation_app.is_running():
        # run everything in inference mode
    
    bucket_vel_max_per_episode = torch.zeros(args_cli.num_envs, 1, device=env.device)
    bucket_vel_max_close = []
    bucket_vel_max_full = []
    soil_param_close = []
    soil_param_full = []
    soil_param_bucket_vel = []
    bucket_x_list = []
    bucket_z_list = []
    fill_ratios_list = []
    curls_list = []
    ep_min_bucket_height_list = []

    forces_bucket_vel = []

    bucket_vel_norm_list = []

    # Debug Draw
    if not args_cli.headless:
        termination_visualizer = define_markers()

    env_id_bucket_vel = 0
    ep_len_bucket_vel = 0
    # env stepping
    env.reset()
    obs, _ = env.get_observations()

    with torch.inference_mode():

        # Loop
        for i in range(num_steps):
            if i % 20 == 0:
                print("step {}/{}".format(i, num_steps))
                '''if args.log_env_nr is not None:
                logger.log_states(i)  # want first/reset state'''

            ''' if i% 100 ==0 and i !=0:
                values = []
                sum_pos_term = 0
                for key, value in env.unwrapped.termination_excavation.episode_pos_term_counts.items():
                    values.append(torch.sum(value).item() / episode_count)
                    sum_pos_term += int(torch.sum(value).item())
                print("Success rate:", sum_pos_term/episode_count)'''


            # Pre-step-storage
            soil_params_pre = env.unwrapped.soil.soil_parameters.params.clone()
            pullup_dist_pre = env.unwrapped.pullup_dist.clone()
            max_depth_height_z_pre = env.unwrapped.soil.max_depth_height.z.clone()
            soil_height_z_pre = env.unwrapped.soil.soil_height.z.clone()
            soil_height_offset_pre = env.unwrapped.soil.soil_height.offset.clone()
            max_depth_height_offset_pre = env.unwrapped.soil.max_depth_height.offset.clone()

            # agent stepping
            actions = policy(obs)

            # env stepping
            obs, rewards, dones, infos = env.step(actions)
            ep_lens += 1
            # log

            bucket_aoa[:, i] = env.unwrapped.m545_measurements.bucket_aoa
            bucket_vel[:, i] = torch.linalg.norm(env.unwrapped.m545_measurements.bucket_vel_w, dim=-1)
            base_vel[:, i] = torch.linalg.norm(env.unwrapped.m545_measurements.root_lin_vel_w, dim=-1)
            bucket_z[:, i] = env.unwrapped.m545_measurements.bucket_pos_w[:, 2]
            max_depth[:, i] = env.unwrapped.soil.get_max_depth_height_at_pos(env.unwrapped.m545_measurements.bucket_pos_w[:, 0:1]).squeeze()
            pullup_dist[:, i] = env.unwrapped.pullup_dist
            bucket_x[:, i] = env.unwrapped.m545_measurements.bucket_pos_w[:, 0]
            in_soil[:, i] = (env.unwrapped.soil.get_bucket_depth() > 0.0).squeeze()
            ssp_alpha[:, i] = (env.unwrapped.soil.ssp.soil_alpha).clone().squeeze()
            soil_params[:,i,:] = env.unwrapped.soil.soil_parameters.params.clone()
            fill_ratios[:, i] = env.unwrapped.soil.get_fill_ratio().squeeze()
            curls[:, i] = env.unwrapped.soil.get_bucket_full_angle_w().squeeze()
            ep_min_bucket_height[:, i] = env.unwrapped.termination_excavation.ep_min_bucket_height.squeeze()
            bucket_force_com[:,i] = env.unwrapped.bucket_force_com.squeeze()

            for j in range((env.unwrapped.num_envs)):
                if bucket_vel[j,i] > bucket_vel_max_per_episode[j]:
                    bucket_vel_max_per_episode[j] = bucket_vel[j,i]


            if dones.any():
                
                done_ids = dones.nonzero()

                # Indices
                #

                episode_count += len(done_ids)
                # tolist compies to cpu automatically
                ep_len_counts["timeout"].extend(ep_lens[env.unwrapped.termination_excavation.time_out_buf].tolist())

                for name in env.unwrapped.termination_excavation.neg_term_names:
                    ep_len_counts["neg_" + name].extend(ep_lens[env.unwrapped.termination_excavation.episode_neg_term_buf[name]].tolist())

                # joint pos of base vel terminated
                ids_bucket_vel = torch.nonzero(env.unwrapped.termination_excavation.episode_neg_term_buf['bucket_vel'], as_tuple= True)[0]
                ids_aoa =  torch.nonzero(env.unwrapped.termination_excavation.episode_neg_term_buf['bucket_aoa'], as_tuple= True)[0]
                joint_pos =  env.unwrapped.torques[ids_bucket_vel].clone()
                joint_pos_bucket_vel.append(joint_pos)

                ep_len_counts["close"].extend(ep_lens[env.unwrapped.termination_excavation.close_pos_term_buf].tolist())
                ep_len_counts["full"].extend(ep_lens[env.unwrapped.termination_excavation.full_pos_term_buf].tolist())
                
               
                # Get dof_pos of 
                
                # Logging for different term conditons
                ids_full= torch.nonzero(env.unwrapped.termination_excavation.full_pos_term_buf, as_tuple= True)[0]
                ids_close= torch.nonzero(env.unwrapped.termination_excavation.close_pos_term_buf, as_tuple= True)[0]

                bucket_vel_max_close.append(bucket_vel_max_per_episode[ids_close].clone())
                bucket_vel_max_full.append(bucket_vel_max_per_episode[ids_full].clone())
                bucket_vel_max_per_episode[done_ids] = 0

                soil_param_close.append(soil_params[ids_close,i-1,:])
                soil_param_full.append(soil_params[ids_full,i-1,:])
                soil_param_bucket_vel.append(soil_params[ids_bucket_vel,i-1,:])

                # Log the forces for negative termination bucket vel
                forces_bucket_vel.append(bucket_force_com[ids_bucket_vel,i-1,:])


                ids_bucket_vel_valid = []
                for k in range(len(ids_bucket_vel.tolist())):
                    if  ep_len_counts["neg_" + 'bucket_vel'][k] >10:
                        ids_bucket_vel_valid.append(ids_bucket_vel[k])

                if len(ids_bucket_vel_valid) > 0:
                    ids_bucket_vel_valid = torch.tensor(ids_bucket_vel_valid,device=env.device).reshape(-1)
                    bucket_x_list.append(bucket_x[ids_bucket_vel_valid, i-1])
                    bucket_z_list.append(bucket_z[ids_bucket_vel_valid, i-1])
                    fill_ratios_list.append(fill_ratios[ids_bucket_vel_valid, i-1])
                    curls_list.append(curls[ids_bucket_vel_valid, i-1])
                    ep_min_bucket_height_list.append(ep_min_bucket_height[ids_bucket_vel_valid, i-1])

                    env_id_bucket_vel = ids_bucket_vel_valid[0]
                    ep_len_bucket_vel = int(ep_lens[ids_bucket_vel_valid[0]])



                if env.unwrapped.termination_excavation.episode_neg_term_buf['bucket_vel'].any():
                    if not args_cli.headless:
                        marker_locations = env.unwrapped.m545_measurements.root_pos_w[ids_bucket_vel]+torch.zeros_like(env.unwrapped.m545_measurements.root_pos_w[ids_bucket_vel])
                        marker_locations[:,2] += 5.0
                        marker_orientations =  torch.tensor([1,0,0,0],device=env.unwrapped.device).expand(env.unwrapped.num_envs, -1)[ids_bucket_vel]
                        marker_indices = 6*torch.ones(env.unwrapped.num_envs, device = env.unwrapped.device)[ids_bucket_vel]
                        termination_visualizer.visualize(marker_locations, marker_orientations, marker_indices=marker_indices)

                    print(f'{i} Neg_bucket_vel done at env', ids_bucket_vel)
                
                if env.unwrapped.termination_excavation.episode_neg_term_buf['bucket_aoa'].any():
                    print(f'{i} aoa done at env', torch.nonzero(env.unwrapped.termination_excavation.episode_neg_term_buf['bucket_aoa'], as_tuple= True)[0])



                if env.unwrapped.termination_excavation.full_pos_term_buf.any():
                    print('Positive reached')
                    env_id_full = ids_full[0]
                    ep_len_full = int(ep_lens[ids_full[0]])

                # reset ep lens
                ep_lens[done_ids] = 0
                env_initial_indices[done_ids] = env.unwrapped.sampled[done_ids]
            '''if i == 10:
                env.unwrapped.termination_excavation.episode_pos_term_counts["desired_close"] = 0
                env.unwrapped.termination_excavation.episode_pos_term_counts["desired_close"] = 0'''

    show_custom_plots = False


    plt.figure(figsize=(8, 6))
    plt.hist(ep_len_counts["neg_" + 'bucket_vel'], bins=40, alpha=0.75)
    plt.title('Distribution of episode length for bucket_vel termination')
    plt.xlabel('Value')
    plt.ylabel('Frequency')
    plt.show()
    

    plt.figure(figsize=(8, 6))
    plt.hist(ep_len_counts["neg_" + 'bucket_aoa'], bins=40, alpha=0.75)
    plt.title('Distribution of episode length for bucket_aoa termination')
    plt.xlabel('Value')
    plt.ylabel('Frequency')
    plt.show()

    if show_custom_plots:
        plt.figure(figsize=(8, 6))
        plt.hist(torch.cat(bucket_vel_max_full, dim=0).cpu().numpy(), bins=40, alpha=0.75)
        plt.title('Distribution of maximmum bucket velocity for full termination')
        plt.xlabel('Value')
        plt.ylabel('Frequency')
        plt.show()
        
    
        
        plt.figure(figsize=(8, 6))
        plt.plot(bucket_vel[ env_id_bucket_vel,:ep_len_bucket_vel].cpu().numpy())
        plt.title('Bucket velocity for  bucket vel terminated episode')
        plt.xlabel('iter')
        plt.ylabel('Bucket vel')
        plt.show()

        plt.figure(figsize=(8, 6))
        plt.plot(bucket_vel[ env_id_full,:ep_len_full].cpu().numpy())
        plt.title('Bucket velocity for  bucket vel terminated episode')
        plt.xlabel('iter')
        plt.ylabel('Bucket vel')
        plt.show()
  
  
        plt.figure(figsize=(8, 6))
        plt.hist(bucket_force_com[:,0].cpu().numpy(), bins=40, alpha=0.75)
        plt.title('Distribution of x force on bucket ')
        plt.xlabel('Value')
        plt.ylabel('Frequency')
        plt.show()

        plt.figure(figsize=(8, 6))
        plt.hist(bucket_force_com[:,2].cpu().numpy(), bins=40, alpha=0.75)
        plt.title('Distribution of z force on bucket ')
        plt.xlabel('Value')
        plt.ylabel('Frequency')
        plt.show()
        
        plt.figure(figsize=(8, 6))
        plt.hist(torch.cat(fill_ratios_list, dim=0).cpu().numpy(), bins=40, alpha=0.75)
        plt.title('Distribution of fill_ratios before termination after 10 steps')
        plt.xlabel('Value')
        plt.ylabel('Frequency')
        plt.show()


        plt.figure(figsize=(8, 6))
        plt.hist(torch.cat(curls_list, dim=0).cpu().numpy(), bins=40, alpha=0.75)
        plt.title('Distribution of curls before termination after 10 steps')
        plt.xlabel('Value')
        plt.ylabel('Frequency')
        plt.show()

        heights = torch.cat(bucket_z_list, dim=0)-torch.cat(ep_min_bucket_height_list, dim=0)
        plt.figure(figsize=(8, 6))
        plt.hist(heights.cpu().numpy(), bins=40, alpha=0.75)
        plt.title('Distribution of heights before  termination after 10 steps')
        plt.xlabel('Value')
        plt.ylabel('Frequency')
        plt.show()


        joint_pos_reset_bucket_vel_sim = torch.cat(joint_pos_bucket_vel, dim=0)
        fig, axs = plt.subplots(1, 4, figsize=(20, 5))

        for i in range(4):
            axs[i].hist(joint_pos_reset_bucket_vel_sim[:, i].cpu().numpy(), bins=20, alpha=0.7)
            axs[i].set_title(f'Histogram of joint torques {i+1}')

        plt.tight_layout()
        plt.show()

        plt.figure(figsize=(8, 6))
        plt.hist(ep_len_counts["neg_" + 'bucket_vel'], bins=40, alpha=0.75)
        plt.title('Distribution of episode length for bucket_vel termination')
        plt.xlabel('Value')
        plt.ylabel('Frequency')
        plt.show()

        plt.figure(figsize=(8, 6))
        plt.hist((ep_len_counts["full"]), bins=40, alpha=0.75)
        plt.title('Distribution of episode length for full termination')
        plt.xlabel('Value')
        plt.ylabel('Frequency')
        plt.show()

        plt.figure(figsize=(8, 6))
        plt.hist(torch.cat(bucket_vel_max_close, dim=0).cpu().numpy(), bins=40, alpha=0.75)
        plt.title('Distribution of maximmum bucket velocity for close termination')
        plt.xlabel('Value')
        plt.ylabel('Frequency')



        fig, axs = plt.subplots(env.unwrapped.soil.soil_parameters.n_params, 1, figsize=(10, 8))
        soil_param_close_tensor = torch.cat(soil_param_close,dim=0)
        for i in range(env.unwrapped.soil.soil_parameters.n_params):
            # Flattening the tensor across environments and steps for each parameter
            param_values = soil_param_close_tensor[:, i].flatten().cpu().numpy()
            axs[i].hist(param_values, bins=20, alpha=0.75)
            axs[i].set_title(f'soil_param_close_termination{i+1}')
        plt.tight_layout()


        fig2, axs2 = plt.subplots(env.unwrapped.soil.soil_parameters.n_params, 1, figsize=(10, 8))
        soil_param_full_tensor = torch.cat(soil_param_full,dim=0)
        for i in range(env.unwrapped.soil.soil_parameters.n_params):
            # Flattening the tensor across environments and steps for each parameter
            param_values = soil_param_full_tensor[:, i].flatten().cpu().numpy()
            axs2[i].hist(param_values, bins=20, alpha=0.75)
            axs2[i].set_title(f'soil_param_full_termination{i+1}')
        plt.tight_layout()

        fig3, axs3 = plt.subplots(env.unwrapped.soil.soil_parameters.n_params, 1, figsize=(10, 8))
        soil_param_bucket_vel_tensor = torch.cat(soil_param_bucket_vel,dim=0)
        for i in range(env.unwrapped.soil.soil_parameters.n_params):
            # Flattening the tensor across environments and steps for each parameter
            param_values = soil_param_bucket_vel_tensor[:, i].flatten().cpu().numpy()
            axs3[i].hist(param_values, bins=20, alpha=0.75)
            axs3[i].set_title(f'soil_param_bucket_vel_termination{i+1}')
        plt.tight_layout()

        plt.show()



        '''
        TODO: Implement
        if args.log_env_nr is not None:
            logger.log_states(i + 1)  # end state
            logger.log_dones()
            logger.plot_states(show=False)
            logger.save_plots(play_log_dir, open=True)
        '''

        # Flattening the tensor to get all values in a single array for plotting
        all_alpha_values = ssp_alpha.cpu().flatten().numpy()

        # Plotting histogram for ssp_alpha to see its distribution
        #plt.figure(figsize=(8, 6))
        #plt.hist(all_alpha_values, bins=20, alpha=0.75)
        #plt.title('Distribution of ssp_alpha')
        #plt.xlabel('Value')
        #plt.ylabel('Frequency')
        #plt.show()

        # Soil parameters
        fig, axs = plt.subplots(env.unwrapped.soil.soil_parameters.n_params, 1, figsize=(10, 8))
    #
        for i in range(env.unwrapped.soil.soil_parameters.n_params):
            # Flattening the tensor across environments and steps for each parameter
            param_values = soil_params[:, :, i].flatten().cpu().numpy()
            axs[i].hist(param_values, bins=20, alpha=0.75)
            axs[i].set_title(f'soil_param_all_{i+1}')

        plt.tight_layout()
        plt.show()

        # Plotting histogram for ssp_alpha to see its distribution
        plt.figure(figsize=(8, 6))
        plt.hist(bucket_vel.cpu().numpy(), bins=30, alpha=0.75)
        plt.title('Distribution of bucket_vel')
        plt.xlabel('Value')
        plt.ylabel('Frequency')
        plt.show()

        print('Max base vel', bucket_vel.max())
        mask = (base_vel == bucket_vel.max())
        print('Number of max:' ,  mask.sum().item())


    #--------- Plotting
    # terminations, percentage [0,1]
    values = []
    labels = []
    for key, value in env.unwrapped.termination_excavation.episode_neg_term_counts.items():
        values.append(torch.sum(value).item() / episode_count)
        labels.append(key)

    sum_pos_term = 0
    for key, value in env.unwrapped.termination_excavation.episode_pos_term_counts.items():
        values.append(torch.sum(value).item() / episode_count)
        labels.append(key)
        sum_pos_term += int(torch.sum(value).item())

    full_term = torch.sum(env.unwrapped.termination_excavation.episode_pos_term_counts["desired_full"]).item() / episode_count
    close_term = torch.sum(env.unwrapped.termination_excavation.episode_pos_term_counts["desired_close"]).item() / episode_count


    values.append(torch.sum(env.unwrapped.termination_excavation.time_out_count).item() / episode_count)
    labels.append("timeout")

    _, ax = plt.subplots()
    ax.tick_params(axis="x", which="major", labelsize=6)
    ax.bar(np.arange(len(values)), values, tick_label=labels)
    ax.set_title(
        "close ({:.2f}) & full ({:.2f}) term/tot term: {} / {} [{:.2f}%]".format(
            close_term, full_term, sum_pos_term, episode_count, 100.0 * sum_pos_term / episode_count
        )
    )
    ax.grid()

    # stats violating negative termination conditions


    def log_and_print_stats(name, errs, num_data, error_dict):
        error_dict[name] = errs
        print(
            "{:<25} num/num_data: {:<10.2e} mean: {:<7.2f} std: {:<7.2f} min: {:<7.2f} max: {:<7.2f}".format(
                name,
                len(errs) / num_data,
                statistics.mean(errs) if len(errs) > 1 else np.nan,
                statistics.stdev(errs) if len(errs) > 1 else np.nan,
                min(errs) if len(errs) > 0 else np.nan,
                max(errs) if len(errs) > 0 else np.nan,
            )
        )


    print("num data samples: ", num_data)
    error_dict = {}

    # bucket aoa
    bad_aoa = bucket_aoa < 0.0
    fast_enough = bucket_vel > env.cfg.terminations_excavation.bucket_vel_aoa_threshold
    ids = torch.where(torch.logical_and(in_soil, torch.logical_and(bad_aoa, fast_enough)))
    errs = bucket_aoa[ids] - 0.0
    log_and_print_stats("bucket_aoa", errs.tolist(), num_data, error_dict)
    # bucket vel
    ids = torch.where(env.unwrapped.m545_measurements.bucket_vel_norm > env.cfg.terminations_excavation.max_bucket_vel)
    errs = env.unwrapped.m545_measurements.bucket_vel_norm[ids] - env.cfg.terminations_excavation.max_bucket_vel
    log_and_print_stats("bucket_vel", errs.tolist(), num_data, error_dict)

    # base vel
    ids = torch.where(base_vel > env.cfg.terminations_excavation.max_base_vel)
    errs = base_vel[ids] - env.cfg.terminations_excavation.max_base_vel
    log_and_print_stats("base_vel", errs.tolist(), num_data, error_dict)

    # max depth
    ids = torch.where(bucket_z < (max_depth - env.cfg.terminations_excavation.max_depth_overshoot))
    errs = bucket_z[ids] - (max_depth[ids] - env.cfg.terminations_excavation.max_depth_overshoot)
    log_and_print_stats("max_depth", errs.tolist(), num_data, error_dict)

    # pullup
    ids = torch.where(bucket_x < pullup_dist)
    errs = bucket_x[ids] - pullup_dist[ids]
    log_and_print_stats("pullup", errs.tolist(), num_data, error_dict)

    # episode lengths
    log_and_print_stats("len timeout", ep_len_counts["timeout"], num_data, error_dict)
    log_and_print_stats("len close", ep_len_counts["close"], num_data, error_dict)
    log_and_print_stats("len full", ep_len_counts["full"], num_data, error_dict)

    for name in env.unwrapped.termination_excavation.neg_term_names:
        log_and_print_stats("len neg_" + name, ep_len_counts["neg_" + name], num_data, error_dict)


    for key, value in error_dict.items():
        fig, ax = plt.subplots()
        ax.boxplot(value)
        ax.set_xticklabels([key], fontsize=6)
        quantiles = np.quantile(value, [0.25, 0.5, 0.75]) if len(value) > 1 else [0, 0, 0]
        ax.set_yticks(quantiles)
        ax.set_title(
            "q1-q3: {}, steps/total_steps: {} / {} [{:.2f}%]".format(
                " | ".join([str(np.round(x, 2)) for x in quantiles]), len(value), num_data, 100.0 * len(value) / num_data
            )
        )
        ax.grid()

    # save all plots in pdf and open
    filename = os.path.join(play_log_dir, "stats.pdf")
    multipage(filename)
    os.system("xdg-open " + filename)
    plt.close("all")

    # close the simulator
    env.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()


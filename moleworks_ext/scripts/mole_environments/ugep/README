# Play
Example:
```
/home/<USER>/anaconda3/envs/m545_orbit/bin/python /home/<USER>/git/orbit/source/standalone/workflows/rsl_rl/ugep/excavation_play.py --run_name 2024-05-13_18-17-47_linear_100_scale_066
```
If you wanna visualize you need to change the asset path on the excavator.py file

# Benchmark
you need to modify the variable:
```
    agent_cfg.load_run = "2024-05-13_17-52-57_random_100_scale_100"
```
the run the excavation_benchmark.py file and remember to change the asset path on the excavator.py ```
ASSETS_DIR = f"{ORBIT_ASSETS_DATA_DIR}/Robots/Gravis/usd_linear_100_scale_066_2_mass"

# Conversion
To convert the urdf files you can use the following command:
```
python source/standalone/tools/convert_multiple_urdfs.py --input_folder /home/<USER>/git/rlgpu/m545_isaac_gym/rsc/m545_urdf_linear_10 --output_folder /home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_assets/data/Robots/Gravis/utest_10 --urdf_file m545_fixed_tele_narrow_merged_modified_no_visual.urdf
```




import argparse
import numpy as np
import os
import pickle
import shutil
import sys
import xml.etree.ElementTree as ET
import yaml
from typing import Any, Dict
import trimesh
import pandas as pd

from dataclasses import dataclass
from typing import Optional, List, Dict
import xml.etree.ElementTree as ET
from isaaclab.app import App<PERSON>auncher

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import cli_args  # isort: skip


def parse_arguments():
    parser = argparse.ArgumentParser(description="Train an RL agent with RSL-RL.")
    parser.add_argument("--cpu", action="store_true", default=False, help="Use CPU pipeline.")
    parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
    parser.add_argument("--task", type=str, default=None, help="Name of the task.")
    parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")
    parser.add_argument("--tags", nargs='+', type=str, default=[], help="Tags used for the run.")
    parser.add_argument("--num_samples", type=int, default=1500, help="Number of samples to generate.")
    parser.add_argument("--copy_meshes", action="store_true", default=True, help="Whether to copy meshes.")
    cli_args.add_rsl_rl_args(parser)
    AppLauncher.add_app_launcher_args(parser)
    args = parser.parse_args()
    args.task = 'Isaac-ugep-v0'
    args.num_envs = 100
    args.headless = True
    return args


args = parse_arguments()
app_launcher = AppLauncher(args)
simulation_app = app_launcher.app

from moleworks_ext.assets.general_excavator import Cat323ReferenceValues, GeneralExcavatorReferenceValues

from moleworks_ext.tasks.gep.excavation_utils.generate_ee_contraints import generate_ee_constraints_model
from moleworks_ext.tasks.gep.excavation_utils.helpers import extract_transformations_and_limits

# Global variables
NUM_SAMPLES = args.num_samples
COPY_MESHES = args.copy_meshes
URDF_FOLDER = '/home/<USER>/git/rlgpu/m545_isaac_gym/rsc/cat323/urdf'
URDF_PATH = os.path.join(URDF_FOLDER, 'cat323_rl_merged.urdf')
TORQUE_LIMITS_PATH = '/home/<USER>/git/rlgpu/m545_isaac_gym/rsc/cat323/limits/torque_limits.yaml'
OUTPUT_PATH = f'/home/<USER>/git/rlgpu/m545_isaac_gym/rsc/sampled_urdfs_{NUM_SAMPLES}/'
MESH_FOLDER = '/home/<USER>/git/rlgpu/m545_isaac_gym/rsc/cat323'
SAMPLES_CSV_PATH = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/gep/excavation_utils/data/new_samples_with_torque.csv'
REFERENCE_VALUES = Cat323ReferenceValues


def generate_scale_factors_yaml(output_path: str, i: int, row: pd.Series, reference_values: GeneralExcavatorReferenceValues):
    scale_factors = {
        "BASE": [float(row['Track Length'] / reference_values.ref_base_length), 1.0, float(row["J_BOOM_z"] / reference_values.boom_height)],
        "BOOM": [float(row['J_BOOM_Length'] / reference_values.boom_length), 1.0, 1.0],
        "DIPPER": [float(row['J_DIPPER_Length'] / reference_values.dipper_length), 1.0, 1.0],
        # same scaling in x and y
        "SHOVEL": [float(row['Shovel_x_offset'] / reference_values.shovel_length), float(row['Shovel_Width'] / reference_values.shovel_length), 1.0]
    }
    folder_path = os.path.join(output_path, str(i))
    os.makedirs(folder_path, exist_ok=True)
    file_path = os.path.join(folder_path, "scale_factors.yaml")
    with open(file_path, 'w') as file:
        yaml.dump(scale_factors, file, default_flow_style=False)


def load_and_prepare_data():
    new_samples_df = pd.read_csv(SAMPLES_CSV_PATH)
    return new_samples_df.head(NUM_SAMPLES)


def scale_mesh_(file_path, scale_x=1.0, scale_y=1.0, scale_z=1.0):
    """Scales the vertices of a mesh in a COLLADA (.dae) file."""
    # Create a backup of the original file if it doesn't exist
    backup_path = file_path + ".backup"
    if not os.path.exists(backup_path):
        shutil.copy(file_path, backup_path)

    # Register the COLLADA namespace
    ET.register_namespace('', 'http://www.collada.org/2005/11/COLLADASchema')

    # Load the COLLADA file
    tree = ET.parse(file_path)
    root = tree.getroot()

    # Namespace map
    ns = {'collada': 'http://www.collada.org/2005/11/COLLADASchema'}

    # Find all the float_array elements containing vertex positions
    for float_array in root.findall('.//collada:float_array', ns):
        float_list = [float(num) for num in float_array.text.split()]

        if len(float_list) % 3 != 0:
            print(f"Error in vertex format in {float_array.attrib['id']}")
            shutil.copy(backup_path, file_path)  # Restore from backup
            return

        vertices = np.array(float_list).reshape(-1, 3)

        vertices[:, 0] *= scale_x
        vertices[:, 1] *= scale_y
        vertices[:, 2] *= scale_z

        scaled_float_list = vertices.flatten().tolist()

        float_array.text = ' '.join(f"{num:.6f}" for num in scaled_float_list)

    # Write the modified tree back to the file
    tree.write(file_path, xml_declaration=True, encoding='UTF-8')


def scale_stl(file_path, scale_x=1.0, scale_y=1.0, scale_z=1.0):
    """Scales an STL file."""
    # Load the mesh using trimesh
    mesh = trimesh.load(file_path)

    # Scale the mesh vertices
    mesh.apply_scale([scale_x, scale_y, scale_z])

    # Save the modified mesh back to the STL file
    mesh.export(file_path)
    print(f"Scaled STL {file_path} by {scale_x}, {scale_y}, {scale_z}")


def scale_meshes(link, scale_x, scale_y, scale_z, urdf_file_path):
    """Scales the meshes in a link, handling both DAE and STL files."""
    urdf_dir = os.path.dirname(urdf_file_path)
    for visual in link.findall('visual'):
        for mesh in visual.findall('geometry/mesh'):
            if 'filename' in mesh.attrib:
                file_path = os.path.join(urdf_dir, mesh.attrib['filename'])
                if file_path.endswith('.dae'):
                    xyz = []
                    tf = visual.find('origin')
                    tf_xyz = tf.get('xyz')
                    
                    if tf_xyz:
                        xyz.extend(map(float, tf_xyz.split()))
                        xyz[0] *= scale_x
                        xyz[1] *= scale_y
                        xyz[2] *= scale_z
                        tf.set("xyz", " ".join(map(str, xyz)))
                    scale_mesh_(file_path, scale_x=scale_x, scale_y=scale_y, scale_z=scale_z)
                elif file_path.endswith('.stl'):
                    scale_stl(file_path, scale_x=scale_x, scale_y=scale_y, scale_z=scale_z)
                else:
                    print(f"Unsupported file type: {file_path}")


def copy_meshes(sample_dir: str):
    mesh_path = os.path.join(MESH_FOLDER, 'meshes')
    dest_path = os.path.join(sample_dir, 'meshes')
    if os.path.exists(dest_path):
        shutil.rmtree(dest_path)
    shutil.copytree(mesh_path, dest_path)


def find_pkl_file(folder_path: str) -> str:
    pkl_file = next((f for f in os.listdir(folder_path) if f.endswith('.pkl')), None)
    if pkl_file is None:
        raise FileNotFoundError("No .pkl file found in the torque limits folder.")
    return os.path.join(folder_path, pkl_file)


def update_torque_limits(row: pd.Series, torque_limits: Dict[str, Any]) -> Dict[str, Any]:
    new_torque_limits = torque_limits.copy()
    for joint in ['J_BOOM', 'J_DIPPER', 'J_SHOVEL']:
        new_torque_limits[joint]['min'] = float(row[f'{joint}_Min_Torque'])
        new_torque_limits[joint]['max'] = float(row[f'{joint}_Max_Torque'])
    return new_torque_limits


@dataclass
class JointMapping:
    joint_name: str
    length_keys: List[Optional[str]]
    pitch_keys: List[Optional[str]] = None


def scale_value(sample_value: float, reference_value: float) -> float:
    if reference_value == 0:
        raise ValueError("Reference value for scaling cannot be zero.")
    return sample_value / reference_value


def update_origin_attributes(origin_element, scaling_factors: List[Optional[float]], pitch_factors: Optional[List[Optional[float]]] = None):
    # Update xyz
    xyz_str = origin_element.get('xyz', '')
    xyz_values = list(map(float, xyz_str.split())) if xyz_str else [0.0, 0.0, 0.0]
    
    for idx, scale in enumerate(scaling_factors):
        if scale is not None:
            xyz_values[idx] *= scale
    
    origin_element.set('xyz', ' '.join(f"{val:.6f}" for val in xyz_values))
    
    # Update rpy if applicable
    if pitch_factors:
        rpy_str = origin_element.get('rpy', '')
        rpy_values = list(map(float, rpy_str.split())) if rpy_str else [0.0, 0.0, 0.0]
        
        for idx, scale in enumerate(pitch_factors):
            if scale is not None:
                rpy_values[idx] *= scale
                
        origin_element.set('rpy', ' '.join(f"{val:.6f}" for val in rpy_values))


def modify_mass(link_element, new_mass: float):
    mass_element = link_element.find('inertial/mass')
    if mass_element is not None:
        old_mass = mass_element.get('value')
        mass_element.set('value', f"{new_mass}")
        print(f"Updated mass from {old_mass} to {new_mass}")
    else:
        raise ValueError(f"Mass element not found in link {link_element.get('name')}")


def modify_urdf_content(urdf_content: list, row: pd.Series, reference_values: Any) -> list:
    tree = ET.ElementTree(ET.fromstring(''.join(urdf_content)))
    root = tree.getroot()
    
    joint_mappings = [
        JointMapping(joint_name='J_BOOM', length_keys=[None, None, "J_BOOM_z"]),
        JointMapping(joint_name='J_DIPPER', length_keys=['J_BOOM_Length', None, None]),
        JointMapping(joint_name='J_SHOVEL', length_keys=['J_DIPPER_Length', None, None]),
        JointMapping(joint_name='J_ENDEFFECTOR_CONTACT', length_keys=['Shovel_x_offset', None, 'Shovel_z_offset'],
                    pitch_keys=[None, 'Shovel_pitch_offset', None]),
    ]
    
    for mapping in joint_mappings:
        joint = root.find(f'.//joint[@name="{mapping.joint_name}"]')
        if joint is not None:
            origin = joint.find('origin')
            if origin is not None:
                scaling_factors = []
                for key in mapping.length_keys:
                    if key is not None:
                        ref_attr = getattr(reference_values, key, None)
                        sample_value = float(row[key])
                        if ref_attr is None:
                            raise AttributeError(f"Reference attribute '{key}' not found in REFERENCE_VALUES.")
                        scale = scale_value(sample_value, ref_attr)
                        scaling_factors.append(scale)
                    else:
                        scaling_factors.append(None)
                
                pitch_factors = []
                if mapping.pitch_keys:
                    for key in mapping.pitch_keys:
                        if key is not None:
                            ref_attr = getattr(reference_values, key, None)
                            sample_value = float(row[key])
                            if ref_attr is None:
                                raise AttributeError(f"Reference attribute '{key}' not found in REFERENCE_VALUES.")
                            scale = scale_value(sample_value, ref_attr)
                            pitch_factors.append(scale)
                        else:
                            pitch_factors.append(None)
                else:
                    pitch_factors = None
                
                update_origin_attributes(origin, scaling_factors, pitch_factors)
            else:
                print(f"No origin tag found for joint '{mapping.joint_name}'. Skipping scaling.")
        else:
            print(f"Joint '{mapping.joint_name}' not found in URDF. Skipping.")
    
    # Modify masses
    for link_name in ['BASE', 'BOOM', 'DIPPER', 'SHOVEL']:
        link = root.find(f'.//link[@name="{link_name}"]')
        if link is not None:
            new_mass = float(row.get(f'{link_name}_Mass', 0.0))
            modify_mass(link, new_mass)
        else:
            print(f"Link '{link_name}' not found in URDF. Skipping mass update.")
    
    # Convert the modified XML tree back to a list of lines
    modified_urdf_content = ET.tostring(root, encoding='unicode').splitlines(keepends=True)
    
    # Ensure the URDF ends with </robot>
    if not modified_urdf_content[-1].strip().endswith('</robot>'):
        modified_urdf_content.append('</robot>\n')
    
    return modified_urdf_content


def process_sample(idx: int, row: pd.Series, urdf_content: list, torque_limits: Dict[str, Any], pkl_source_path: str, w_p_wb_source_path: str):
    sample_dir = os.path.join(OUTPUT_PATH, str(idx))
    os.makedirs(os.path.join(sample_dir, 'urdf'), exist_ok=True)
    os.makedirs(os.path.join(sample_dir, 'limits'), exist_ok=True)

    if COPY_MESHES:
        copy_meshes(sample_dir)

    generate_scale_factors_yaml(OUTPUT_PATH, idx, row, REFERENCE_VALUES)

    new_torque_limits = update_torque_limits(row, torque_limits)
    torque_limits_path = os.path.join(sample_dir, 'limits', 'torque_limits.yaml')
    with open(torque_limits_path, 'w') as file:
        yaml.dump(new_torque_limits, file, default_flow_style=False)

    new_urdf_content = modify_urdf_content(urdf_content, row, REFERENCE_VALUES)
    urdf_path = os.path.join(sample_dir, 'urdf', 'sampled.urdf')
    with open(urdf_path, 'w') as file:
        file.writelines(new_urdf_content)

    tree = ET.parse(urdf_path)
    if COPY_MESHES:
        # Scale meshes
        root = tree.getroot()
        # scaling is useful for visualization if we wanna scale the current meshes
        scale_factors = {       
            # "BASE": [float(row['Track Length'] / REFERENCE_VALUES.ref_base_length), 1.0, 1.0],
            "BASE": [1.0, 1.0, float(row["J_BOOM_z"] / REFERENCE_VALUES.boom_height)],
            "BOOM": [float(row['J_BOOM_Length'] / REFERENCE_VALUES.boom_length), 1.0, 1.0],
            "DIPPER": [float(row['J_DIPPER_Length'] / REFERENCE_VALUES.dipper_length), 1.0, 1.0],
            # same expansion factor in x and y
            # "SHOVEL": [float(row['J_SHOVEL_Length'] / REFERENCE_VALUES.shovel_length), float(row['J_SHOVEL_Length'] / REFERENCE_VALUES.shovel_length), 1.0]
            "SHOVEL": [float(row['Shovel_x_offset'] / REFERENCE_VALUES.shovel_length), float(row['Shovel_Width'] / REFERENCE_VALUES.shovel_length), 1.0]
        }
        for link in root.findall('link'):
            link_name = link.get('name')
            print(f"Link name: {link_name}")
            if link_name in scale_factors:
                scale_x, scale_y, scale_z = scale_factors[link_name]
                scale_meshes(link, scale_x, scale_y, scale_z, urdf_path)

    # Write the modified URDF back to the file
    tree.write(urdf_path, xml_declaration=True, encoding='UTF-8')

    joint_names = ['J_BOOM', 'J_DIPPER', 'J_SHOVEL', 'J_ENDEFFECTOR_CONTACT']
    transformations_and_limits = extract_transformations_and_limits(urdf_path, joint_names)
    pickle_path = os.path.join(sample_dir, "transformations_and_limits.pickle")
    with open(pickle_path, "wb") as f:
        pickle.dump(transformations_and_limits, f)

    shutil.copy(pkl_source_path, os.path.join(sample_dir, 'limits', 'torque_factors.pkl'))
    shutil.copy(w_p_wb_source_path, os.path.join(sample_dir, 'urdf', 'w_P_wb.yaml'))

    generate_ee_constraints_model(sample_dir)
    print(f"Files generated and copied successfully for sample {idx}.")


def main():
    yaml.add_representer(np.float64, lambda dumper, data: dumper.represent_float(float(data)))

    new_samples_df = load_and_prepare_data()

    with open(URDF_PATH, 'r') as file:
        urdf_content = file.readlines()

    with open(TORQUE_LIMITS_PATH, 'r') as file:
        torque_limits = yaml.safe_load(file)

    pkl_source_path = find_pkl_file(os.path.dirname(TORQUE_LIMITS_PATH))
    w_p_wb_source_path = os.path.join(URDF_FOLDER, 'w_P_wb.yaml')
    if not os.path.exists(w_p_wb_source_path):
        raise FileNotFoundError(f"w_P_wb.yaml file not found at {w_p_wb_source_path}")

    for idx, row in new_samples_df.iterrows():
        process_sample(idx, row, urdf_content, torque_limits, pkl_source_path, w_p_wb_source_path)


if __name__ == "__main__":
    main()
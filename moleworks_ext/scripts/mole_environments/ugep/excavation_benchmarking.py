# Copyright (c) 2022-2024, The ORBIT Project Developers.
# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
Script to play and benchmark an Excavation RL agent from RSL-RL.
Modernized setup based on Isaac Lab examples. Does not log additional info for debugging.
Focuses on termination statistics and violation analysis.

Key Features:
- Uses modernized environment and policy loading from Isaac Lab examples.
- Loads training config from the specified run directory.
- Allows overriding some config via command-line or specific functions.
- Benchmarking stats:
    1) Termination cause histogram (per asset and overall).
    2) Violation checks for negative termination criteria (even if disabled).
    3) Episode length distributions per termination type.
- Logs results to a dedicated benchmarking directory within the run folder.
- Creates a multi-page PDF report and a JSON file with stats.
"""
from isaaclab.app import AppLauncher
import cli_args  # isort: skip

import argparse
import json
import os
import pickle
import statistics
import sys
import time  # Added for potential real-time option
import traceback
from collections import deque
from datetime import datetime

# Define specific command-line arguments for benchmarking
parser = argparse.ArgumentParser(
    description="Benchmark an Excavation RL agent with RSL-RL."
)
parser.add_argument(
    "--num_envs", type=int, default=1024, help="Number of environments to simulate."
)  # Default added
parser.add_argument(
    "--task", type=str, default="Isaac-ugep-v0", help="Name of the task."
)  # Default added
parser.add_argument("--seed", type=int, default=2, help="Seed used for the environment")
parser.add_argument(
    "--time", type=float, default=19.95, help="Duration of each episode in seconds."
)
parser.add_argument(
    "--warmup_steps",
    type=int,
    default=20,
    help="Number of initial steps to ignore for termination logging.",
)
parser.add_argument(
    "--disable_neg_term",
    action="store_true",
    default=False,
    help="Disable negative terminations during benchmarking.",
)
parser.add_argument(
    "--real_time",
    action="store_true",
    default=False,
    help="Run in real-time, if possible (slows down benchmarking).",
)
parser.add_argument(
    "--soil_type",
    type=str,
    default="random",
    help="Type of soil to use for the environment.",
)
# Arguments from play.py (adapted)

# append RSL-RL cli arguments (will include device, experiment_name etc.)
cli_args.add_rsl_rl_args(parser)
# append AppLauncher cli args (will include headless, cpu etc.)
AppLauncher.add_app_launcher_args(parser)
args_cli = parser.parse_args()

# --- Launch Isaac Sim ---
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

import carb
import gymnasium as gym
import matplotlib.pyplot as plt
import numpy as np
import torch
from matplotlib.backends.backend_pdf import PdfPages
from rsl_rl.runners import OnPolicyRunner

# Add the parent directory to the Python path for local imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# local imports - Assuming cli_args defines add_rsl_rl_args

# Task-specific imports (adjust paths if needed)
import moleworks_ext.tasks  # noqa: F401 - Registers the task
from moleworks_ext.tasks.ugep.excavation_utils.excavation_arg_helper import (
    get_play_args,  # Keep if specific play args are needed beyond training args
    override_env_cfg_with_args,
)
from moleworks_ext.tasks.ugep.excavation_utils.excavation_utils import (
    multipage,
)  # Keep for PDF generation
from moleworks_ext.tasks.ugep.ugep_env_cfg import (
    ugepEnvCfg,
)  # Keep if direct access is needed

# Isaac Lab imports
from isaaclab.envs import DirectMARLEnv, multi_agent_to_single_agent
from isaaclab.utils.assets import retrieve_file_path
from isaaclab.utils.dict import print_dict

# RSL-RL / Isaac Lab RL imports
from isaaclab_rl.rsl_rl import (
    RslRlOnPolicyRunnerCfg,
    RslRlVecEnvWrapper,
    # export_policy_as_jit,  # Keep if export is desired (optional)
    # export_policy_as_onnx,  # Keep if export is desired (optional)
)
from isaaclab_tasks.utils import get_checkpoint_path, parse_env_cfg


def main():
    """Runs the benchmarking process."""

    # --- Parse configurations ---
    # Agent configuration (from RSL-RL args)
    # Note: load_run and load_checkpoint from agent_cfg might be overridden below based on args_cli
    agent_cfg: RslRlOnPolicyRunnerCfg = cli_args.parse_rsl_rl_cfg(
        args_cli.task, args_cli
    )

    # --- Determine Paths ---
    log_root_path = os.path.join("logs", "rsl_rl", agent_cfg.experiment_name)
    log_root_path = os.path.abspath(log_root_path)
    print(f"[INFO] Using experiment root directory: {log_root_path}")

    # Determine the run path based on the agent config load_run (set via CLI or default)
    # Ensure load_run is not None, which should be handled by RSL-RL arg parsing
    if agent_cfg.load_run == ".*":  # Check for the default RSL-RL pattern
        # If load_run is the default pattern, find the latest run directory.
        print(
            "[INFO] agent_cfg.load_run is the default pattern '.*'. Finding the latest run..."
        )
        runs = [
            d
            for d in os.listdir(log_root_path)
            if os.path.isdir(os.path.join(log_root_path, d))
        ]
        if not runs:
            raise FileNotFoundError(
                f"No runs found in {log_root_path} when searching for the latest run."
            )
        latest_run = sorted(runs)[-1]
        run_dir_name = latest_run
        # Update agent_cfg.load_run to the actual directory name found
        # This might be useful if agent_cfg is used elsewhere expecting a concrete name
        agent_cfg.load_run = run_dir_name
        print(f"[INFO] Found latest run: {run_dir_name}")
    elif agent_cfg.load_run is None:
        # Handle case where load_run is explicitly None (shouldn't happen with current RSL defaults)
        raise ValueError(
            "agent_cfg.load_run is None. A run directory name or '.*' pattern is expected."
        )
    else:
        # A specific run directory was provided via CLI
        run_dir_name = agent_cfg.load_run
        print(f"[INFO] Using run specified in agent config: {run_dir_name}")

    run_path = os.path.join(log_root_path, run_dir_name)
    if not os.path.exists(run_path):
        raise FileNotFoundError(
            f"Run directory '{run_dir_name}' not found at: {run_path}"
        )

    # Determine the checkpoint path using logic similar to play.py
    if args_cli.checkpoint:
        # If a specific checkpoint file/dir is provided via --checkpoint
        resume_path = retrieve_file_path(args_cli.checkpoint)
        if not resume_path or not os.path.isfile(
            resume_path
        ):  # Check if it's a valid file
            raise FileNotFoundError(
                f"Checkpoint not found or is not a file at specified path: {args_cli.checkpoint}"
            )
        print(f"[INFO] Using specified checkpoint: {resume_path}")
        # Ensure the specified checkpoint belongs to the run_path derived earlier for consistency checks downstream
        if os.path.dirname(os.path.dirname(resume_path)) != run_path:
            print(
                f"[WARNING] The specified checkpoint '{resume_path}' does not seem to belong to the derived run path '{run_path}'. Ensure this is intended."
            )
    else:
        # Find checkpoint within the run directory using load_checkpoint pattern from agent_cfg
        # No need to override agent_cfg values here, use what's parsed by cli_args
        resume_path = get_checkpoint_path(
            log_root_path, agent_cfg.load_run, agent_cfg.load_checkpoint
        )
        if not resume_path:
            raise FileNotFoundError(
                f"Checkpoint matching pattern '{agent_cfg.load_checkpoint}' not found in run '{agent_cfg.load_run}' located at: {run_path}"
            )
        print(
            f"[INFO] Using checkpoint from run '{agent_cfg.load_run}': {os.path.basename(resume_path)}"
        )

    # --- Load Training Arguments ---
    # Load the arguments used during training for environment config consistency
    train_args_path = os.path.join(run_path, "params/env.pkl")
    train_args = argparse.Namespace()  # Initialize with empty namespace
    if os.path.exists(train_args_path):
        try:
            with open(train_args_path, "rb") as f:
                train_args = pickle.load(f)
            print("[INFO] Loaded training arguments from:", train_args_path)
        except ModuleNotFoundError as e:
            print(
                f"[WARNING] Failed to load pickled training arguments from '{train_args_path}'."
            )
            print(f"          Error: {e}")
            print(
                "          This likely means the pickle file was created with an older incompatible environment (e.g., omni.isaac.orbit_tasks)."
            )
            print(
                "          Proceeding with default environment config. Specific training settings (like rbf_theta) may be missing."
            )
            # Keep train_args as the empty Namespace initialized earlier
        except Exception as e:
            print(
                f"[WARNING] An unexpected error occurred while loading '{train_args_path}': {e}"
            )
            # Keep train_args as the empty Namespace
    else:
        print(
            "[WARNING] Training arguments file 'params/env.pkl' not found in run directory. Using default environment config."
        )
        # train_args is already an empty Namespace

    # --- Create Environment Configuration ---
    # Start with base config parsed using task name and command-line args
    env_cfg = parse_env_cfg(
        args_cli.task,
        num_envs=args_cli.num_envs,
        # Add other relevant args from args_cli if needed by parse_env_cfg
    )

    # Ensure train_args has soil_type, using CLI arg as a fallback
    # This is to ensure compatibility with older training runs that didn't save soil_type
    # if not hasattr(train_args, "soil_type"):
    #     # args_cli.soil_type will exist due to the new argument with a default value
    #     print(
    #         f"[INFO] 'soil_type' not found in loaded training arguments. Using CLI value for soil_type: {args_cli.soil_type}"
    #     )
    #     train_args.soil_type = args_cli.soil_type

    # # Remove slope_ang from train_args if it exists, as it might cause issues with current env_cfg
    # if hasattr(train_args, "slope_ang"):
    #     print(
    #         f"[INFO] Removing 'slope_ang' from loaded training arguments to avoid potential conflicts with current env_cfg."
    #     )
    #     delattr(train_args, "slope_ang")

    # Override env_cfg with loaded training arguments
    # This function needs to exist and handle merging argparse Namespaces/dicts into the EnvCfg
    # env_cfg = override_env_cfg_with_args(env_cfg, train_args)

    # Override env_cfg with specific play/benchmarking arguments (if applicable)
    # play_args = get_play_args() # If there are specific play args different from train args
    # env_cfg = override_env_cfg_with_args(env_cfg, play_args)

    # Apply benchmarking-specific overrides
    env_cfg.seed = args_cli.seed
    # env_cfg.reset.sample_soil = True # Example: Keep or remove specific overrides as needed
    # env_cfg.reset.only_above_soil = True
    env_cfg.send_timeouts = True  # Essential for timeout tracking
    if hasattr(env_cfg, "terminations_excavation"):
        env_cfg.terminations_excavation.disable_negative_termination = (
            args_cli.disable_neg_term
        )
    else:
        print(
            "[WARNING] 'terminations_excavation' not found in env_cfg. Cannot disable negative terminations."
        )
    # Add other necessary overrides for benchmarking here
    # Example: env_cfg.terminations_excavation.max_bucket_vel = 1.0

    # --- Create Isaac Environment ---
    # Note: render_mode is not typically used in headless benchmarking
    env = gym.make(args_cli.task, cfg=env_cfg, headless=args_cli.headless)

    # Wrap for RSL-RL
    env = RslRlVecEnvWrapper(env)

    # --- Set Environment Parameters (Task-Specific) ---
    # Apply curriculum settings, noise deactivation etc. after env creation
    # Ensure these attributes/methods exist on the unwrapped environment
    try:
        # Check if the necessary attributes exist before trying to access them
        if hasattr(env.unwrapped, "curriculum_excavation") and hasattr(
            train_args, "rbf_theta"
        ):
            print("[INFO] Setting curriculum level and RBF theta...")
            env.unwrapped.curriculum_excavation.set_level_and_update(
                torch.tensor(
                    100, device=env.unwrapped.device, dtype=torch.float32
                )  # Example level
            )
            env.unwrapped.curriculum_excavation.theta = train_args.rbf_theta
            if (
                hasattr(env.unwrapped, "soil")
                and hasattr(env.unwrapped.soil, "soil_height_rbf")
                and hasattr(env.unwrapped.soil, "max_depth_height_rbf")
            ):
                env.unwrapped.soil.soil_height_rbf.compute_norm_transform(
                    theta=env.unwrapped.curriculum_excavation.theta
                )
                env.unwrapped.soil.max_depth_height_rbf.compute_norm_transform(
                    theta=env.unwrapped.curriculum_excavation.theta
                )
            else:
                print(
                    "[WARNING] Soil module or RBF attributes not found. Skipping RBF norm transform."
                )
        elif not hasattr(train_args, "rbf_theta"):
            print(
                "[WARNING] 'rbf_theta' not found in loaded training arguments (train_args). Skipping curriculum/RBF setup that depends on it."
            )
        else:  # curriculum_excavation missing
            print(
                "[WARNING] 'curriculum_excavation' attribute not found on unwrapped env. Skipping curriculum/RBF setup."
            )

        if hasattr(env.unwrapped, "train"):
            env.unwrapped.train = (
                False  # Deactivate training-specific noise/randomizations
            )
            print("[INFO] Deactivated training mode noise/randomizations.")
        else:
            print(
                "[WARNING] 'train' attribute not found on unwrapped env. Cannot deactivate training mode."
            )

    except AttributeError as e:
        print(f"[ERROR] Failed task-specific setup (curriculum/train mode): {e}")
        # Decide whether to continue or exit based on severity
        # raise # Uncomment to exit if this setup is critical

    # --- Load Policy ---
    # Initialize the runner
    # Set log_dir=None as we are not training/logging with the runner itself
    ppo_runner = OnPolicyRunner(
        env, agent_cfg.to_dict(), log_dir=None, device=agent_cfg.device
    )

    # Load the checkpoint into the runner's policy
    ppo_runner.load(resume_path)
    print(f"[INFO]: Successfully loaded model checkpoint from: {resume_path}")

    # Obtain the inference policy
    policy = ppo_runner.get_inference_policy(device=env.unwrapped.device)

    # Optional: Export policy
    # export_model_dir = os.path.join(run_path, "exported")
    # export_policy_as_jit(ppo_runner.alg.actor_critic, ppo_runner.obs_normalizer, path=export_model_dir, filename="policy.pt")
    # export_policy_as_onnx(ppo_runner.alg.actor_critic, normalizer=ppo_runner.obs_normalizer, path=export_model_dir, filename="policy.onnx")

    # --- Prepare Benchmarking ---
    # Create dedicated logging directory for this benchmarking run
    bench_log_dir = os.path.join(
        run_path,
        "benchmarking_" + os.path.splitext(os.path.basename(resume_path))[0],
        datetime.now().strftime("%y_%m_%d_%H_%M_%S"),
    )
    os.makedirs(bench_log_dir, exist_ok=True)
    print(f"[INFO] Saving benchmark results to: {bench_log_dir}")

    # Calculate simulation steps
    # Ensure dt and decimation are accessible from the env config or unwrapped env
    try:
        physics_dt = env.unwrapped.physics_dt
        decimation = env.unwrapped.cfg.decimation
        num_steps = int(args_cli.time / (physics_dt * decimation))
        print(
            f"[INFO] Simulating for {num_steps} steps per episode ({args_cli.time}s)."
        )
    except AttributeError as e:
        print(
            f"[ERROR] Cannot determine physics_dt or decimation from environment: {e}. Exiting."
        )
        env.close()
        return

    num_data = (
        args_cli.num_envs * num_steps
    )  # Total potential data points across all envs and steps

    # --- Initialize Data Logging Structures ---
    # Tensors for checking termination condition violations
    # Pre-allocate on the correct device
    device = env.unwrapped.device
    bucket_aoa = torch.zeros(args_cli.num_envs, num_steps, device=device)
    bucket_vel_norm = torch.zeros(
        args_cli.num_envs, num_steps, device=device
    )  # Renamed for clarity
    base_vel_norm = torch.zeros(
        args_cli.num_envs, num_steps, device=device
    )  # Renamed for clarity
    max_depth_limit = torch.zeros(
        args_cli.num_envs, num_steps, device=device
    )  # Renamed for clarity
    pullup_limit = torch.zeros(
        args_cli.num_envs, num_steps, device=device
    )  # Renamed for clarity
    in_soil = torch.zeros(
        args_cli.num_envs, num_steps, device=device, dtype=torch.bool
    )  # Use bool
    bucket_x = torch.zeros(args_cli.num_envs, num_steps, device=device)
    bucket_z = torch.zeros(args_cli.num_envs, num_steps, device=device)
    # Add joint velocities if needed for termination checks
    # joint_vel = torch.zeros(args_cli.num_envs, num_steps, env.unwrapped.num_actions, device=device) # Example

    # Episode length tracking
    ep_lens = torch.zeros(args_cli.num_envs, device=device, dtype=torch.long)
    ep_len_counts = {}
    ep_len_counts["timeout"] = deque()
    # Get termination names dynamically if possible
    neg_term_names = []
    pos_term_names = []
    try:
        # Adjust attribute names based on your specific termination manager
        if hasattr(env.unwrapped, "termination_excavation"):
            term_manager = env.unwrapped.termination_excavation
            neg_term_names = list(getattr(term_manager, "neg_term_names", []))
            pos_term_names = list(
                getattr(
                    term_manager,
                    "pos_term_names",
                    ["desired_full", "desired_close", "desired_partial"],
                )
            )  # Provide defaults
            # Initialize deques for all possible termination types found
            for name in neg_term_names:
                ep_len_counts["neg_" + name] = deque()
            for name in pos_term_names:
                ep_len_counts[name] = deque()  # Store positive terms directly by name
        else:
            print(
                "[WARNING] 'termination_excavation' manager not found. Using default termination names."
            )
            # Fallback to common names if manager not found
            neg_term_names = [
                "max_vel",
                "bucket_aoa",
                "base_vel",
                "joint_vel",
                "max_depth",
                "pullup",
                "spilling",
            ]  # Example defaults
            pos_term_names = ["desired_full", "desired_close", "desired_partial"]
            for name in neg_term_names:
                ep_len_counts["neg_" + name] = deque()
            for name in pos_term_names:
                ep_len_counts[name] = deque()

    except AttributeError as e:
        print(
            f"[ERROR] Could not access termination names: {e}. Using hardcoded defaults."
        )
        # Define default names if access fails
        neg_term_names = ["term1", "term2"]  # Add expected negative term names
        pos_term_names = ["pos1", "pos2"]  # Add expected positive term names
        for name in neg_term_names:
            ep_len_counts["neg_" + name] = deque()
        for name in pos_term_names:
            ep_len_counts[name] = deque()

    # --- Run Experiment ---
    total_episodes_completed = 0
    obs, _ = env.reset()  # Use VecEnv's reset

    # Determine dt for real-time simulation option
    sim_dt = physics_dt * decimation

    print("[INFO] Starting simulation loop...")
    with torch.inference_mode():
        for i in range(num_steps):
            step_start_time = time.time()
            if i % 100 == 0:  # Print progress less frequently
                progress = i / num_steps * 100
                print(
                    f"  Step {i}/{num_steps} ({progress:.1f}%) - Episodes Completed: {total_episodes_completed}"
                )

            # --- Log instantaneous data for violation checks ---
            # Use try-except blocks for robustness if attributes might be missing
            try:
                # Access measurements directly from the unwrapped env's buffer or methods
                # Adapt these lines based on where the data resides in your specific env
                if hasattr(env.unwrapped, "m545_measurements"):
                    meas = env.unwrapped.m545_measurements
                    bucket_aoa[:, i] = meas.bucket_aoa
                    bucket_vel_norm[:, i] = torch.linalg.norm(meas.bucket_vel_w, dim=-1)
                    base_vel_norm[:, i] = torch.linalg.norm(meas.root_lin_vel_w, dim=-1)
                    bucket_pos_w = meas.bucket_pos_w
                    bucket_x[:, i] = bucket_pos_w[:, 0]
                    bucket_z[:, i] = bucket_pos_w[:, 2]
                else:
                    # Handle cases where measurements are stored differently
                    # Example: bucket_vel_norm[:, i] = env.unwrapped.data.bucket_vel_norm_buffer ...
                    print(
                        "[WARNING] 'm545_measurements' not found, skipping some data logging."
                    )
                    bucket_pos_w = None  # Ensure bucket_pos_w is defined

                if hasattr(env.unwrapped, "soil") and bucket_pos_w is not None:
                    soil = env.unwrapped.soil
                    max_depth_limit[:, i] = soil.get_max_depth_height_at_pos(
                        bucket_pos_w[:, :1]
                    ).squeeze(
                        -1
                    )  # Ensure squeeze removes last dim
                    in_soil[:, i] = (soil.bucket_depth > 0.0).squeeze(-1)
                else:
                    print(
                        "[WARNING] 'soil' module or bucket position not available, skipping soil-related logging."
                    )

                if hasattr(env.unwrapped, "pullup_dist"):
                    pullup_limit[:, i] = (
                        env.unwrapped.pullup_dist
                    )  # Assuming this is the limit, not the current value
                else:
                    print("[WARNING] 'pullup_dist' not found, skipping pullup logging.")

                # Log joint velocities if needed
                # if hasattr(env.unwrapped, 'joint_vel'):
                #    joint_vel[:, i] = env.unwrapped.joint_vel

            except AttributeError as e:
                print(
                    f"[ERROR] Data logging failed at step {i}: {e}. Filling with NaNs."
                )
                # Fill current step with NaN or zeros to avoid crashing analysis later
                bucket_aoa[:, i] = torch.nan
                bucket_vel_norm[:, i] = torch.nan
                # ... fill other logged tensors ...
                in_soil[:, i] = False  # Default to False if data is missing

            # --- Agent and Environment Step ---
            actions = policy(obs)
            obs, rewards, dones, infos = env.step(actions)  # Use VecEnv's step
            ep_lens += 1

            # --- Log Termination Data ---
            # Check if 'terminations' key exists and is not empty
            if dones.any() and i >= args_cli.warmup_steps:
                done_ids = torch.where(dones)[0]  # Get indices of finished envs

                # Safely access termination info from 'infos' dictionary
                # The exact keys depend on how RslRlVecEnvWrapper passes info
                term_manager = getattr(env.unwrapped, "termination_excavation", None)

                if term_manager:
                    # Timeouts
                    timed_out_mask = term_manager.time_out_buf[done_ids]
                    ep_len_counts["timeout"].extend(
                        ep_lens[done_ids][timed_out_mask].tolist()
                    )

                    # Negative Terminations
                    for name in neg_term_names:
                        neg_term_mask = term_manager.episode_neg_term_buf[name][
                            done_ids
                        ]
                        ep_len_counts["neg_" + name].extend(
                            ep_lens[done_ids][neg_term_mask].tolist()
                        )

                    # Positive Terminations (assuming keys match pos_term_names)
                    for name in pos_term_names:
                        # Use .get() for safety if a positive termination type might not occur
                        term_key = name  # Assuming the key in episode_pos_term_buf matches the name
                        # Get the correct buffer based on the termination name
                        if name == "desired_full":
                            buffer = term_manager.full_pos_term_buf
                        elif name == "desired_close":
                            buffer = term_manager.close_pos_term_buf
                        elif name == "desired_partial":
                            buffer = term_manager.partial_pos_term_buf
                        else:
                            # Handle unexpected positive termination names if necessary
                            print(
                                f"Warning: Unrecognized positive termination name: {name}"
                            )
                            continue  # Skip or handle appropriately

                        pos_term_mask = buffer[done_ids]
                        ep_len_counts[name].extend(
                            ep_lens[done_ids][pos_term_mask].tolist()
                        )

                else:
                    # Fallback if termination manager is not directly accessible
                    # Requires inspecting the 'infos' structure from RslRlVecEnvWrapper
                    # This part might need adjustment based on VecEnvWrapper's info structure
                    if "_separate_env_infos" in infos:
                        for idx, done_id in enumerate(done_ids):
                            env_info = infos["_separate_env_infos"][done_id.item()]
                            if env_info.get("TimeLimit.truncated", False):
                                ep_len_counts["timeout"].append(ep_lens[done_id].item())
                            # Add logic here to parse other termination reasons from env_info if available
                            # else: check env_info for other termination keys...

                    else:
                        print(
                            "[WARNING] Cannot determine specific termination cause. Logging only total dones."
                        )

                total_episodes_completed += len(done_ids)
                ep_lens[done_ids] = 0  # Reset lengths for finished envs

            # --- Real-time delay ---
            if args_cli.real_time:
                elapsed_time = time.time() - step_start_time
                sleep_time = sim_dt - elapsed_time
                if sleep_time > 0:
                    time.sleep(sleep_time)

    print(
        f"[INFO] Simulation finished. Total episodes completed: {total_episodes_completed}"
    )
    if total_episodes_completed == 0:
        print(
            "[WARNING] No episodes completed during the simulation. Cannot generate statistics."
        )
        env.close()
        return

    # --- Process and Save Results ---
    pdf_filename = os.path.join(bench_log_dir, "stats_report.pdf")
    print(f"[INFO] Generating PDF report: {pdf_filename}")
    with PdfPages(pdf_filename) as pdf:

        # --- Termination Statistics per Asset (if applicable) ---
        # This requires the environment's 'infos' to contain asset-specific termination counts
        # Adapt keys ('terminations_by_asset_count', etc.) based on your env's info structure
        all_data_to_serialize = []
        # Combine all potential termination keys for consistent ordering
        all_term_keys = ["timeout"] + pos_term_names + neg_term_names
        if "terminations_by_asset_count" in infos:  # Check if asset info is available
            print("[INFO] Plotting termination stats per asset...")
            asset_ids = list(infos.get("terminations_by_asset_count", {}).keys())

            for asset_id in asset_ids:
                data_to_serialize = {"asset_id": asset_id, "counts": {}}
                values = []
                labels = []

                # Collect counts for this asset using .get() for safety
                term_counts = infos.get("terminations_by_asset_count", {}).get(
                    asset_id, {}
                )
                pos_term_counts = infos.get(
                    "positive_terminations_by_asset_count", {}
                ).get(asset_id, {})
                timeout_count = infos.get("timeout_by_asset_count", {}).get(asset_id, 0)

                combined_counts = {"timeout": timeout_count}
                combined_counts.update(pos_term_counts)  # Add positive counts
                combined_counts.update(
                    term_counts
                )  # Add negative counts (might overwrite if names overlap)

                for key in all_term_keys:
                    count = combined_counts.get(key, 0)
                    # Ensure count is a scalar number
                    if isinstance(count, torch.Tensor):
                        count = count.sum().item()  # Sum if it's a tensor per env
                    elif not isinstance(count, (int, float)):
                        count = 0  # Default to 0 if type is unexpected

                    if count > 0:  # Only plot/report terminations that occurred
                        values.append(count)
                        labels.append(key)
                        data_to_serialize["counts"][key] = count

                all_data_to_serialize.append(data_to_serialize)

                # Plotting
                if values:  # Only plot if there's data for this asset
                    total_terminations_asset = sum(values)
                    percentages = [100.0 * v / total_terminations_asset for v in values]

                    fig, ax = plt.subplots(figsize=(12, 8))
                    bars = ax.bar(
                        np.arange(len(values)),
                        values,
                        tick_label=labels,
                        color="skyblue",
                    )
                    plt.xticks(rotation=45, ha="right", fontsize=10)
                    plt.yticks(fontsize=10)
                    ax.set_ylabel("Count", fontsize=12)
                    # Add percentages on bars
                    for bar, percentage in zip(bars, percentages):
                        height = bar.get_height()
                        ax.text(
                            bar.get_x() + bar.get_width() / 2.0,
                            height,
                            f"{percentage:.1f}%",
                            ha="center",
                            va="bottom",
                            fontsize=9,
                        )

                    ax.set_title(
                        f"Termination Distribution for Asset {asset_id} (Total: {total_terminations_asset})",
                        fontsize=14,
                    )
                    ax.grid(axis="y", linestyle="--", alpha=0.7)
                    plt.tight_layout()
                    pdf.savefig(fig)
                    plt.close(fig)
                else:
                    print(f"[INFO] No termination data recorded for asset {asset_id}.")

            # Save per-asset data to JSON
            all_data_filepath = os.path.join(
                bench_log_dir, "terminations_by_asset.json"
            )
            with open(all_data_filepath, "w") as file:
                json.dump(all_data_to_serialize, file, indent=4)
            print(f"[INFO] Saved per-asset termination counts to: {all_data_filepath}")

        else:
            print(
                "[INFO] 'terminations_by_asset_count' not found in infos. Skipping per-asset analysis."
            )

        # --- Overall Termination Distribution ---
        print("[INFO] Plotting overall termination distribution...")
        values = []
        labels = []
        term_manager = getattr(env.unwrapped, "termination_excavation", None)
        total_term_counts = {}

        if term_manager:
            # Collect counts directly from the manager's final state
            total_term_counts["timeout"] = term_manager.time_out_count.sum().item()
            for name in neg_term_names:
                total_term_counts[name] = (
                    term_manager.episode_neg_term_counts.get(name, torch.tensor(0))
                    .sum()
                    .item()
                )
            for name in pos_term_names:
                total_term_counts[name] = (
                    term_manager.episode_pos_term_counts.get(name, torch.tensor(0))
                    .sum()
                    .item()
                )

        else:
            # Fallback: Estimate from ep_len_counts (less accurate if episodes finish before full length)
            print(
                "[WARNING] Estimating overall counts from episode length logs (may be less accurate)."
            )
            total_term_counts["timeout"] = len(ep_len_counts.get("timeout", []))
            for name in neg_term_names:
                total_term_counts[name] = len(ep_len_counts.get("neg_" + name, []))
            for name in pos_term_names:
                total_term_counts[name] = len(ep_len_counts.get(name, []))

        # Prepare for plotting
        for key in all_term_keys:
            count = total_term_counts.get(key, 0)
            if count > 0:  # Only include terminations that occurred
                values.append(count)
                labels.append(key)

        # Plot overall distribution
        if values:
            percentages = [100.0 * v / total_episodes_completed for v in values]
            fig, ax = plt.subplots(figsize=(12, 8))
            bars = ax.bar(
                np.arange(len(values)), percentages, tick_label=labels, color="salmon"
            )  # Plot percentages
            plt.xticks(rotation=45, ha="right", fontsize=10)
            plt.yticks(fontsize=10)
            ax.set_ylabel("Percentage of Total Episodes (%)", fontsize=12)
            # Add percentages on bars
            for bar in bars:
                height = bar.get_height()
                ax.text(
                    bar.get_x() + bar.get_width() / 2.0,
                    height,
                    f"{height:.1f}%",
                    ha="center",
                    va="bottom",
                    fontsize=9,
                )

            # Calculate summary stats for title
            sum_pos_term_count = sum(
                total_term_counts.get(name, 0) for name in pos_term_names
            )
            title_str = f"Overall Termination Distribution (Total Episodes: {total_episodes_completed})\n"
            pos_term_details = []
            for name in pos_term_names:
                count = total_term_counts.get(name, 0)
                if total_episodes_completed > 0:
                    perc = 100.0 * count / total_episodes_completed
                    pos_term_details.append(f"{name}: {perc:.1f}% ({count})")
                else:
                    pos_term_details.append(f"{name}: N/A ({count})")

            pos_summary_perc = (
                (100.0 * sum_pos_term_count / total_episodes_completed)
                if total_episodes_completed > 0
                else 0.0
            )
            title_str += f"Positive Terms: {' | '.join(pos_term_details)} | Total Positive: {pos_summary_perc:.1f}% ({sum_pos_term_count})"

            ax.set_title(title_str, fontsize=14)
            ax.grid(axis="y", linestyle="--", alpha=0.7)
            plt.tight_layout()
            pdf.savefig(fig)
            plt.close(fig)

            # Print overall stats to console
            print("\n--- Overall Termination Statistics ---")
            print(f"Total Episodes Completed: {total_episodes_completed}")
            for label, count in zip(labels, values):
                perc = (
                    (100.0 * count / total_episodes_completed)
                    if total_episodes_completed > 0
                    else 0.0
                )
                print(f"  {label:<20}: {count:<8} ({perc:>5.1f}%)")
            print("-" * 40)

        else:
            print("[INFO] No overall termination data to plot.")

        # --- Violation Analysis ---
        print(
            "\n--- Violation Analysis (Checking Conditions Even if Termination Disabled) ---"
        )
        violation_data = {}  # Store raw violation values for boxplots/analysis

        def log_and_analyze_violations(name, values, limit, comparison, violation_data):
            """Helper to find, log, and store violation data."""
            if values.numel() == 0:  # Handle empty tensors if logging failed
                print(f"{name}: No data recorded.")
                return

            # Find where the violation occurs based on the comparison type
            if comparison == "greater":
                violated_ids = torch.where(values > limit)
                if torch.is_tensor(limit) and limit.dim() > 0:
                    limit_vals = limit[violated_ids]
                else:
                    limit_vals = limit
                overshoot = (values[violated_ids] - limit_vals).cpu().numpy()
            elif comparison == "less":
                violated_ids = torch.where(values < limit)
                if torch.is_tensor(limit) and limit.dim() > 0:
                    limit_vals = limit[violated_ids]
                else:
                    limit_vals = limit
                undershoot = (
                    (limit_vals - values[violated_ids]).cpu().numpy()
                )  # Positive value indicates violation margin
                overshoot = undershoot  # Use consistent variable name for storage
            else:
                print(f"Unknown comparison type for {name}")
                return

            num_violations = len(overshoot)
            total_data_points = (
                values.numel()
            )  # Total steps * num_envs for this variable
            violation_rate = (
                (num_violations / total_data_points) * 100.0
                if total_data_points > 0
                else 0.0
            )

            if num_violations > 0:
                violation_data[name] = overshoot.tolist()  # Store raw violation margins
                print(
                    "{:<25} Violations: {:<8} ({:<6.2f}%) | Mean Viol: {:<7.2f} | Std Dev: {:<7.2f} | Min Viol: {:<7.2f} | Max Viol: {:<7.2f}".format(
                        name,
                        num_violations,
                        violation_rate,
                        np.mean(overshoot),
                        np.std(overshoot) if num_violations > 1 else 0.0,
                        np.min(overshoot),
                        np.max(overshoot),
                    )
                )
            else:
                violation_data[name] = []  # Store empty list if no violations
                print(f"{name:<25} Violations: 0 (0.00%)")

        # Analyze each potential negative termination condition
        term_cfg = getattr(env_cfg, "terminations_excavation", None)
        if term_cfg:
            # Bucket AoA (Violates if < 0 while moving fast enough in soil)
            aoa_limit = 0.0
            vel_thresh = getattr(
                term_cfg, "bucket_vel_aoa_threshold", 0.05
            )  # Get threshold from config
            # Find steps where AoA is bad AND velocity is high AND in soil
            condition_mask = torch.logical_and(in_soil, bucket_vel_norm > vel_thresh)
            violating_steps_aoa = bucket_aoa[condition_mask]  # Filter AoA values first
            # Now check where these filtered values are below the limit
            log_and_analyze_violations(
                "bucket_aoa (<0)",
                violating_steps_aoa,
                aoa_limit,
                "less",
                violation_data,
            )

            # Bucket Velocity
            limit = getattr(term_cfg, "max_bucket_vel", 1.0)
            log_and_analyze_violations(
                "bucket_vel", bucket_vel_norm, limit, "greater", violation_data
            )

            # Base Velocity
            limit = getattr(term_cfg, "max_base_vel", 0.5)
            log_and_analyze_violations(
                "base_vel", base_vel_norm, limit, "greater", violation_data
            )

            # Max Depth (Bucket Z < Max Depth Limit - Overshoot Allowance)
            overshoot_allowance = getattr(term_cfg, "max_depth_overshoot", 0.05)
            # Limit here is the *allowed minimum Z coordinate*
            depth_violation_limit = max_depth_limit - overshoot_allowance
            log_and_analyze_violations(
                "max_depth (z<limit)",
                bucket_z,
                depth_violation_limit,
                "less",
                violation_data,
            )

            # Pullup (Bucket X < Pullup Distance Limit)
            # Assumes pullup_limit stores the minimum allowed X
            log_and_analyze_violations(
                "pullup (x<limit)", bucket_x, pullup_limit, "less", violation_data
            )

            # Add checks for other conditions like joint velocity limits, spilling etc. if needed
            # e.g., Joint Velocity
            # if hasattr(term_cfg, 'max_joint_vel'):
            #    joint_vel_limit = term_cfg.max_joint_vel # Assuming a single limit or needs per-joint handling
            #    # Check absolute joint velocities against the limit
            #    abs_joint_vel = torch.abs(joint_vel_buffer) # Assuming joint_vel_buffer is populated
            #    log_and_analyze_violations("joint_vel", abs_joint_vel, joint_vel_limit, 'greater', violation_data)

        else:
            print(
                "[WARNING] 'terminations_excavation' config not found. Skipping violation analysis."
            )

        # --- Episode Length Analysis ---
        print("\n--- Episode Length Statistics (steps) ---")
        all_ep_lens = []
        for key, lengths_deque in ep_len_counts.items():
            lengths = list(lengths_deque)
            if lengths:
                all_ep_lens.extend(lengths)
                violation_data[f"ep_len_{key}"] = lengths  # Store lengths for plotting
                print(
                    "{:<25} Count: {:<8} | Mean: {:<7.1f} | Std Dev: {:<7.1f} | Min: {:<7.0f} | Max: {:<7.0f}".format(
                        f"Ep. Len ({key})",
                        len(lengths),
                        statistics.mean(lengths),
                        statistics.stdev(lengths) if len(lengths) > 1 else 0.0,
                        min(lengths),
                        max(lengths),
                    )
                )
            else:
                violation_data[f"ep_len_{key}"] = []
                print(f"{f'Ep. Len ({key})':<25} Count: 0")

        # Overall episode length stats
        if all_ep_lens:
            print(
                "{:<25} Count: {:<8} | Mean: {:<7.1f} | Std Dev: {:<7.1f} | Min: {:<7.0f} | Max: {:<7.0f}".format(
                    "Ep. Len (Overall)",
                    len(all_ep_lens),
                    statistics.mean(all_ep_lens),
                    statistics.stdev(all_ep_lens) if len(all_ep_lens) > 1 else 0.0,
                    min(all_ep_lens),
                    max(all_ep_lens),
                )
            )
        else:
            print("Ep. Len (Overall): No episodes completed.")

        # --- Plot Boxplots for Violations and Episode Lengths ---
        print("[INFO] Plotting boxplots for violations and episode lengths...")
        plot_keys = sorted(
            [k for k, v in violation_data.items() if v]
        )  # Only plot keys with data

        if plot_keys:
            num_plots = len(plot_keys)
            fig, axes = plt.subplots(
                num_plots, 1, figsize=(10, 5 * num_plots), squeeze=False
            )  # Create subplots

            for idx, key in enumerate(plot_keys):
                value = violation_data[key]
                ax = axes[idx, 0]  # Get the current axis

                ax.boxplot(value, vert=True, showfliers=True)  # Show outliers
                ax.set_xticklabels(
                    []
                )  # No need for x-tick labels on individual boxplots
                ax.tick_params(
                    axis="x", which="both", bottom=False, top=False
                )  # Hide x-axis ticks

                # Calculate stats for title
                if len(value) > 0:
                    quantiles = np.quantile(value, [0.25, 0.5, 0.75])
                    q_str = " | ".join([f"{q:.2f}" for q in quantiles])
                    if "ep_len" in key:
                        # For episode lengths, use total_episodes_completed for percentage
                        num_occurrences = len(value)
                        total_base = total_episodes_completed
                        perc = (
                            (100.0 * num_occurrences / total_base)
                            if total_base > 0
                            else 0.0
                        )
                        title = f"{key}: Count: {num_occurrences} ({perc:.1f}% of Eps) | Q1/Med/Q3: {q_str}"
                    else:
                        # For violations, use total data points for percentage
                        num_occurrences = len(value)
                        # Estimate total data points for the original variable
                        # This is an approximation as it depends on which variable 'key' corresponds to
                        approx_total_points = args_cli.num_envs * num_steps
                        perc = (
                            (100.0 * num_occurrences / approx_total_points)
                            if approx_total_points > 0
                            else 0.0
                        )
                        title = f"{key}: Violations: {num_occurrences} ({perc:.2e}% of Steps) | Q1/Med/Q3: {q_str}"

                    ax.set_title(title, fontsize=11)
                    # Adjust y-axis limits slightly for better visibility
                    buffer = (
                        (max(value) - min(value)) * 0.05
                        if max(value) != min(value)
                        else 0.5
                    )
                    ax.set_ylim(
                        [min(value) - buffer - 1e-6, max(value) + buffer + 1e-6]
                    )  # Add small epsilon
                else:
                    ax.set_title(f"{key}: No data", fontsize=11)
                    ax.set_ylim([0, 1])  # Default limits

                ax.grid(axis="y", linestyle="--", alpha=0.7)

            plt.tight_layout(
                rect=[0, 0.03, 1, 0.98]
            )  # Adjust layout to prevent title overlap
            pdf.savefig(fig)  # Save the figure with all boxplots
            plt.close(fig)
        else:
            print(
                "[INFO] No violation or episode length data recorded. Skipping boxplots."
            )

    print(f"[INFO] Benchmarking complete. Report saved to: {pdf_filename}")
    env.close()


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print("\n[ERROR] An exception occurred during benchmarking:")
        carb.log_error(str(e))
        carb.log_error(traceback.format_exc())
        # Optional: re-raise the exception after logging
        # raise
    finally:
        # Close the simulator app cleanly
        if simulation_app:
            simulation_app.close()
        print("[INFO] Simulation application closed.")

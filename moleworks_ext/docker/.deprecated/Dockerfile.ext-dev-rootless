# Use the basic isaaclab image as the base
FROM isaac-lab-base AS base

# =========================
# Build Arguments and ENV
# =========================
ARG EXTENSION_NAME_ARG
ARG EXT_PATH_ARG
ARG DOCKER_EXT_PATH_ARG
# We keep these args for compatibility; they aren't used for switching users
ARG DOCKER_USER_NAME_ARG
ARG DOCKER_USER_HOME_ARG

ENV EXT_PATH=${EXT_PATH_ARG} \
    EXTENSION_NAME=${EXTENSION_NAME_ARG} \
    DOCKER_EXT_PATH=${DOCKER_EXT_PATH_ARG} \
    HOME=/root

# =========================
# Install System Dependencies
# =========================
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    figlet \
    apt-utils \
    libeigen3-dev \
    locate \
    wget \
    pkg-config \
    dialog \
    tasksel \
    curl \
    python3-pip \
    git \
    git-lfs \
    rsync \
    software-properties-common

# =========================
# Configure Git LFS
# =========================
RUN git lfs install

# =========================
# Setup Extension Directories
# =========================
RUN mkdir -p ${DOCKER_EXT_PATH}/data ${DOCKER_EXT_PATH}/logs && \
    chmod -R 777 ${DOCKER_EXT_PATH}


# =========================
# Copy and Install moleworks_ext
# =========================
COPY --chown=root:root source/moleworks_ext ${DOCKER_EXT_PATH}/source/moleworks_ext
RUN --mount=type=cache,target=/root/.cache/pip \
    cd ${DOCKER_EXT_PATH} && \
    ${ISAACLAB_PATH}/isaaclab.sh -p -m pip install -e source/moleworks_ext

# =========================
# Clean Up moleworks_ext Directory
# =========================
RUN rm -rf ${DOCKER_EXT_PATH}/source/moleworks_ext

# =========================
# Create Symlinks for Sensors
# =========================
RUN mkdir -p /workspace/isaaclab/source/exts/ && \
    ln -s /workspace/isaaclab/_isaac_sim/exts/isaacsim.sensors.rtx /workspace/isaaclab/source/exts/isaacsim.sensors.rtx

# =========================
# Clone and Install rsl_rl
# =========================
RUN git clone https://github.com/leggedrobotics/rsl_rl.git /tmp/rsl_rl && \
    cd /tmp/rsl_rl && \
    ${ISAACLAB_PATH}/isaaclab.sh -p -m pip install .

# =========================
# Python Packages Installation
# =========================
RUN ${ISAACLAB_PATH}/_isaac_sim/python.sh -m pip install warp-lang ruamel.yaml

# =========================
# Install Node.js and Claude Code
# =========================
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    npm config set registry https://registry.npmjs.org/ && \
    npm install -g @anthropic-ai/claude-code && \
    rm -rf /var/lib/apt/lists/*

# =========================
# Set Ownership and Permissions
# =========================
RUN chown -R root:root /isaac-sim/kit && \
    chmod -R 777 /isaac-sim/kit

# =========================
# Environment Setup
# =========================
# Copy a bashrc for root
COPY docker/bashrc /root/.bashrc
RUN chmod a+rwx /root/.bashrc

# Use a modified entrypoint that does not switch user
COPY docker/entrypoint_root.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# =========================
# Set Up Python Aliases
# =========================
RUN echo "alias python3='${ISAACLAB_PATH}/_isaac_sim/python.sh'" >> /root/.bashrc && \
    echo "alias python='${ISAACLAB_PATH}/_isaac_sim/python.sh'" >> /root/.bashrc

# =========================
# Final Configuration
# =========================
WORKDIR /root
ENTRYPOINT ["/entrypoint.sh"]

# Docker Compose Override Template
# This file is auto-generated by mount_config.py based on .mount.config
# DO NOT EDIT THIS FILE DIRECTLY - use ./container.sh mount-* commands
#
# This template shows the structure of the generated override file
# Actual content will be populated based on your mount configuration

services:
  # Each service can have additional volumes added based on mount config
  # The mount_config.py script will populate these dynamically
  
  isaac-lab-ext:
    volumes: []
      # Example mounts that would be added when enabled:
      # - type: bind
      #   source: /path/to/isaaclab/source
      #   target: /workspace/isaaclab/source
      #   read_only: false
      # - type: bind
      #   source: /path/to/rsl_rl
      #   target: /workspace/isaaclab/_isaac_sim/kit/python/lib/python3.10/site-packages/rsl_rl
      #   read_only: false

  isaac-lab-ext-dev:
    volumes: []

  isaac-lab-ext-dev-rootless:
    volumes: []
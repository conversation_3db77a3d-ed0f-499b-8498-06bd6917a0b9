#!/usr/bin/env bash

# in the case you need to load specific modules on the cluster, add them here
module load eth_proxy

# Multi-GPU configuration - these variables are set by submit_multi_gpu_job.sh
# For single GPU training, set NUM_GPUS=1
# For multi-GPU training, set NUM_GPUS to desired number (2, 4, 8, etc.)
NUM_GPUS=${NUM_GPUS:-2}
GPU_TYPE=${GPU_TYPE:-"rtx_4090"}
#GPU_TYPE=${GPU_TYPE:-"a100_80gb"}

# Use pre-calculated resources from submit_multi_gpu_job.sh if available
# Otherwise fall back to simple calculation for backward compatibility
if [[ -n "$TOTAL_CPUS" && -n "$TOTAL_MEM" ]]; then
    echo "Using pre-calculated resources from submit_multi_gpu_job.sh"
    echo "Configuring SLURM job for ${NUM_GPUS} GPU(s) of type ${GPU_TYPE}"
    echo "Total CPUs: ${TOTAL_CPUS}, Total Memory: ${TOTAL_MEM}GB"
else
    echo "Using fallback resource calculation"
    CPUS_PER_GPU=${CPUS_PER_GPU:-8}
    MEM_PER_CPU=${MEM_PER_CPU:-16384}
    TOTAL_CPUS=$((NUM_GPUS * CPUS_PER_GPU))
    TOTAL_MEM=$((NUM_GPUS * CPUS_PER_GPU * MEM_PER_CPU / 1024))  # Convert to GB
    echo "Configuring SLURM job for ${NUM_GPUS} GPU(s) of type ${GPU_TYPE}"
    echo "Total CPUs: ${TOTAL_CPUS}, Total Memory: ${TOTAL_MEM}GB"
fi

# Calculate CPUs per task and memory per CPU for SLURM
CPUS_PER_TASK=$((TOTAL_CPUS / NUM_GPUS))
MEM_PER_CPU=$((TOTAL_MEM * 1024 / TOTAL_CPUS))  # Convert GB to MB and divide by total CPUs

# create job script with compute demands
cat <<EOT > job.sh
#!/bin/bash

#SBATCH --nodes=1                   # Use single node for multi-GPU training
#SBATCH --ntasks=${NUM_GPUS}        # Number of tasks (one per GPU)
#SBATCH --cpus-per-task=${CPUS_PER_TASK}  # CPUs per GPU for I/O handling
#SBATCH --gpus-per-node=${GPU_TYPE}:${NUM_GPUS}  # Request GPUs per node
#SBATCH --time=23:00:00
#SBATCH --mem-per-cpu=${MEM_PER_CPU}M  # Memory per CPU in MB
#SBATCH --mail-type=END
#SBATCH --mail-user=name@mail
#SBATCH --job-name="training-${NUM_GPUS}gpu-$(date +"%Y-%m-%dT%H:%M")"

# Set environment variables for multi-GPU training
export MASTER_ADDR=\$(hostname)
export MASTER_PORT=29500
export WORLD_SIZE=${NUM_GPUS}

# Set GPU optimization environment variables
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:1024
export CUDA_LAUNCH_BLOCKING=0
export TORCH_CUDA_ARCH_LIST="8.0"  # A100 compute capability
export NCCL_DEBUG=INFO  # Enable NCCL debugging for multi-GPU

# For single GPU, set CUDA_VISIBLE_DEVICES
if [ ${NUM_GPUS} -eq 1 ]; then
    export CUDA_VISIBLE_DEVICES=0
    export LOCAL_RANK=0
    export RANK=0
    echo "Single GPU training mode"
    # Pass the container profile first to run_singularity.sh, then all arguments intended for the executed script
    bash "$1/docker/cluster/run_singularity.sh" "$1" "$2" "${@:3}"
else
    echo "Multi-GPU training mode with ${NUM_GPUS} GPUs"
    # For multi-GPU, use srun to launch distributed training
    srun --ntasks=${NUM_GPUS} --ntasks-per-node=${NUM_GPUS} bash -c '
        export LOCAL_RANK=\$SLURM_LOCALID
        export RANK=\$SLURM_PROCID
        export CUDA_VISIBLE_DEVICES=\$LOCAL_RANK
        echo "Process \$RANK on GPU \$LOCAL_RANK"
        bash "$1/docker/cluster/run_singularity.sh" "$1" "$2" "${@:3}"
    '
fi
EOT

sbatch < job.sh
rm job.sh
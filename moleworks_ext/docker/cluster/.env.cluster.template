###
# Cluster specific settings
###

# Job scheduler used by cluster.
# Currently supports PBS and SLURM
CLUSTER_USER=lterenzi
EXTENSION_NAME=moleworks_ext   

CLUSTER_JOB_SCHEDULER=SLURM
# Docker cache dir for <PERSON> Sim (has to end on docker-isaac-sim)
# e.g. /cluster/scratch/$USER/docker-isaac-sim
CLUSTER_ISAAC_SIM_CACHE_DIR=/cluster/scratch/$CLUSTER_USER/docker-isaac-sim
# Main cluster directory for moleworks_ext
CLUSTER_ISAACLAB_DIR=/cluster/home/<USER>/$EXTENSION_NAME
# Cluster login
CLUSTER_LOGIN=$<EMAIL>
# Cluster scratch directory to store the SIF file
# e.g. /cluster/scratch/$USER
CLUSTER_SIF_PATH=/cluster/work/rsl/$CLUSTER_USER
# Remove the temporary isaaclab code copy after the job is done
REMOVE_CODE_COPY_AFTER_JOB=false
# Python executable within Isaac Lab directory to run with the submitted job
CLUSTER_PYTHON_EXECUTABLE=scripts/rl/rsl_rl/train.py

# External Codebase Mounting
# External mounts are now configured via the unified mount system.
# Before submitting cluster jobs, configure mounts locally:
#   cd docker
#   ./container.sh mount-setup
# The .mount.config file will be automatically synced to the cluster.

# WANDB (optional)
WANDB_API_KEY="****************************************"
WANDB_USERNAME="idate96"
# Set to "disabled" to disable WANDB logging, or "online" to enable
WANDB_MODE="online" 
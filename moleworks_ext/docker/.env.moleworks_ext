# =========================
# Extension Configuration
# =========================
EXTENSION_NAME=moleworks_ext
EXT_PATH=/home/<USER>/git/moleworks_ext
DOCKER_EXT_PATH=/workspace/$EXTENSION_NAME

# =========================
# Docker User Configuration
# =========================
DOCKER_USER_NAME=root
DOCKER_USER_HOME=/root

# =========================
# Isaac Sim Configuration
# =========================
DOCKER_ISAACSIM_ROOT_PATH=/isaac-sim

# =========================
# Built-in IsaacLab Configuration
# =========================
# The built-in IsaacLab is always at /workspace/isaaclab
DOCKER_ISAACLAB_PATH=/workspace/isaaclab

# =========================
# External Codebase Mounting
# =========================
# External mounts are now configured via the unified mount system.
# Run './container.sh mount-setup' to configure optional mounts for IsaacLab and RSL-RL.

# =========================
# NVIDIA Configuration
# =========================
ACCEPT_EULA=Y

# =========================
# WANDB Configuration (Optional)
# =========================
WANDB_API_KEY="****************************************"
WANDB_USERNAME="mkrause8-eth-z-rich"
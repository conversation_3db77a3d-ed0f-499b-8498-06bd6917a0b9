# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is **moleworks_ext**, an IsaacLab extension for robotics simulation and reinforcement learning training of Menzi excavators (M545, M445 models). It provides simulation environments for excavation, navigation, material handling, and boulder manipulation tasks.

## Common Commands

### Training and Testing
```bash
# Train RL agent
python scripts/rl/rsl_rl/train.py --task=<PERSON>-m545-digging --num_envs 1024

# Test environment
python scripts/rl/rsl_rl/zero_agent.py --task=Isaac-m545-digging

# List available environments
python scripts/list_envs.py

# Run gamepad control
python scripts/mole_environments/excavation/gamepad.py

# Standalone simulation
python scripts/standalone/mole_sim.py
```

### Cluster Deployment
```bash
# Build and push to cluster
cd docker/cluster
./cluster_interface.sh push moleworks_ext

# Submit training job
./cluster_interface.sh job moleworks_ext --task <PERSON>-m545-digging --num_envs 64000

# Sync logs from cluster
./sync_experiments.sh --remove ~/experiments/logs
```

## Architecture Overview

### Core Structure

**Tasks** (`source/moleworks_ext/moleworks_ext/tasks/`):
- `excavation/`: Traditional soil excavation environments with M545 excavator
- `single_boulder_excavation/`: Boulder manipulation tasks with custom physics
- `ugep/`: Universal Generalized Excavation Protocol with scaling laws
- `driving/`: Navigation and terrain traversal
- `sim/`: Basic simulation environment for robot spawning and measurements

**Common Components** (`source/moleworks_ext/moleworks_ext/common/`):
- `actions/`: Joint and task-space control actions
- `sensors/`: RTX LiDAR, precise contact sensors, ray casters
- `controllers/`: PID, inverse dynamics, trajectory controllers
- `utils/`: M545 measurements, limits, multi-object spawning

**ROS Integration** (`source/moleworks_ext/moleworks_ext/ros/`):
- Publishers/subscribers for robot state streaming
- ROS message interfaces for real robot deployment
- Eval simulation for real-sim transfer

**Resources** (`source/moleworks_ext/moleworks_ext/rsc/`):
- USD models for M545/M445 excavators
- Boulder assets and terrain models
- Material definitions and textures

### Task Environment Pattern

Each task follows this structure:
1. **Environment Class**: Inherits from `ManagerBasedRLEnv` (e.g., `excavation_env.py`)
2. **Configuration**: Main config in `env_cfg/` with robot, sensor, reward settings
3. **MDP Components**: Observations, rewards, terminations in `mdp/`
4. **Agent Config**: RSL-RL agent configuration in `agents/`
5. **Tests**: Environment validation in `tests/`
6. **Utils**: Task-specific utilities and measurements

### Key Environment Configs

- **excavation**: `source/moleworks_ext/moleworks_ext/tasks/excavation/env_cfg/m545_env_cfg.py`
- **boulder**: `source/moleworks_ext/moleworks_ext/tasks/single_boulder_excavation/env_cfg/m545_single_boulder_excavation_cfg_raycast_w_shovel.py`
- **ugep**: `source/moleworks_ext/moleworks_ext/tasks/ugep/ugep_env_cfg.py`
- **driving**: `source/moleworks_ext/moleworks_ext/tasks/driving/env_cfg/m545_driving_env_cfg.py`

### Specialized Components

**Soil Simulation**:
- Physics-based soil models in `tasks/excavation/soil_model/`
- 2D soil height fields and force calculations
- SSP (Specific Surface Potential) soil parameters

**Boulder Physics**:
- Multi-asset boulder spawning system
- Ellipsoid fitting for boulder measurements
- Custom ray casting for bucket-boulder interaction

**UGEP System**:
- Scaling laws for different excavator models
- GMM models for excavation prediction
- Universal excavation protocol across machine types

## Development Guidelines

### Adding New Tasks

1. Create task directory in `source/moleworks_ext/moleworks_ext/tasks/your_task/`
2. Implement environment class inheriting from `ManagerBasedRLEnv`
3. Define configuration in `env_cfg/` with robot, sensors, rewards
4. Create MDP components in `mdp/` (observations, rewards, terminations)
5. Add agent configuration in `agents/`
6. Register task in `source/moleworks_ext/moleworks_ext/tasks/__init__.py`

### Environment Registration

Tasks are registered via gym registration in task `__init__.py` files:
```python
gym.register(
    id="Isaac-TaskName-v0",
    entry_point="moleworks_ext.tasks.task_name:TaskEnvCfg",
    disable_env_checker=True,
)
```

### Testing Workflow

1. **Zero Agent Test**: Verify environment loads and runs
2. **Reset Test**: Ensure proper environment reset behavior
3. **PID Tuning**: Validate controller parameters
4. **Gamepad Control**: Manual validation via gamepad
5. **Benchmark**: Performance and stability testing

### ROS Bridge Usage

The ROS bridge enables real robot deployment:
- Configure publishers/subscribers in `ros/ros_manager/`
- Define message interfaces in `ros/ros2_ws/src/m545_interfaces/`
- Use eval_sim for real-sim validation

### Cluster Training

- Built-in cluster management in `docker/cluster/`
- Self-contained Isaac Lab installation in containers
- SLURM/PBS job scheduling support
- Automatic log synchronization

### Docker Environments

- `Dockerfile.ext`: Production training container
- `Dockerfile.ext-dev`: Development with tools
- `Dockerfile.ext-ros2`: ROS2 integration support

## Important Notes

- **USD Models**: Robot models use USD format for Isaac Sim compatibility
- **Physics Solver**: Uses PhysX for accurate excavation simulation
- **Action Space**: Supports both joint and task-space control
- **Observation Space**: Configurable sensor fusion (cameras, LiDAR, proprioception)
- **Reward Engineering**: Task-specific reward functions in MDP modules
- **Curriculum Learning**: Progressive difficulty in excavation tasks
- **Real-Sim Transfer**: ROS bridge for deployment validation

## File Naming Conventions

- Environment configs: `*_env_cfg.py`
- Agent configs: `rsl_rl_cfg*.py`
- Test scripts: `test_*.py` or `zero_*.py`
- Utility modules: `*_utils.py`
- Measurement classes: `*_measurements.py`
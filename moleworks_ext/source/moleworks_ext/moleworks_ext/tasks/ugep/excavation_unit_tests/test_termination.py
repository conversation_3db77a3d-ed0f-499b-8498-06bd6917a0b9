# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test Termination Condition for Excavation"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os

from omni.isaac.orbit.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Test Termination Condition for Excavation Environment")
parser.add_argument("--cpu", action="store_true", default=False, help="Use CPU pipeline.")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")


# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
args_cli.task = "Isaac-m545-v0"
args_cli.num_envs = 1000
args_cli.headless = True

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import matplotlib.pyplot as plt
import torch
import traceback

import carb
from rsl_rl.runners import OnPolicyRunner

import omni.isaac.contrib_tasks  # noqa: F401
import omni.isaac.orbit_tasks  # noqa: F401
from omni.isaac.orbit_tasks.m545.excavation_utils.excavation_utils import plot_hist, plot_hist_double
from omni.isaac.orbit_tasks.utils import parse_env_cfg

DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")


def main():
    """Zero actions agent with Orbit environment."""
    # parse env configuration
    env_cfg = parse_env_cfg("Isaac-m545-v0", use_gpu=True, num_envs=args_cli.num_envs)
    env_cfg.sim.dt = 0.0375
    env_cfg.reset.only_above_soil = False

    # create environment
    env = gym.make(args_cli.task, cfg=env_cfg)

    # print info (this is vectorized environment)
    print(f"[INFO]: Gym observation space: {env.observation_space}")
    print(f"[INFO]: Gym action space: {env.action_space}")

    # reset environment
    env.unwrapped.curriculum_excavation.set_level_and_update(2000)
    env.reset()

    # Num steps and num_reset
    num_resets = 100
    num_steps = 1
    total_resets = (num_steps) * (num_resets) * env.num_envs
    done_count = torch.zeros(1, device=env.device)
    actions = torch.zeros(env.unwrapped.num_envs, 4)

    # run everything in inference mode
    with torch.inference_mode():
        for i in range(num_resets):
            # Reset the env
            env.reset()
            print("Reset: ", i)
            # compute zero actions
            for j in range(num_steps):
                print("Step: ", j)
                # apply actions
                obs, rewards, dones, timeouts, infos = env.step(actions)

                # Live Monitoring of most important termination conditions
                if env.unwrapped.termination_excavation.episode_neg_term_buf["bucket_aoa"].any():
                    print(
                        "aoa done at env",
                        torch.nonzero(
                            env.unwrapped.termination_excavation.episode_neg_term_buf["bucket_aoa"], as_tuple=True
                        )[0],
                    )
                if env.unwrapped.termination_excavation.episode_neg_term_buf["bucket_vel"].any():
                    print(
                        "vel done at env",
                        torch.nonzero(
                            env.unwrapped.termination_excavation.episode_neg_term_buf["bucket_vel"], as_tuple=True
                        )[0],
                    )

                done_count += torch.count_nonzero(dones)
                print(dones.sum())

    # close the simulator
    env.close()

    # Print success rate
    print(
        "done/resets: {} / {} ({} %)".format(
            int(done_count), int(total_resets), 100.0 * float(done_count) / (total_resets)
        )
    )
    # Print Statistics
    for key, value in infos["episode_neg_term_counts"].items():
        print("neg term: " + key + ": " + str(sum(value)))
    for key, value in infos["episode_pos_term_counts"].items():
        print("pos term: " + key + ": " + str(sum(value)))


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()

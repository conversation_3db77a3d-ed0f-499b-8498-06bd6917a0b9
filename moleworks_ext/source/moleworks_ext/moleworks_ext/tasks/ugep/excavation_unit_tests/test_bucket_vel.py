# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Script to test the bucket velocity"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse

from omni.isaac.orbit.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Zero agent for Orbit environments.")
parser.add_argument("--cpu", action="store_true", default=False, help="Use CPU pipeline.")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import torch
import traceback

import carb

import omni.isaac.contrib_tasks  # noqa: F401
import omni.isaac.orbit_tasks  # noqa: F401
from omni.isaac.orbit_tasks.utils import parse_env_cfg


def main():
    """Zero actions agent with Orbit environment."""
    # parse configuration
    env_cfg = parse_env_cfg("Isaac-m545-v0", use_gpu=True, num_envs=10)
    # create environment
    env = gym.make("Isaac-m545-v0", cfg=env_cfg)

    # print info (this is vectorized environment)
    print(f"[INFO]: Gym observation space: {env.observation_space}")
    print(f"[INFO]: Gym action space: {env.action_space}")
    # reset environment
    env.reset()

    num_steps = 500

    dt = env.sim.get_physics_dt()
    manual_bucket_vel_norm = torch.norm(
        (env.m545_measurements.bucket_pos_w - env.m545_measurements.prev_bucket_pos_w) / dt
    )

    # Log data
    error = []
    bucket_vel_list = []
    manual_bucket_vel_list = []

    # Disable termination for longer tracking
    env.cfg.terminations_excavation.disable_negative_termination = True

    with torch.inference_mode():
        # compute zero actions
        for i in range(num_steps):
            actions = torch.zeros(env.action_space.shape, device=env.unwrapped.device)
            env.step(actions)
            # print(env.torques)
            manual_bucket_vel_norm = torch.norm(
                (env.unwrapped.m545_measurements.bucket_pos_w[0] - env.unwrapped.m545_measurements.prev_bucket_pos_w[0])
                / dt
            )

            # Log data
            bucket_vel_list.append(env.unwrapped.m545_measurements.bucket_vel_norm[0])
            manual_bucket_vel_list.append(manual_bucket_vel_norm)
            error.append(env.unwrapped.m545_measurements.bucket_vel_norm[0] - manual_bucket_vel_norm)

    # close the simulator
    env.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()

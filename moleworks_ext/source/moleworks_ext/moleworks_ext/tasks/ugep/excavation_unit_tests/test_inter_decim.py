# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Script to test quantities in inter decimation steps for the excavation agent"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os

from omni.isaac.orbit.app import AppLauncher
from omni.isaac.orbit.envs.excavation_utils.excavation_utils import plot_hist, plot_hist_double

# add argparse arguments
parser = argparse.ArgumentParser(description="Zero agent for Orbit environments.")
parser.add_argument("--cpu", action="store_true", default=False, help="Use CPU pipeline.")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

args_cli.headless = True

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import matplotlib.pyplot as plt
import torch
import traceback

import carb

import omni.isaac.contrib_tasks  # noqa: F401
import omni.isaac.orbit_tasks  # noqa: F401
from omni.isaac.orbit_tasks.utils import parse_env_cfg

DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")


def main():
    """Zero actions agent with Orbit environment."""
    # parse configuration
    env_cfg = parse_env_cfg("Isaac-m545-v0", use_gpu=True, num_envs=1000)
    # create environment
    env = gym.make("Isaac-m545-v0", cfg=env_cfg)

    # print info (this is vectorized environment)
    print(f"[INFO]: Gym observation space: {env.observation_space}")
    print(f"[INFO]: Gym action space: {env.action_space}")
    # reset environment
    env.reset()

    num_resets = 1
    num_steps = 1

    actions = torch.zeros(env.unwrapped.num_envs, 4)

    # Logged Data
    aoas_init = torch.zeros(num_resets, env.num_envs, device=env.device)
    aoas_post = torch.zeros(num_resets, env.num_envs, device=env.device)

    bucket_full_angle_w_init = torch.zeros(num_resets, env.num_envs, device=env.device)
    bucket_full_angle_w_post = torch.zeros(num_resets, env.num_envs, device=env.device)

    depths = torch.zeros(num_resets, env.num_envs, device=env.device)
    depths_post = torch.zeros(num_resets, env.num_envs, device=env.device)

    forces_x = torch.zeros(num_resets, env.num_envs, device=env.device)
    forces_y = torch.zeros(num_resets, env.num_envs, device=env.device)
    forces_z = torch.zeros(num_resets, env.num_envs, device=env.device)

    forces_x_post = torch.zeros(num_resets, env.num_envs, device=env.device)
    forces_y_post = torch.zeros(num_resets, env.num_envs, device=env.device)
    forces_z_post = torch.zeros(num_resets, env.num_envs, device=env.device)

    in_soil_indices = torch.zeros(num_resets, env.num_envs, device=env.device)

    with torch.inference_mode():
        for i in range(num_resets):
            env.reset()

            print("Reset: ", i)
            depths[i] = env.unwrapped.soil.get_bucket_depth().squeeze()

            in_soil_indices = torch.nonzero(env.unwrapped.soil.get_fill_ratio(), as_tuple=True)[0].clone()
            print("IN SOIL", in_soil_indices.shape)

            aoas_init[i] = env.unwrapped.m545_measurements.bucket_aoa.squeeze()
            bucket_full_angle_w_init[i] = env.unwrapped.soil.get_bucket_full_angle_w().squeeze()

            forces_x[i] = env.unwrapped.bucket_force_com[:, 0].clone()
            forces_y[i] = env.unwrapped.bucket_force_com[:, 1].clone()
            forces_z[i] = env.unwrapped.bucket_force_com[:, 2].clone()

            diff_in_z = env.unwrapped.m545_measurements.bucket_pos_w[:, 2].clone()
            diff_in_x = env.unwrapped.m545_measurements.bucket_pos_w[:, 0].clone()

            diff_in_soil_height = env.unwrapped.soil.soil_height.get_height(
                env.unwrapped.soil.bucket_pos_w[:, 0].view(-1, 1)
            ).clone()

            bucket_pos_z_0 = env.unwrapped.m545_measurements.bucket_pos_w[:, 2].clone()
            bucket_pos_x_0 = env.unwrapped.m545_measurements.bucket_pos_w[:, 0].clone()
            bucket_pos_y_0 = env.unwrapped.m545_measurements.bucket_pos_w[:, 1].clone()
            soil_height_0 = env.unwrapped.soil.soil_height.get_height(
                env.unwrapped.soil.bucket_pos_w[:, 0].view(-1, 1)
            ).clone()
            force_x_0 = env.unwrapped.bucket_force_com[:, 0].clone()
            force_z_0 = env.unwrapped.bucket_force_com[:, 2].clone()

            # compute zero actions
            for j in range(num_steps):
                print("Step: ", j)
                # ----- Inter decim debugging
                env.inter_decim_step_prep(actions)
                for k in range(4):
                    # DO 1 inter deicmation step
                    env.inter_decim_step()

                    depths[i] = env.unwrapped.soil.get_bucket_depth().squeeze()
                    plot_hist(depths, f"depths_id_{k}", f"depths_id_{k}.png")
                    forces_x_post[i] = env.unwrapped.bucket_force_com[:, 0]
                    plot_hist(forces_x_post.squeeze()[in_soil_indices], f"Fx_id_{k}", f"Fx_id_{k}.png")
                    forces_z_post[i] = env.unwrapped.bucket_force_com[:, 2]
                    plot_hist(forces_z_post.squeeze()[in_soil_indices], f"Fz_id_{k}", f"Fz_id_{k}.png")

                    # Bucket positions
                    plot_hist(
                        env.unwrapped.m545_measurements.bucket_pos_w[:, 0].squeeze()[in_soil_indices],
                        f"bucket_pos_w_x_id_{k}",
                        f"bucket_pos_w_x_id_{k}.png",
                    )
                    plot_hist(
                        env.unwrapped.m545_measurements.bucket_pos_w[:, 1].squeeze()[in_soil_indices],
                        f"bucket_pos_w_y_id_{k}",
                        f"bucket_pos_w_y_id_{k}.png",
                    )
                    plot_hist(
                        env.unwrapped.m545_measurements.bucket_pos_w[:, 2].squeeze()[in_soil_indices],
                        f"bucket_pos_w_z_id_{k}",
                        f"bucket_pos_w_z_id_{k}.png",
                    )

                    plot_hist(
                        env.unwrapped.soil.soil_height.get_height(
                            env.unwrapped.soil.bucket_pos_w[:, 0].view(-1, 1)
                        ).squeeze()[in_soil_indices],
                        f"Soil height{k}",
                        f"soil_height{k}.png",
                    )

                    # Differences
                    plot_hist(
                        (bucket_pos_z_0 - env.unwrapped.m545_measurements.bucket_pos_w[:, 2]).squeeze()[
                            in_soil_indices
                        ],
                        f"Diff in bucket pos z {k}",
                        f"diff/Diff_in_bucket_pos_z_{k}.png",
                    )
                    plot_hist(
                        (bucket_pos_x_0 - env.unwrapped.m545_measurements.bucket_pos_w[:, 0]).squeeze()[
                            in_soil_indices
                        ],
                        f"Diff in bucket pos x {k}",
                        f"diff/Diff_in_bucket_pos_x_{k}.png",
                    )
                    plot_hist(
                        (bucket_pos_y_0 - env.unwrapped.m545_measurements.bucket_pos_w[:, 1]).squeeze()[
                            in_soil_indices
                        ],
                        f"Diff in bucket pos y {k}",
                        f"diff/Diff_in_bucket_pos_y_{k}.png",
                    )
                    plot_hist(
                        (
                            soil_height_0
                            - env.unwrapped.soil.soil_height.get_height(
                                env.unwrapped.soil.bucket_pos_w[:, 0].view(-1, 1)
                            )
                        ).squeeze()[in_soil_indices],
                        f"Diff in Soil Height {k}",
                        f"diff/Diff_in_soil_height_{k}.png",
                    )
                    plot_hist(
                        (force_x_0 - env.unwrapped.bucket_force_com[:, 0]).squeeze()[in_soil_indices],
                        f"Diff in Force x {k}",
                        f"diff/Diff_in_force_x_{k}.png",
                    )
                    plot_hist(
                        (force_z_0 - env.unwrapped.bucket_force_com[:, 2]).squeeze()[in_soil_indices],
                        f"Diff in Force x {k}",
                        f"diff/Diff_in_force_z_{k}.png",
                    )
                    # Differences in achieved joint vel
                    plot_hist(
                        (env.unwrapped.m545_measurements.joint_vel[:, 0]).squeeze()[in_soil_indices],
                        f"Diff in Joint vel 1 id {k}",
                        f"diff/Diff_in_joint_vel_1_{k}.png",
                    )
                    plot_hist(
                        (env.unwrapped.m545_measurements.joint_vel[:, 1]).squeeze()[in_soil_indices],
                        f"Diff in Joint vel 2 id {k}",
                        f"diff/Diff_in_joint_vel_2_{k}.png",
                    )
                    plot_hist(
                        (env.unwrapped.m545_measurements.joint_vel[:, 2]).squeeze()[in_soil_indices],
                        f"Diff in Joint vel 3 id {k}",
                        f"diff/Diff_in_joint_vel_3_{k}.png",
                    )
                    plot_hist(
                        (env.unwrapped.m545_measurements.joint_vel[:, 3]).squeeze()[in_soil_indices],
                        f"Diff in Joint vel 4 id {k}",
                        f"diff/Diff_in_joint_vel_4_{k}.png",
                    )

                diff_in_z -= env.unwrapped.m545_measurements.bucket_pos_w[:, 2]
                plot_hist(diff_in_z.squeeze()[in_soil_indices], f"Bucket pos 0 - bucket pos 4 in Z", f"diff_in_z.png")

                diff_in_soil_height -= env.unwrapped.soil.soil_height.get_height(
                    env.unwrapped.soil.bucket_pos_w[:, 0].view(-1, 1)
                )
                plot_hist(
                    diff_in_soil_height.squeeze()[in_soil_indices],
                    f"Soil Height 0 - Soil Height 4",
                    f"diff_in_soil_height.png",
                )

                diff_in_x -= env.unwrapped.m545_measurements.bucket_pos_w[:, 0]
                plot_hist(diff_in_x.squeeze()[in_soil_indices], f"Bucket pos 0 - bucket pos 4 in x", f"diff_in_x.png")

                # ----- Inter decim debugging

                # aoas_post[i] = env.unwrapped.m545_measurements.bucket_aoa.squeeze()
                # bucket_full_angle_w_post[i] = env.unwrapped.soil.get_bucket_full_angle_w().squeeze()
                # print(env.scene.articulations['robot'].data.root_lin_vel_w)
                # if env.unwrapped.common_step_counter > 1:
                # if j !=0:
                depths_post[i] = env.unwrapped.soil.get_bucket_depth().squeeze()

                forces_x_post[i] = env.unwrapped.bucket_force_com[:, 0].clone()
                forces_y_post[i] = env.unwrapped.bucket_force_com[:, 1].clone()
                forces_z_post[i] = env.unwrapped.bucket_force_com[:, 2].clone()

    # close the simulator
    env.close()

    # plot_hist(aoas_init, "aoas_init", "aoas_init.png")
    # plot_hist(aoas_post, "aoas_post", "aoas_post.png")
    # plot_hist(depths, "depths", "depths.png")
    # plot_hist(depths_post, "depths_post", "depths_post.png")


#
# plot_hist(forces_x.squeeze()[in_soil_indices], "bucket force x", "force_x.png")
# plot_hist(forces_y.squeeze()[in_soil_indices], "bucket force y", "force_y.png")
# plot_hist(forces_z.squeeze()[in_soil_indices], "bucket force z", "force_z.png")
#
#
# plot_hist(forces_x_post.squeeze()[in_soil_indices], "bucket force x post", "force_x_post.png")
# plot_hist(forces_y_post.squeeze()[in_soil_indices], "bucket force y post", "force_y_post.png")


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()

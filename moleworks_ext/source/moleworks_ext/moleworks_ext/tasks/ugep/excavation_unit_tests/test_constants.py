# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test some constant from the robot"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse

from omni.isaac.orbit.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Zero agent for Orbit environments.")
parser.add_argument("--cpu", action="store_true", default=False, help="Use CPU pipeline.")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import torch
import traceback

import carb

import omni.isaac.contrib_tasks  # noqa: F401
import omni.isaac.orbit_tasks  # noqa: F401
from omni.isaac.orbit_tasks.utils import parse_env_cfg


def main():
    """Zero actions agent with Orbit environment."""
    # parse configuration
    env_cfg = parse_env_cfg("Isaac-m545-v0", use_gpu=True, num_envs=2)
    # create environment
    env = gym.make("Isaac-m545-v0", cfg=env_cfg)

    # print info (this is vectorized environment)
    print(f"[INFO]: Gym observation space: {env.observation_space}")
    print(f"[INFO]: Gym action space: {env.action_space}")

    print("INERTIA", env.m545_asset.root_physx_view.get_inertias().to(env.device)[0])
    print("Masse", env.m545_asset.root_physx_view.get_masses().to(env.device)[0])

    """
    Comparison values from Gym
    GYM:

    tensor([[ 2.1212e+04,  6.0274e+01,  7.4568e+02,  6.0274e+01,  2.2514e+04,
         -3.6403e+02,  7.4568e+02, -3.6403e+02,  3.0311e+04],
        [ 6.2707e+01,  2.3197e+00,  8.9178e+01,  2.3197e+00,  3.2916e+03,
          2.9488e-02,  8.9178e+01,  2.9495e-02,  3.2916e+03],
        [ 1.2198e+01,  2.0740e-01,  1.2570e+00,  2.0740e-01,  3.8850e+02,
         -2.0218e-04,  1.2570e+00, -1.9455e-04,  3.8850e+02],
        [ 4.8211e+01, -4.2332e+00,  4.0176e+01, -4.2332e+00,  2.3456e+03,
          3.0795e+00,  4.0176e+01,  3.0795e+00,  2.3456e+03],
        [ 1.0421e+02, -5.1053e-02, -4.6526e+00, -5.1053e-02,  1.3288e+02,
         -4.5803e-02, -4.6526e+00, -4.5803e-02,  1.2870e+02]], device='cuda:0')

    INERTIA tensor([[ 1.2973e+04,  6.8737e+01,  1.1999e+03,  6.8737e+01,  2.5913e+04,
         -3.0468e+02,  1.1999e+03, -3.0468e+02,  2.7783e+04],
        [ 6.2707e+01,  2.3197e+00,  8.9178e+01,  2.3197e+00,  3.2916e+03,
          2.9488e-02,  8.9178e+01,  2.9495e-02,  3.2916e+03],
        [ 1.2198e+01,  2.0740e-01,  1.2570e+00,  2.0740e-01,  3.8850e+02,
         -2.0218e-04,  1.2570e+00, -1.9455e-04,  3.8850e+02],
        [ 4.8211e+01, -4.2332e+00,  4.0176e+01, -4.2332e+00,  2.3456e+03,
          3.0795e+00,  4.0176e+01,  3.0795e+00,  2.3456e+03],
        [ 1.0432e+02,  1.6036e-01, -2.6035e+00,  1.6036e-01,  1.5443e+02,
         -5.0624e-02, -2.6035e+00, -5.0624e-02,  1.5015e+02]], device='cuda:0')

    """

    # close the simulator
    env.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()

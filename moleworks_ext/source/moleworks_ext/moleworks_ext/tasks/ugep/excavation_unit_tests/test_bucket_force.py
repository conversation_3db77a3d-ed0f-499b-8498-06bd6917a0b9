# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022, NVIDIA CORPORATION & AFFILIATES, ETH Zurich, and University of Toronto
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
This script is designed to test how forces are applied on the bucket
"""


"""Launch Isaac Sim Simulator first."""

import argparse
import matplotlib.pyplot as plt

from omni.isaac.orbit.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Test bucket force")
parser.add_argument("--headless", action="store_true", default=False, help="Force display off at all times.")
parser.add_argument("--robot", type=str, default="Isaac-m545-v0", help="Name of the robot.")
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(headless=args_cli.headless)
simulation_app = app_launcher.app


import numpy as np
import torch

import omni.isaac.core.utils.torch as torch_utils
from omni.isaac.orbit_assets.m545 import M545_DOF_ARM_CFG

import omni.isaac.orbit.sim as sim_utils
from omni.isaac.orbit.assets import Articulation, ArticulationCfg, AssetBase, AssetBaseCfg, RigidObject
from omni.isaac.orbit.controllers.inverse_dynamics import InverseDynamicsController, InverseDynamicsControllerCfg
from omni.isaac.orbit.sim import SimulationContext
from omni.isaac.orbit.sim.spawners.from_files.from_files_cfg import GroundPlaneCfg, UsdFileCfg

from omni.isaac.orbit_tasks.m545.excavation_env_cfg import ExcavationEnvCfg
from omni.isaac.orbit_tasks.m545.m545_env_cfg import M545EnvCfg

joint_vel_limits = np.zeros((4, 2))
# joint_vel_limits[0] = np.array([-1, 1])
joint_vel_limits[0] = np.array([-0.5, 0.5])
joint_vel_limits[1] = np.array([-0.6, 0.6])
joint_vel_limits[2] = np.array([-0.4, 0.4])
joint_vel_limits[3] = np.array([-0.8, 0.8])
joint_efforts_limits = np.zeros((4, 2))
# joint_efforts_limits[0] = np.array([-2e6, 2e6])
joint_efforts_limits[0] = np.array([-2e6, 2e6])
joint_efforts_limits[1] = np.array([-1e6, 1e6])
joint_efforts_limits[2] = np.array([-1e6, 1e6])
joint_efforts_limits[3] = np.array([-1e6, 1e6])


from omni.isaac.orbit.terrains import TerrainImporter, TerrainImporterCfg


def main():
    """
    The main function initializes and runs the simulation environment. It performs several key tasks:
    - Initializes the simulation with specified configurations, including device usage (GPU acceleration), physics parameters, and the main camera view.
    - Configures and spawns elements in the simulation, such as lighting and the robot, based on predefined configurations.
    - Sets up the terrain using the TerrainImporter with configurable parameters for physics material properties and environmental setup.
    - Runs the simulation loop, applying external forces to the robot, simulating its dynamics, and resetting the robot's state as necessary.
    - The simulation loop continues until the Isaac Sim application is closed, with the robot's joint states being updated at each step.
    The function showcases the integration of various Isaac Sim components to create a comprehensive simulation environment for robotic research and development.
    """
    # Load kit helper
    sim = SimulationContext(
        sim_utils.SimulationCfg(
            device="cuda",
            use_gpu_pipeline=True,
            dt=0.04,
            physx=sim_utils.PhysxCfg(
                max_position_iteration_count=6,
                min_position_iteration_count=6,
                max_velocity_iteration_count=2,
                min_velocity_iteration_count=2,
            ),
        )
    )
    # Set main camera
    sim.set_camera_view([12, 12, 12], [0.0, 0.0, 0.0])

    # Lights-1
    cfg = sim_utils.DistantLightCfg(intensity=600.0, color=(0.75, 0.75, 0.75))
    cfg.func("/World/Light/greyLight", cfg, translation=(4.5, 3.5, 10.0))
    # Robot Cfg
    robot_cfg = M545_DOF_ARM_CFG
    robot = Articulation(cfg=robot_cfg.replace(prim_path="/World/Robot"))

    terrain_cfg = TerrainImporterCfg(
        prim_path="/World/ground",
        terrain_type="plane",
        terrain_generator=None,
        max_init_terrain_level=5,
        collision_group=-1,
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",
            restitution_combine_mode="multiply",
            static_friction=0.8,  # should be 0.8
            dynamic_friction=0.8,
            restitution=0.8,
        ),
        debug_vis=False,
        env_spacing=10,
        num_envs=1,
    )

    terrain = TerrainImporter(cfg=terrain_cfg.replace(prim_path="/World/Ground"))

    # Define simulation stepping
    physics_dt = sim.get_physics_dt()
    # episode counter
    ep_step_count = 0

    # Play the simulator
    sim.reset()
    # Now we are ready!
    print("[INFO]: Setup complete...")

    # Simulate physics
    while simulation_app.is_running():
        # If simulation is stopped, then exit.
        if sim.is_stopped():
            break
        # If simulation is paused, then skip.
        if not sim.is_playing():
            sim.step(render=app_launcher.RENDER)
            continue
        else:
            if ep_step_count == 0:
                robot.reset()

            # Example of a force that can be applied
            const = 1e5
            force_tensor_base = torch.tensor(
                [
                    [0, 0, 0],
                    [0.0000e00, 0.0000e00, 0.0000e00],
                    [0.0000e00, 0.0000e00, 0.0000e00],
                    [0.0, 0.0, 0],
                    [0, const, 0.0],
                ],
                device=robot.device,
            ).reshape(1, 5, 3)
            force_tensor = torch.tensor(
                [
                    [0.0000e00, 0.0000e00, 0.0000e00],
                    [0.0000e00, 0.0000e00, 0.0000e00],
                    [0.0000e00, 0.0000e00, 0.0000e00],
                    [0.0000e00, 0.0000e00, 0.0000e00],
                    [2.1418e04, 0.0000e00, 7.1892e03],
                ],
                device=robot.device,
            ).reshape(1, 5, 3)
            moment_tensor = torch.tensor(
                [
                    [0.0000, 0.0000, 0.0000],
                    [0.0000, 0.0000, 0.0000],
                    [0.0000, 0.0000, 0.0000],
                    [0.0000, 0.0000, 0.0000],
                    [0.0000, -16739.1543, 0.0000],
                ],
                device=robot.device,
            ).reshape(1, 5, 3)
            force_tensor_0 = torch.zeros_like(force_tensor)
            moment_tensor_0 = torch.zeros_like(moment_tensor)

            # Apply a force since this ep_step_count
            if ep_step_count > 300:
                robot.set_external_force_and_torque(force_tensor_base, moment_tensor_0)

            joint_pos, joint_vel = robot.data.default_joint_pos, robot.data.default_joint_vel
            robot.write_joint_state_to_sim(joint_pos, joint_vel)
            robot.write_data_to_sim()
            sim.step()
            robot.update(physics_dt)

            ep_step_count += 1


if __name__ == "__main__":
    main()
    simulation_app.close()

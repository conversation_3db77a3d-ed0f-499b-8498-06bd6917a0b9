# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022, NVIDIA CORPORATION & AFFILIATES, ETH Zurich, and University of Toronto
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
This script is designed to tune the PID gains in the inverse dynamics controller for the excavation agent.
A lot of data are logged but most of them are very useful so those are left.
"""

"""Launch Isaac Sim Simulator first."""

import argparse
import matplotlib.pyplot as plt

from omni.isaac.orbit.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(
    description="This script demonstrates how to simulate a mobile manipulator with dummy joints."
)
parser.add_argument("--headless", action="store_true", default=False, help="Force display off at all times.")
parser.add_argument("--robot", type=str, default="Isaac-ugep-v0", help="Name of the robot.")
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(headless=args_cli.headless)
simulation_app = app_launcher.app


import numpy as np
import torch

import omni.isaac.core.utils.torch as torch_utils
# loads a single excavator 
from omni.isaac.orbit_assets.general_excavator import GENERAL_EX_CFG

import omni.isaac.orbit.sim as sim_utils
from omni.isaac.orbit.assets import Articulation, ArticulationCfg, AssetBase, AssetBaseCfg, RigidObject
from omni.isaac.orbit.controllers.inverse_dynamics import InverseDynamicsController, InverseDynamicsControllerCfg
from omni.isaac.orbit.sim import SimulationContext
from omni.isaac.orbit.sim.spawners.from_files.from_files_cfg import GroundPlaneCfg, UsdFileCfg

from omni.isaac.orbit_tasks.ugep.ugep_env_cfg import ugepEnvCfg, ExcavationEnvCfg

joint_vel_limits = np.zeros((3, 2))
joint_vel_limits[0] = np.array([-0.5, 0.5])
joint_vel_limits[1] = np.array([-0.6, 0.6])
# joint_vel_limits[2] = np.array([-0.4, 0.4])
joint_vel_limits[2] = np.array([-0.8, 0.8])
joint_efforts_limits = np.zeros((3, 2))
joint_efforts_limits[0] = np.array([-2e6, 2e6])
joint_efforts_limits[1] = np.array([-1e6, 1e6])
# joint_efforts_limits[2] = np.array([-1e6, 1e6])
joint_efforts_limits[2] = np.array([-1e6, 1e6])


from omni.isaac.orbit.terrains import TerrainImporter, TerrainImporterCfg

# Config
INV_DYN_CFG = InverseDynamicsControllerCfg(
    command_type="vel",
    k_p=[0, 0, 0],
    k_d=[25, 30, 20],  #  manual mass matrix: k_d=
    # 25,30,40,20, Gym: 25,30,20,20
    dof_limits=joint_vel_limits,
    dof_efforts_limits=joint_efforts_limits,
)


def main():
    """Main function."""
    sim, robot = set_simulation()

    """
    Initial pose = torch.tensor([[-0.78, 1.57, 0.009, 0.0]], device = robot.device)
            "J_BOOM": -0.78,
            "J_DIPPER": 1.57,
            "J_TELE": 0.009,
            "J_SHOVEL": 0.,
    joint limit:
        joint_pos_limits[0] = np.array([-1.34, 0.44])
        joint_pos_limits[1] = np.array([0.58, 2.78])
        joint_pos_limits[2] = np.array([0.0, 1.8])
        joint_pos_limits[3] = np.array([-0.35, 2.28])

        limit_target = torch.tensor([[-1.34,  2.78,  1.8,  2.28]], device = robot.device)

        "J_BOOM": 0,
        "J_DIPPER": 1,
        "J_SHOVEL": 3

    """
    # Buffer for velocity target
    veloctiy_target = torch.zeros_like(robot.data.default_joint_pos)
    # Decide wheter we follow 0 velocity or a sinus
    follow_sinus = True
    # Duration [s]
    duration = 12
    # Choose which joint to follow a sinusoid. Others will follow 0 velocity target
    joints_to_tune = ["J_BOOM", "J_DIPPER", "J_SHOVEL"]
    # DO the simulation
    do_simulation(
        sim,
        robot,
        duration,
        controller_type="inverse_dynamics",
        inverse_dyn_target_vel=veloctiy_target,
        t_oscill=0,
        t_ext_force=2,
        joints_to_tune=joints_to_tune,
        follow_sinus=follow_sinus,
    )


def set_simulation():
    # Load kit helper
    sim = SimulationContext(
        sim_utils.SimulationCfg(
            device="cuda",
            use_gpu_pipeline=True,
            dt=0.04,
            physx=sim_utils.PhysxCfg(
                max_position_iteration_count=6,
                min_position_iteration_count=6,
                max_velocity_iteration_count=2,
                min_velocity_iteration_count=2,
            ),
        )
    )
    # Set main camera
    sim.set_camera_view([12, 12, 12], [0.0, 0.0, 0.0])

    # -- Spawn things into stage
    # Lights-1
    cfg = sim_utils.DistantLightCfg(intensity=600.0, color=(0.75, 0.75, 0.75))
    cfg.func("/World/Light/greyLight", cfg, translation=(4.5, 3.5, 10.0))
    # Robot Cfg
    robot_cfg = GENERAL_EX_CFG
    robot = Articulation(cfg=robot_cfg.replace(prim_path="/World/Robot"))
    # Terrain
    terrain_cfg = TerrainImporterCfg(
        prim_path="/World/ground",
        terrain_type="plane",
        terrain_generator=None,
        max_init_terrain_level=5,
        collision_group=-1,
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="average",
            restitution_combine_mode="multiply",
            static_friction=0.8,  # should be 0.8
            dynamic_friction=0.8,
            restitution=0.8,
        ),
        debug_vis=False,
        env_spacing=10,
        num_envs=1,
    )
    terrain = TerrainImporter(cfg=terrain_cfg.replace(prim_path="/World/Ground"))

    # Play the simulator
    sim.reset()
    # Now we are ready!
    print("[INFO]: Setup complete...")
    return sim, robot


def do_simulation(
    sim,
    robot,
    duration,
    controller_type,
    inverse_dyn_target_vel,
    joints_to_tune,
    t_oscill,
    t_ext_force,
    follow_sinus=True,
):
    """
    Do the simulation. Params
        controller_type: controller type we want to use, inverse dynamics is now implememtd but another could be used here
        inverse_dyn_target_vel: Target veloctiy
        joints_to_tune: List with name of joints to tune
        t_oscill: When oscillation starts
        t_ext_force: When external force is applied
        follow_sinus: If we want the joints to follow inverse_dyn_target_vel or a sinus

    """
    # joint ids
    joint_ids, _ = robot.find_joints(joint_names)
    joint_ids = torch.tensor(joint_ids, device=robot.device)
    joint_ids_shifted = joint_ids + 6

    # dummy action
    actions_inv_dyn = torch.zeros(robot.root_physx_view.count, robot.num_joints, device=robot.device)

    # Set up controll
    inverse_dyn_controller = InverseDynamicsController(INV_DYN_CFG, robot.root_physx_view.count, robot.device)

    # Define simulation stepping
    physics_dt = sim.get_physics_dt()
    # episode counter
    sim_time = 0.0
    ep_step_count = 0
    frequency_id = 0
    decimation = 1
    num_steps_per_episode = int(duration / physics_dt / decimation)

    # Buffer
    manual_joint_vel = torch.zeros_like(robot.data.joint_pos, device=robot.device)
    previous_arm_joint_pos = torch.zeros_like(robot.data.joint_pos, device=robot.device)
    actuation_forces = robot.root_physx_view.get_dof_actuation_forces()
    arm_joint_vel = robot.data.joint_vel

    # Logging
    data_history = torch.zeros((int(duration / physics_dt), 3, len(joint_ids)), device=robot.device)
    body_pos = []
    dof_pos = []
    body_vel = []
    manual_grav = []
    sim_grav = []
    error_grav = []
    sim_arm_vel = []
    manual_arm_vel = []
    error_vel = []
    coriolis_centrifugal_forces = []
    torques = []
    gravity_contrib_list = []
    accel_contrib_list = []
    mass_matrices = []
    desired_dof_dof_vels = []
    desired_dofs = []
    dof_vels = []
    bucket_vel_w_list = []
    external_force_comp_list = []
    external_moment_comp_list = []

    jacobians = torch.zeros([1, 5, 6, 10], device=robot.device)

    # Simulate physics
    while simulation_app.is_running():
        # If simulation is stopped, then exit.
        if sim.is_stopped():
            break
        # If simulation is paused, then skip.
        if not sim.is_playing():
            sim.step(render=app_launcher.RENDER)
            continue
        else:
            body_pos.append(robot.data.body_pos_w[:, :, 2])
            if ep_step_count == 0:
                joint_pos, joint_vel = robot.data.default_joint_pos, robot.data.default_joint_vel
                robot.write_joint_state_to_sim(joint_pos, joint_vel)
                root_state = robot.data.default_root_state
                robot.write_root_state_to_sim(root_state)

            else:
                if ep_step_count % num_steps_per_episode == 0:
                    frequency_id += 1
                    # plot_errors_over_iteration(manual_grav, "Gravity torque manually computed")
                    # plot_errors_over_iteration(sim_grav, "Gravity torque from the sim")
                    # plot_errors_over_iteration(error_grav, "Absolute error gravity")
                    # plot_errors_over_iteration(sim_arm_vel[:], "Joint velocities from the sim")
                    # plot_velocities_over_iteration(bucket_vel_w_list, "Bucket velocity")
                    ##plot_errors_over_iteration(manual_arm_vel[:], "Manually computed joint velocities")
                    # plot_errors_over_iteration(error_vel[:], "Absolute velocity error")
                    # plot_bodies_over_iteration(body_pos, "Bodies pos")
                    # plot_bodies_over_iteration(body_vel, "Bodies vel")
                    # plot_errors_over_iteration(dof_pos, "Dof_pos", t_oscill, t_ext_force)
                    # plot_errors_over_iteration(torques, "Applied torques", t_oscill, t_ext_force)
                    # plot_errors_over_iteration(dof_vels, "Inverse dyn dof vels", t_oscill, t_ext_force)
                    # plot_errors_over_iteration(desired_dofs, "Inverse dyn desired dof vels", t_oscill, t_ext_force)
                    # plot_errors_over_iteration(desired_dof_dof_vels, "Inverse dynamic error (des vel-vel)", t_oscill, t_ext_force)
                    # plot_errors_over_iteration(external_force_comp_list, "External Force compensation", t_oscill, t_ext_force)
                    # plot_errors_over_iteration(external_moment_comp_list, "External Moment compensation", t_oscill, t_ext_force)
                    # plot_mass_matrices(mass_matrices)
                    # plot_errors_over_iteration(gravity_contrib_list, "Gravity torque contribution", t_oscill, t_ext_force)
                    # plot_errors_over_iteration(accel_contrib_list, "Mass*Accel torque contribution", t_oscill, t_ext_force)

                    # Tracking error for inverse_dynamics
                    if controller_type == "inverse_dynamics" or controller_type == "set_dof_velocity_targets":
                        plot_trajectories(data_history[:, 0], data_history[:, 1])
                    # Show the plots-
                    plt.show()
                    if frequency_id == len(frequencies):
                        frequency_id = 0
                        actions_inv_dyn = robot.data.default_joint_pos

                if follow_sinus:
                    if ep_step_count < t_oscill:
                        actions_inv_dyn[:, :] = 0
                    else:
                        actions_inv_dyn[:, :] = 0
                        for i in range(len(joints_to_tune)):
                            actions_inv_dyn[:, joint_dict[joints_to_tune[i]]] = (
                                np.sin((sim_time - 100 * physics_dt) * frequencies[frequency_id] * 2 * np.pi) * 0.2
                            )
                else:
                    actions_inv_dyn[:, :] = inverse_dyn_target_vel
            
            actions_inv_dyn[:, :] = 0

            # Track target position
            inverse_dyn_controller.set_command(actions_inv_dyn[:, joint_ids])

            # Jacobians
            prev_jacobians = jacobians.clone()
            jacobians = robot.root_physx_view.get_jacobians()
            # print(jacobians-prev_jacobians)
            jacobian_ee_lin = jacobians[:, :, :3, joint_ids_shifted]
            jacobian_ee_rot = jacobians[:, :, 3:, joint_ids_shifted]  # jacobians[:, -1, 3:]

            # masses
            masses = robot.root_physx_view.get_masses().to(robot.device)
            gravity = torch.tensor([0, 0, -9.81], device=robot.device)
            # Reshape your tensors to align with broadcasting rules
            gravity_expanded = gravity.view(1, 3)
            masses_expanded = masses.view(robot.num_bodies, 1)
            gravity_forces = masses_expanded * gravity_expanded
            jac_lin = jacobians[:, :, 0:3, :].to(robot.device)
            jac_rot = jacobians[:, :, 3:6, :].to(robot.device)
            jac_lin_T = jac_lin.transpose(-2, -1)
            jac_rot_T = jac_rot.transpose(-2, -1)
            # Compute the generalized torques by contracting over dimensions 5 and 3
            # gravity_torques_manual = torch.einsum('ijkl,jk->il', jacobian_ee_lin, gravity_forces)
            # Mass matrix and gravity torques
            # if ep_step_count == 0:
            #     # Precomputed for first step to stabilize the asset, TODO: possibly recompute
            #     gravity_torques_manual = torch.tensor([91020.93, 25834.578, 2782.934], device=robot.device)
            #     mass_matrix = torch.tensor(
            #         [
            #             [40491.41, 16668.646, 4673.478, 1860.3077],
            #             [16668.646, 20955.602, 803.5827, 2422.6606],
            #             [4673.478, 803.5827, 1519.462, 76.925156],
            #             [1860.3077, 2422.6606, 76.925156, 505.5495],
            #         ],
            #         device=robot.device,
            #     )
            #     gravity_torques = robot.root_physx_view.get_generalized_gravity_forces().clone()
            # else:
            # Compute
            gravity_genco_tau = torch.sum(
                torch.matmul(
                    jac_lin_T[:, -3:, -3:, :],
                    gravity_forces[-3:, :].unsqueeze(-1),
                ),
                dim=1,
            )
            gravity_torques_manual = gravity_genco_tau.squeeze()
            # compute gravity torques on the joints
            gravity_torques = robot.root_physx_view.get_generalized_gravity_forces().clone()
            # Self computed mass matrix
            inertias = robot.root_physx_view.get_inertias().to(robot.device)
            inertias = torch.reshape(inertias, (4, 3, 3))
            mass_matrix_manual = torch.sum(
                jac_lin_T.matmul(masses.view(-1, 1, 1) * jac_lin) + jac_rot_T.matmul(inertias.matmul(jac_rot)),
                dim=1,
            )[:, -robot.num_joints :, -robot.num_joints :]

            mass_matrix = mass_matrix_manual
            mass_matrices.append(mass_matrix)

            # Coriolis and centrifugal forces
            coriolis_centrifugal_force = robot.root_physx_view.get_coriolis_and_centrifugal_forces()
            manual_grav.append(gravity_torques_manual)
            sim_grav.append(gravity_torques)
            # error_grav.append(gravity_torques_manual - gravity_torques)
            coriolis_centrifugal_forces.append(coriolis_centrifugal_force)
            body_pos.append(robot.data.body_pos_w[:, :, 2])
            const = 8000

            # ---------------- Force Compensation, not at step 0 -----------------------
            if ep_step_count < t_ext_force:
                # No force applied and no force compensation at the beginning
                if controller_type == "inverse_dynamics":
                    id_torques = inverse_dyn_controller.compute(
                        robot.data.joint_pos[:, joint_ids],
                        arm_joint_vel,
                        mass_matrix,
                        gravity_torques_manual,
                        (coriolis_centrifugal_force),
                        # external_force=external_force_comp,
                        # external_moment=external_moment_comp,
                    )
                    actuation_forces[:, joint_ids] = id_torques
                    print("*joint_ids ", joint_ids)
                    print("*arm torques inv dyn", id_torques)
                    robot.set_joint_effort_target((actuation_forces), joint_ids)
                    # robot.root_physx_view.set_dof_actuation_forces((actuation_forces), joint_ids)
                    torques.append(id_torques.clone())

            else:
                # Apply the force and compensate for it after
                # Force
                # Apply the force and the moment
                force_tensor = torch.tensor(
                    [
                        [0.0000e00, 0.0000e00, 0.0000e00],
                        [0.0000e00, 0.0000e00, 0.0000e00],
                        [0.0000e00, 0.0000e00, 0.0000e00],
                        [2.1418e04, 0.0000e00, 7.1892e03],
                    ],
                    device=robot.device,
                ).reshape(1, 4, 3)
                moment_tensor = torch.tensor(
                    [
                        [0.0000, 0.0000, 0.0000],
                        [0.0000, 0.0000, 0.0000],
                        [0.0000, 0.0000, 0.0000],
                        [0.0000, -16739.1543, 0.0000],
                    ],
                    device=robot.device,
                ).reshape(1, 4, 3)
                force_tensor_0 = torch.zeros_like(force_tensor)
                moment_tensor_0 = torch.zeros_like(moment_tensor)
                force_tensor_idx = torch.tensor([[2.1418e04, 0.0000e00, 7.1892e03]], device=robot.device).reshape(
                    1, 1, 3
                )
                moment_tensor_idx = torch.tensor([[0.0000, -16739.1543, 0.0000]], device=robot.device).reshape(1, 1, 3)

                robot.set_external_force_and_torque(force_tensor_idx, moment_tensor_idx, body_ids=[3])
                bucket_jac_lin_T_dof = jacobians[:, -1, 0:3, -robot.num_joints :].transpose(-2, -1)
                bucket_jac_rot_T_dof = jacobians[:, -1, 3:6, -robot.num_joints :].transpose(-2, -1)
                ext_f_genco_tau = torch.matmul(bucket_jac_lin_T_dof, force_tensor[0, 3].reshape([1, 3, 1]))
                external_force_comp = ext_f_genco_tau.squeeze(-1)[:, -robot.num_joints :]
                ext_m_genco_tau = torch.matmul(bucket_jac_rot_T_dof, moment_tensor[0, 3].reshape([1, 3, 1]))
                external_moment_comp = ext_m_genco_tau.squeeze(-1)[:, -robot.num_joints :]

                external_force_comp_list.append(external_force_comp.clone())
                external_moment_comp_list.append(external_moment_comp.clone())
                if controller_type == "inverse_dynamics":
                    id_torques = inverse_dyn_controller.compute(
                        robot.data.joint_pos[:, joint_ids],
                        arm_joint_vel,
                        mass_matrix,
                        gravity_torques_manual,
                        (coriolis_centrifugal_force),
                        external_force=external_force_comp,
                        external_moment=external_moment_comp,
                    )
                    actuation_forces[:, joint_ids] = id_torques

                    print("arm torques inv dyn", id_torques)
                    robot.set_joint_effort_target((actuation_forces), joint_ids)
                    torques.append(id_torques.clone())
            # write commands to sim
            if controller_type == "set_dof_velocity_targets":
                robot.root_physx_view.set_dof_velocity_targets(actions_inv_dyn, joint_ids)
                print("set_dof_velocity_targets")

            # Write buffers in the sim
            robot.write_data_to_sim()
            # Physics stepping
            sim.step()
            # Update robot
            robot.update(physics_dt)
            # input("press enter to continue")
            # Compute velocite and chosse which one îs used
            sim_vel = robot.data.joint_vel.clone()
            manual_joint_vel = (robot.data.joint_pos - previous_arm_joint_pos) / physics_dt
            arm_joint_vel = sim_vel  # manual_joint_vel
            previous_arm_joint_pos = robot.data.joint_pos[:, joint_ids]

            # Log data
            error_vel.append(manual_joint_vel - robot.data.joint_vel)
            body_vel.append(robot.data.body_vel_w[:, :, 2])
            dof_pos.append(robot.data.joint_pos.clone())

            # Bucket velocity
            j_pitch_vel = robot.data.body_lin_vel_w[:, -1, :]
            j_pitch_ang_vel = robot.data.body_ang_vel_w[:, -1, :]
            j_pitch_quat = robot.data.body_quat_w[:, -1, :]
            p_r_pe = torch.tensor([1.418, 0.0, 0.053], device=robot.device).expand(1, -1)

            w_r_pe = torch_utils.quat_rotate(j_pitch_quat, p_r_pe)
            bucket_vel_w = j_pitch_vel + torch.cross(j_pitch_ang_vel, w_r_pe)
            bucket_vel_w_list.append(bucket_vel_w)

            sim_arm_vel.append(sim_vel)
            manual_arm_vel.append(manual_joint_vel)
            data_history[ep_step_count % num_steps_per_episode, 0, :] = actions_inv_dyn[:, joint_ids].clone()
            data_history[ep_step_count % num_steps_per_episode, 1, :] = arm_joint_vel
            data_history[ep_step_count % num_steps_per_episode, 2, :] = robot.data.joint_pos[:, joint_ids].clone()

            print("Joint pos", robot.data.joint_pos)

            sim_time += physics_dt
            ep_step_count += 1


def plot_errors_over_iteration(errors, title, t_oscill, t_ext_force):
    # Convert the list of torch tensors to a single torch tensor
    errors_tensor = torch.cat(errors, dim=0)

    # Extract errors for variables a, b, c, d, e
    errors_a = errors_tensor[:, 0].cpu().numpy()  # torch.Size([249, 5])
    errors_b = errors_tensor[:, 1].cpu().numpy()
    errors_c = errors_tensor[:, 2].cpu().numpy()
    errors_d = errors_tensor[:, 3].cpu().numpy()

    fig, ax = plt.subplots()

    # Plot errors for each variable over the number of iterations
    ax.plot(range(1, len(errors) + 1), errors_a, label="J_BOOM")
    ax.plot(range(1, len(errors) + 1), errors_b, label="J_DIPPER")
    # ax.plot(range(1, len(errors) + 1), errors_c, label="J_TELE")
    ax.plot(range(1, len(errors) + 1), errors_d, label="J_SHOVEL")

    ax.axvline(x=t_oscill, color="r", linestyle="--", label="Oscillation")
    ax.axvline(x=t_ext_force, color="g", linestyle="--", label="External force")

    # Set labels and title
    ax.set_xlabel("Iteration")
    ax.set_ylabel(" ")
    ax.set_title(title)

    # Display legend
    ax.legend()


def plot_velocities_over_iteration(velocity_list, title):

    vel_tensor = torch.cat(velocity_list, dim=0)

    # Extract errors for variables a, b, c, d, e
    vel_x = vel_tensor[:, 0].cpu().numpy()  # torch.Size([249, 5])
    vel_y = vel_tensor[:, 1].cpu().numpy()
    vel_z = vel_tensor[:, 2].cpu().numpy()

    norm_list = [torch.norm(vel) for vel in velocity_list]

    # Create a figure and axis
    fig, ax = plt.subplots()

    # Plot errors for each variable over the number of iterations
    ax.plot(range(1, len(vel_x) + 1), vel_x, label="vel_x")
    ax.plot(range(1, len(vel_y) + 1), vel_y, label="vel_y")
    ax.plot(range(1, len(vel_z) + 1), vel_z, label="vel_z")
    ax.plot(range(1, len(norm_list) + 1), norm_list, label="Velocity Norm")

    # Set labels and title
    ax.set_xlabel("Iteration")
    ax.set_ylabel(" ")
    ax.set_title(title)

    # Display legend
    ax.legend()


def plot_bodies_over_iteration(errors, title):
    # Convert the list of torch tensors to a single torch tensor
    errors_tensor = torch.cat(errors, dim=0)

    # Extract errors for variables a, b, c, d, e
    errors_a = errors_tensor[:, 0].cpu().numpy()  # torch.Size([249, 5])
    errors_b = errors_tensor[:, 1].cpu().numpy()
    errors_c = errors_tensor[:, 2].cpu().numpy()
    errors_d = errors_tensor[:, 3].cpu().numpy()
    errors_e = errors_tensor[:, 4].cpu().numpy()

    # Create a figure and axis
    fig, ax = plt.subplots()

    # Plot errors for each variable over the number of iterations
    ax.plot(range(1, len(errors) + 1), errors_a, label="BASE")
    ax.plot(range(1, len(errors) + 1), errors_b, label="BOOM")
    ax.plot(range(1, len(errors) + 1), errors_c, label="DIPPER")
    # ax.plot(range(1, len(errors) + 1), errors_d, label="TELE")
    ax.plot(range(1, len(errors) + 1), errors_d, label="ROTO_BASE")

    # Set labels and title
    ax.set_xlabel("Iteration")
    ax.set_ylabel(" ")
    ax.set_title(title)

    # Display legend
    ax.legend()


def plot_mass_matrices(mass_matrices):
    fig, ax = plt.subplots()

    # Calculate the norm for each matrix in the list
    norm_list = [torch.sum(mass_matrix**2).item() for mass_matrix in mass_matrices]

    # Plotting the norms
    ax.plot(range(1, len(norm_list) + 1), norm_list, marker="o", label="Mass Matrix Norm")

    # Adding legend
    ax.legend()

    # Adding labels and title
    ax.set_title("Norm of Mass Matrices in the List")
    ax.set_xlabel("Matrix Index")
    ax.set_ylabel("Norm")


def plot_trajectories(commanded_velocities: torch.Tensor, achieved_velocities: torch.Tensor):
    """
    commanded_velocities: tensor of shape (num_steps, num_joints)
    achieved_velocities: tensor of shape (num_steps, num_joints)
    """
    fig, axs = plt.subplots(4, 1, figsize=(10, 10))
    for i in range(3):
        axs[i].plot(commanded_velocities.cpu().numpy()[:, i], label="Commanded")
        axs[i].plot(achieved_velocities.cpu().numpy()[:, i], label="Achieved")
        axs[i].set_ylim([-1, 1])
        axs[i].legend()


if __name__ == "__main__":
    frequencies = [0.5, 1, 2]  # [0.5, 1, 2]
    joint_names = ["J_BOOM", "J_DIPPER", "J_SHOVEL"]
    joint_dict = {"J_BOOM": 0, "J_DIPPER": 1, "J_SHOVEL": 2}

    main()
    simulation_app.close()

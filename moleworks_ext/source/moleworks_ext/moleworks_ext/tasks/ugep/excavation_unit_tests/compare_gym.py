# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Runs one simulation step with input data from <PERSON>."""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os

from omni.isaac.orbit.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Zero agent for Orbit environments.")
parser.add_argument("--cpu", action="store_true", default=False, help="Use CPU pipeline.")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

args_cli.headless = True

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import matplotlib.pyplot as plt
import torch
import traceback

import carb

import omni.isaac.contrib_tasks  # noqa: F401
import omni.isaac.orbit_tasks  # noqa: F401
from omni.isaac.orbit_tasks.utils import parse_env_cfg


def main():
    """Zero actions agent with Orbit environment."""
    # parse configuration
    env_cfg = parse_env_cfg("Isaac-m545-v0", use_gpu=True, num_envs=10)
    # create environment
    env = gym.make("Isaac-m545-v0", cfg=env_cfg)

    # print info (this is vectorized environment)
    print(f"[INFO]: Gym observation space: {env.observation_space}")
    print(f"[INFO]: Gym action space: {env.action_space}")
    # reset environment
    env.reset()

    # Data to log
    num_resets = 1
    aoas_init = torch.zeros(num_resets, env.num_envs, device=env.device)
    aoas_post = torch.zeros(num_resets, env.num_envs, device=env.device)
    bucket_full_angle_w_init = torch.zeros(num_resets, env.num_envs, device=env.device)
    bucket_full_angle_w_post = torch.zeros(num_resets, env.num_envs, device=env.device)

    # Set-up the exact same conditions as in Gym, values were queried using the debugger
    all_env_ids = torch.arange(env.num_envs, dtype=torch.int64, device=env.device)
    env.soil.reset(idxs=all_env_ids)
    env.termination_excavation.reset(idxs=all_env_ids)

    # Fix the soil
    env.pullup_dist = torch.tensor([3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0, 3.0], device="cuda:0")
    env.soil.max_depth_height.z = torch.tensor(
        [
            [
                -3.0500,
                -3.0500,
                -3.0500,
                -3.0500,
                -3.0500,
                -3.0500,
                -3.0500,
                -3.0500,
                -3.0329,
                -3.0064,
                -2.9771,
                -2.9455,
                -2.9119,
                -2.8766,
                -2.8400,
                -2.8031,
                -2.7662,
                -2.7305,
                -2.6967,
                -2.6861,
                -2.6942,
                -2.7057,
                -2.7201,
                -2.7368,
                -2.7552,
                -2.7745,
                -2.7941,
                -2.8135,
                -2.8321,
                -2.8499,
                -2.8665,
                -2.8824,
                -2.8979,
                -2.9128,
                -2.9279,
                -2.9430,
                -2.9581,
                -2.9734,
                -2.9883,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
            ],
            [
                -2.9672,
                -2.9335,
                -2.8990,
                -2.8648,
                -2.8317,
                -2.8004,
                -2.7719,
                -2.7474,
                -2.7275,
                -2.7131,
                -2.7049,
                -2.7034,
                -2.7089,
                -2.7216,
                -2.7411,
                -2.7667,
                -2.7976,
                -2.8327,
                -2.8705,
                -2.9095,
                -2.9479,
                -2.9844,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -2.9944,
                -2.9819,
                -2.9714,
                -2.9630,
                -2.9568,
                -2.9530,
                -2.9517,
                -2.9530,
                -2.9567,
                -2.9629,
                -2.9712,
                -2.9815,
                -2.9931,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
            ],
            [
                -2.0744,
                -2.0709,
                -2.0658,
                -2.0586,
                -2.0490,
                -2.0371,
                -2.0226,
                -2.0055,
                -1.9858,
                -1.9637,
                -1.9396,
                -1.9136,
                -1.8861,
                -1.8576,
                -1.8286,
                -1.7993,
                -1.7706,
                -1.7427,
                -1.7208,
                -1.7145,
                -1.7106,
                -1.7091,
                -1.7092,
                -1.7104,
                -1.7126,
                -1.7155,
                -1.7190,
                -1.7238,
                -1.7303,
                -1.7399,
                -1.7753,
                -1.8127,
                -1.8507,
                -1.8877,
                -1.9221,
                -1.9527,
                -1.9786,
                -1.9993,
                -2.0146,
                -2.0245,
                -2.0543,
                -2.0808,
                -2.1010,
                -2.1141,
                -2.1199,
                -2.1189,
                -2.1119,
                -2.1000,
                -2.0847,
                -2.0681,
                -2.0516,
                -2.0367,
                -2.0247,
                -2.0167,
                -2.0131,
                -2.0138,
                -2.0183,
                -2.0259,
                -2.0355,
                -2.0460,
                -2.0562,
                -2.0649,
                -2.0716,
                -2.0753,
                -2.0760,
                -2.0736,
                -2.0684,
                -2.0609,
                -2.0516,
                -2.0411,
                -2.0298,
                -2.0179,
                -2.0058,
                -1.9934,
                -1.9806,
                -1.9671,
                -1.9755,
                -2.0028,
                -2.0317,
                -2.0611,
                -2.0896,
            ],
            [
                -2.5190,
                -2.5359,
                -2.5534,
                -2.5715,
                -2.5901,
                -2.6092,
                -2.6286,
                -2.6479,
                -2.6667,
                -2.6846,
                -2.7011,
                -2.7157,
                -2.7282,
                -2.7383,
                -2.7458,
                -2.7509,
                -2.7535,
                -2.7537,
                -2.7518,
                -2.7477,
                -2.7415,
                -2.7333,
                -2.7229,
                -2.7104,
                -2.6958,
                -2.6791,
                -2.6607,
                -2.6407,
                -2.6198,
                -2.5989,
                -2.5786,
                -2.5600,
                -2.5440,
                -2.5317,
                -2.5241,
                -2.5220,
                -2.5260,
                -2.5366,
                -2.5537,
                -2.5774,
                -2.6072,
                -2.6423,
                -2.6816,
                -2.7242,
                -2.7688,
                -2.8141,
                -2.8587,
                -2.9017,
                -2.9422,
                -2.9794,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -2.9868,
                -2.9688,
                -2.9535,
                -2.9413,
                -2.9321,
                -2.9260,
                -2.9225,
                -2.9211,
                -2.9213,
                -2.9225,
                -2.9240,
                -2.9257,
                -2.9271,
            ],
            [
                -2.7019,
                -2.7051,
                -2.7091,
                -2.7143,
                -2.7209,
                -2.7288,
                -2.7378,
                -2.7477,
                -2.7579,
                -2.7678,
                -2.7766,
                -2.7836,
                -2.7878,
                -2.7890,
                -2.7866,
                -2.7802,
                -2.7698,
                -2.7556,
                -2.7377,
                -2.7168,
                -2.6932,
                -2.6678,
                -2.6413,
                -2.6146,
                -2.5885,
                -2.5913,
                -2.6001,
                -2.6038,
                -2.6024,
                -2.5965,
                -2.5868,
                -2.5741,
                -2.5593,
                -2.5435,
                -2.5275,
                -2.5280,
                -2.5456,
                -2.5647,
                -2.5847,
                -2.6046,
                -2.6239,
                -2.6422,
                -2.6591,
                -2.6745,
                -2.6884,
                -2.7010,
                -2.7128,
                -2.7241,
                -2.7355,
                -2.7476,
                -2.7609,
                -2.7762,
                -2.7935,
                -2.8136,
                -2.8364,
                -2.8618,
                -2.8896,
                -2.9191,
                -2.9496,
                -2.9802,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0073,
                -3.0219,
                -3.0310,
                -3.0343,
                -3.0314,
                -3.0222,
                -3.0068,
                -3.0000,
                -3.0000,
                -3.0000,
                -2.9947,
                -2.9672,
                -2.9389,
                -2.9105,
                -2.8827,
                -2.8562,
                -2.8315,
            ],
            [
                -2.2392,
                -2.2425,
                -2.2454,
                -2.2486,
                -2.2529,
                -2.2587,
                -2.2668,
                -2.2777,
                -2.2918,
                -2.3089,
                -2.3289,
                -2.3511,
                -2.3748,
                -2.3989,
                -2.4227,
                -2.4450,
                -2.4649,
                -2.4820,
                -2.4958,
                -2.5057,
                -2.5118,
                -2.5142,
                -2.5128,
                -2.5078,
                -2.4995,
                -2.4880,
                -2.4736,
                -2.4564,
                -2.4371,
                -2.4158,
                -2.3934,
                -2.3703,
                -2.3474,
                -2.3253,
                -2.3047,
                -2.2859,
                -2.2690,
                -2.2542,
                -2.2408,
                -2.2287,
                -2.2168,
                -2.2046,
                -2.1911,
                -2.1759,
                -2.1586,
                -2.1389,
                -2.1171,
                -2.0938,
                -2.0697,
                -2.0458,
                -2.0234,
                -2.0032,
                -1.9864,
                -1.9740,
                -1.9664,
                -1.9640,
                -1.9670,
                -1.9751,
                -1.9880,
                -2.0051,
                -2.0258,
                -2.0496,
                -2.0757,
                -2.1037,
                -2.1331,
                -2.1637,
                -2.1954,
                -2.2281,
                -2.2618,
                -2.2968,
                -2.3331,
                -2.3707,
                -2.4093,
                -2.4487,
                -2.4882,
                -2.5271,
                -2.5642,
                -2.5983,
                -2.6279,
                -2.6518,
                -2.6688,
            ],
            [
                -1.6632,
                -1.6717,
                -1.6790,
                -1.6849,
                -1.6899,
                -1.6943,
                -1.6985,
                -1.7029,
                -1.7080,
                -1.7141,
                -1.7217,
                -1.7310,
                -1.7424,
                -1.7562,
                -1.7725,
                -1.7920,
                -1.8147,
                -1.8408,
                -1.8705,
                -1.9037,
                -1.9398,
                -1.9786,
                -2.0191,
                -2.0600,
                -2.1002,
                -2.1381,
                -2.1721,
                -2.2007,
                -2.2224,
                -2.2361,
                -2.2408,
                -2.2361,
                -2.2217,
                -2.1979,
                -2.1655,
                -2.1254,
                -2.0790,
                -2.0279,
                -1.9738,
                -1.9185,
                -1.8637,
                -1.8112,
                -1.7623,
                -1.7184,
                -1.6804,
                -1.6488,
                -1.6238,
                -1.6054,
                -1.5933,
                -1.5870,
                -1.5854,
                -1.5879,
                -1.5935,
                -1.6014,
                -1.6108,
                -1.6207,
                -1.6309,
                -1.6406,
                -1.6497,
                -1.6580,
                -1.6655,
                -1.6723,
                -1.6786,
                -1.6847,
                -1.6907,
                -1.6970,
                -1.7035,
                -1.7104,
                -1.7174,
                -1.7245,
                -1.7315,
                -1.7378,
                -1.7433,
                -1.7477,
                -1.7509,
                -1.7525,
                -1.7530,
                -1.7523,
                -1.7508,
                -1.7487,
                -1.7465,
            ],
            [
                -2.8355,
                -2.8498,
                -2.8687,
                -2.8916,
                -2.9174,
                -2.9447,
                -2.9724,
                -2.9988,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -2.9902,
                -2.9754,
                -2.9638,
                -2.9557,
                -2.9516,
                -2.9517,
                -2.9560,
                -2.9643,
                -2.9764,
                -2.9916,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
            ],
            [
                -2.0581,
                -2.0661,
                -2.0722,
                -2.0764,
                -2.0785,
                -2.0784,
                -2.0764,
                -2.0735,
                -2.0701,
                -2.0675,
                -2.0666,
                -2.0681,
                -2.0728,
                -2.0817,
                -2.0952,
                -2.1131,
                -2.1354,
                -2.1619,
                -2.1921,
                -2.2247,
                -2.2589,
                -2.2935,
                -2.3275,
                -2.3593,
                -2.3878,
                -2.4122,
                -2.4314,
                -2.4449,
                -2.4525,
                -2.4542,
                -2.4502,
                -2.4418,
                -2.4294,
                -2.4146,
                -2.3986,
                -2.3825,
                -2.3676,
                -2.3546,
                -2.3441,
                -2.3362,
                -2.3310,
                -2.3277,
                -2.3252,
                -2.3228,
                -2.3191,
                -2.3130,
                -2.3033,
                -2.2894,
                -2.2706,
                -2.2472,
                -2.2192,
                -2.1871,
                -2.1515,
                -2.1141,
                -2.0760,
                -2.0384,
                -2.0027,
                -1.9700,
                -1.9414,
                -1.9183,
                -1.9011,
                -1.8902,
                -1.8862,
                -1.8887,
                -1.8974,
                -1.9119,
                -1.9312,
                -1.9540,
                -1.9791,
                -2.0052,
                -2.0310,
                -2.0548,
                -2.0754,
                -2.0921,
                -2.1038,
                -2.1101,
                -2.1109,
                -2.1061,
                -2.0960,
                -2.0814,
                -2.0628,
            ],
            [
                -2.9208,
                -2.9332,
                -2.9388,
                -2.9384,
                -2.9325,
                -2.9220,
                -2.9079,
                -2.8914,
                -2.8738,
                -2.8563,
                -2.8402,
                -2.8265,
                -2.8159,
                -2.8097,
                -2.8085,
                -2.8122,
                -2.8209,
                -2.8346,
                -2.8521,
                -2.8726,
                -2.8947,
                -2.9170,
                -2.9378,
                -2.9556,
                -2.9689,
                -2.9767,
                -2.9781,
                -2.9731,
                -2.9617,
                -2.9447,
                -2.9228,
                -2.8977,
                -2.8706,
                -2.8433,
                -2.8171,
                -2.7932,
                -2.7726,
                -2.7555,
                -2.7422,
                -2.7323,
                -2.7250,
                -2.7195,
                -2.7148,
                -2.7098,
                -2.7037,
                -2.6959,
                -2.6861,
                -2.6742,
                -2.6602,
                -2.6454,
                -2.6301,
                -2.6153,
                -2.6014,
                -2.5896,
                -2.5803,
                -2.5739,
                -2.5704,
                -2.5699,
                -2.5721,
                -2.5769,
                -2.5840,
                -2.5928,
                -2.6034,
                -2.6152,
                -2.6283,
                -2.6427,
                -2.6583,
                -2.6752,
                -2.6933,
                -2.7132,
                -2.7349,
                -2.7586,
                -2.7845,
                -2.8127,
                -2.8432,
                -2.8757,
                -2.9099,
                -2.9451,
                -2.9802,
                -3.0000,
                -3.0000,
            ],
        ],
        device="cuda:0",
    )
    env.soil.soil_height.z = torch.tensor(
        [
            [
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -3.0000,
                -2.9829,
                -2.9564,
                -2.9271,
                -2.8955,
                -2.8619,
                -2.8266,
                -2.7900,
                -2.7531,
                -2.7162,
                -2.6805,
                -2.6467,
                -2.6161,
                -2.5896,
                -2.5684,
                -2.5531,
                -2.5442,
                -2.5419,
                -2.5457,
                -2.5551,
                -2.5686,
                -2.5848,
                -2.6021,
                -2.6185,
                -2.6323,
                -2.6419,
                -2.6458,
                -2.6432,
                -2.6333,
                -2.6163,
                -2.5924,
                -2.5625,
                -2.5278,
                -2.4898,
                -2.4501,
                -2.4104,
                -2.3727,
                -2.3384,
                -2.3088,
                -2.2852,
                -2.2682,
                -2.2584,
                -2.2554,
                -2.2589,
                -2.2684,
                -2.2829,
                -2.3011,
                -2.3218,
                -2.3440,
                -2.3666,
                -2.3887,
                -2.4098,
                -2.4296,
                -2.4481,
                -2.4657,
                -2.4826,
                -2.4994,
                -2.5166,
                -2.5344,
                -2.5531,
                -2.5725,
                -2.5921,
                -2.6115,
                -2.6298,
                -2.6459,
                -2.6592,
                -2.6687,
                -2.6740,
                -2.6747,
                -2.6708,
                -2.6622,
                -2.6502,
                -2.6351,
                -2.6181,
            ],
            [
                -1.7172,
                -1.7168,
                -1.7157,
                -1.7142,
                -1.7128,
                -1.7121,
                -1.7126,
                -1.7152,
                -1.7201,
                -1.7277,
                -1.7381,
                -1.7509,
                -1.7655,
                -1.7810,
                -1.7966,
                -1.8107,
                -1.8222,
                -1.8302,
                -1.8338,
                -1.8322,
                -1.8254,
                -1.8134,
                -1.7967,
                -1.7763,
                -1.7531,
                -1.7286,
                -1.7035,
                -1.6796,
                -1.6575,
                -1.6379,
                -1.6213,
                -1.6078,
                -1.5971,
                -1.5889,
                -1.5825,
                -1.5773,
                -1.5726,
                -1.5680,
                -1.5629,
                -1.5573,
                -1.5510,
                -1.5446,
                -1.5381,
                -1.5321,
                -1.5269,
                -1.5231,
                -1.5212,
                -1.5215,
                -1.5244,
                -1.5299,
                -1.5385,
                -1.5499,
                -1.5642,
                -1.5812,
                -1.6010,
                -1.6227,
                -1.6462,
                -1.6706,
                -1.6952,
                -1.7194,
                -1.7418,
                -1.7620,
                -1.7785,
                -1.7906,
                -1.7975,
                -1.7984,
                -1.7927,
                -1.7799,
                -1.7599,
                -1.7328,
                -1.6988,
                -1.6583,
                -1.6124,
                -1.5620,
                -1.5088,
                -1.4545,
                -1.4009,
                -1.3501,
                -1.3042,
                -1.2651,
                -1.2347,
            ],
            [
                -2.0244,
                -2.0209,
                -2.0158,
                -2.0086,
                -1.9990,
                -1.9871,
                -1.9726,
                -1.9555,
                -1.9358,
                -1.9137,
                -1.8896,
                -1.8636,
                -1.8361,
                -1.8076,
                -1.7786,
                -1.7493,
                -1.7206,
                -1.6927,
                -1.6666,
                -1.6428,
                -1.6223,
                -1.6058,
                -1.5943,
                -1.5886,
                -1.5891,
                -1.5964,
                -1.6106,
                -1.6314,
                -1.6582,
                -1.6899,
                -1.7253,
                -1.7627,
                -1.8007,
                -1.8377,
                -1.8721,
                -1.9027,
                -1.9286,
                -1.9493,
                -1.9646,
                -1.9745,
                -1.9796,
                -1.9806,
                -1.9783,
                -1.9734,
                -1.9670,
                -1.9599,
                -1.9528,
                -1.9463,
                -1.9408,
                -1.9364,
                -1.9335,
                -1.9317,
                -1.9310,
                -1.9311,
                -1.9317,
                -1.9323,
                -1.9324,
                -1.9320,
                -1.9305,
                -1.9277,
                -1.9235,
                -1.9176,
                -1.9101,
                -1.9013,
                -1.8914,
                -1.8810,
                -1.8706,
                -1.8611,
                -1.8533,
                -1.8478,
                -1.8456,
                -1.8475,
                -1.8539,
                -1.8651,
                -1.8810,
                -1.9014,
                -1.9255,
                -1.9528,
                -1.9817,
                -2.0111,
                -2.0396,
            ],
            [
                -1.1208,
                -1.1432,
                -1.1630,
                -1.1802,
                -1.1946,
                -1.2065,
                -1.2162,
                -1.2236,
                -1.2289,
                -1.2322,
                -1.2333,
                -1.2323,
                -1.2291,
                -1.2233,
                -1.2147,
                -1.2030,
                -1.1884,
                -1.1705,
                -1.1499,
                -1.1270,
                -1.1024,
                -1.0771,
                -1.0519,
                -1.0283,
                -1.0072,
                -0.9895,
                -0.9762,
                -0.9675,
                -0.9634,
                -0.9638,
                -0.9681,
                -0.9750,
                -0.9834,
                -0.9924,
                -1.0005,
                -1.0068,
                -1.0107,
                -1.0116,
                -1.0097,
                -1.0052,
                -0.9988,
                -0.9912,
                -0.9836,
                -0.9769,
                -0.9719,
                -0.9693,
                -0.9696,
                -0.9730,
                -0.9794,
                -0.9882,
                -0.9988,
                -1.0107,
                -1.0228,
                -1.0340,
                -1.0435,
                -1.0505,
                -1.0542,
                -1.0541,
                -1.0498,
                -1.0410,
                -1.0278,
                -1.0106,
                -0.9897,
                -0.9661,
                -0.9407,
                -0.9147,
                -0.8895,
                -0.8665,
                -0.8472,
                -0.8328,
                -0.8243,
                -0.8229,
                -0.8289,
                -0.8422,
                -0.8630,
                -0.8902,
                -0.9232,
                -0.9608,
                -1.0016,
                -1.0442,
                -1.0873,
            ],
            [
                -2.5400,
                -2.5289,
                -2.5173,
                -2.5054,
                -2.4928,
                -2.4794,
                -2.4653,
                -2.4501,
                -2.4344,
                -2.4184,
                -2.4029,
                -2.3887,
                -2.3767,
                -2.3683,
                -2.3642,
                -2.3650,
                -2.3711,
                -2.3826,
                -2.3988,
                -2.4188,
                -2.4414,
                -2.4651,
                -2.4883,
                -2.5096,
                -2.5276,
                -2.5413,
                -2.5501,
                -2.5538,
                -2.5524,
                -2.5465,
                -2.5368,
                -2.5241,
                -2.5093,
                -2.4935,
                -2.4775,
                -2.4622,
                -2.4481,
                -2.4358,
                -2.4256,
                -2.4180,
                -2.4129,
                -2.4106,
                -2.4113,
                -2.4148,
                -2.4212,
                -2.4307,
                -2.4432,
                -2.4588,
                -2.4773,
                -2.4990,
                -2.5235,
                -2.5508,
                -2.5804,
                -2.6123,
                -2.6459,
                -2.6810,
                -2.7167,
                -2.7527,
                -2.7883,
                -2.8229,
                -2.8559,
                -2.8864,
                -2.9140,
                -2.9378,
                -2.9573,
                -2.9719,
                -2.9810,
                -2.9843,
                -2.9814,
                -2.9722,
                -2.9568,
                -2.9355,
                -2.9088,
                -2.8773,
                -2.8419,
                -2.8037,
                -2.7637,
                -2.7230,
                -2.6829,
                -2.6442,
                -2.6081,
            ],
            [
                -1.6832,
                -1.6940,
                -1.7042,
                -1.7131,
                -1.7203,
                -1.7249,
                -1.7262,
                -1.7240,
                -1.7175,
                -1.7067,
                -1.6913,
                -1.6715,
                -1.6474,
                -1.6196,
                -1.5886,
                -1.5553,
                -1.5203,
                -1.4848,
                -1.4495,
                -1.4153,
                -1.3828,
                -1.3528,
                -1.3257,
                -1.3016,
                -1.2810,
                -1.2639,
                -1.2502,
                -1.2399,
                -1.2330,
                -1.2292,
                -1.2282,
                -1.2299,
                -1.2340,
                -1.2399,
                -1.2475,
                -1.2563,
                -1.2661,
                -1.2765,
                -1.2876,
                -1.2993,
                -1.3116,
                -1.3247,
                -1.3385,
                -1.3534,
                -1.3691,
                -1.3855,
                -1.4025,
                -1.4195,
                -1.4360,
                -1.4513,
                -1.4650,
                -1.4760,
                -1.4839,
                -1.4885,
                -1.4894,
                -1.4866,
                -1.4803,
                -1.4708,
                -1.4587,
                -1.4448,
                -1.4299,
                -1.4148,
                -1.4003,
                -1.3872,
                -1.3764,
                -1.3683,
                -1.3635,
                -1.3621,
                -1.3644,
                -1.3703,
                -1.3798,
                -1.3922,
                -1.4072,
                -1.4242,
                -1.4424,
                -1.4612,
                -1.4798,
                -1.4975,
                -1.5135,
                -1.5273,
                -1.5386,
            ],
            [
                -1.3469,
                -1.3684,
                -1.3871,
                -1.4021,
                -1.4124,
                -1.4175,
                -1.4164,
                -1.4085,
                -1.3932,
                -1.3706,
                -1.3406,
                -1.3039,
                -1.2616,
                -1.2146,
                -1.1648,
                -1.1137,
                -1.0632,
                -1.0150,
                -0.9707,
                -0.9317,
                -0.8993,
                -0.8740,
                -0.8564,
                -0.8468,
                -0.8449,
                -0.8503,
                -0.8625,
                -0.8806,
                -0.9038,
                -0.9309,
                -0.9610,
                -0.9927,
                -1.0250,
                -1.0565,
                -1.0861,
                -1.1127,
                -1.1352,
                -1.1529,
                -1.1652,
                -1.1717,
                -1.1724,
                -1.1675,
                -1.1575,
                -1.1431,
                -1.1252,
                -1.1049,
                -1.0833,
                -1.0615,
                -1.0405,
                -1.0212,
                -1.0045,
                -0.9908,
                -0.9808,
                -0.9743,
                -0.9714,
                -0.9719,
                -0.9751,
                -0.9809,
                -0.9884,
                -0.9970,
                -1.0062,
                -1.0155,
                -1.0245,
                -1.0333,
                -1.0418,
                -1.0502,
                -1.0590,
                -1.0686,
                -1.0794,
                -1.0917,
                -1.1056,
                -1.1211,
                -1.1381,
                -1.1560,
                -1.1745,
                -1.1927,
                -1.2102,
                -1.2262,
                -1.2405,
                -1.2527,
                -1.2630,
            ],
            [
                -2.6380,
                -2.6372,
                -2.6338,
                -2.6272,
                -2.6175,
                -2.6049,
                -2.5895,
                -2.5716,
                -2.5519,
                -2.5309,
                -2.5094,
                -2.4884,
                -2.4688,
                -2.4512,
                -2.4361,
                -2.4246,
                -2.4168,
                -2.4127,
                -2.4125,
                -2.4159,
                -2.4223,
                -2.4311,
                -2.4414,
                -2.4528,
                -2.4644,
                -2.4756,
                -2.4864,
                -2.4962,
                -2.5052,
                -2.5134,
                -2.5212,
                -2.5286,
                -2.5360,
                -2.5433,
                -2.5506,
                -2.5577,
                -2.5643,
                -2.5700,
                -2.5745,
                -2.5773,
                -2.5780,
                -2.5765,
                -2.5727,
                -2.5665,
                -2.5581,
                -2.5477,
                -2.5356,
                -2.5219,
                -2.5071,
                -2.4913,
                -2.4748,
                -2.4580,
                -2.4415,
                -2.4255,
                -2.4108,
                -2.3980,
                -2.3882,
                -2.3821,
                -2.3804,
                -2.3838,
                -2.3926,
                -2.4069,
                -2.4264,
                -2.4504,
                -2.4779,
                -2.5075,
                -2.5378,
                -2.5672,
                -2.5945,
                -2.6184,
                -2.6382,
                -2.6534,
                -2.6639,
                -2.6701,
                -2.6727,
                -2.6724,
                -2.6702,
                -2.6671,
                -2.6637,
                -2.6606,
                -2.6584,
            ],
            [
                -1.3453,
                -1.3030,
                -1.2589,
                -1.2146,
                -1.1717,
                -1.1312,
                -1.0942,
                -1.0619,
                -1.0347,
                -1.0131,
                -0.9973,
                -0.9871,
                -0.9823,
                -0.9826,
                -0.9878,
                -0.9973,
                -1.0106,
                -1.0275,
                -1.0472,
                -1.0693,
                -1.0930,
                -1.1179,
                -1.1429,
                -1.1673,
                -1.1901,
                -1.2104,
                -1.2275,
                -1.2406,
                -1.2493,
                -1.2531,
                -1.2519,
                -1.2462,
                -1.2365,
                -1.2231,
                -1.2074,
                -1.1904,
                -1.1732,
                -1.1571,
                -1.1432,
                -1.1326,
                -1.1260,
                -1.1239,
                -1.1265,
                -1.1338,
                -1.1453,
                -1.1601,
                -1.1774,
                -1.1959,
                -1.2145,
                -1.2322,
                -1.2481,
                -1.2610,
                -1.2707,
                -1.2771,
                -1.2803,
                -1.2807,
                -1.2794,
                -1.2769,
                -1.2745,
                -1.2734,
                -1.2747,
                -1.2792,
                -1.2877,
                -1.3006,
                -1.3180,
                -1.3396,
                -1.3649,
                -1.3928,
                -1.4220,
                -1.4515,
                -1.4798,
                -1.5053,
                -1.5267,
                -1.5434,
                -1.5541,
                -1.5586,
                -1.5568,
                -1.5485,
                -1.5343,
                -1.5151,
                -1.4913,
            ],
            [
                -1.9448,
                -1.9212,
                -1.8978,
                -1.8763,
                -1.8583,
                -1.8451,
                -1.8374,
                -1.8359,
                -1.8405,
                -1.8505,
                -1.8652,
                -1.8830,
                -1.9026,
                -1.9220,
                -1.9396,
                -1.9539,
                -1.9634,
                -1.9674,
                -1.9650,
                -1.9563,
                -1.9417,
                -1.9220,
                -1.8985,
                -1.8726,
                -1.8463,
                -1.8213,
                -1.7993,
                -1.7820,
                -1.7708,
                -1.7665,
                -1.7695,
                -1.7800,
                -1.7972,
                -1.8207,
                -1.8490,
                -1.8807,
                -1.9145,
                -1.9487,
                -1.9820,
                -2.0131,
                -2.0410,
                -2.0652,
                -2.0850,
                -2.1006,
                -2.1120,
                -2.1198,
                -2.1247,
                -2.1274,
                -2.1290,
                -2.1306,
                -2.1330,
                -2.1372,
                -2.1437,
                -2.1530,
                -2.1652,
                -2.1804,
                -2.1980,
                -2.2174,
                -2.2380,
                -2.2588,
                -2.2791,
                -2.2979,
                -2.3146,
                -2.3288,
                -2.3402,
                -2.3486,
                -2.3544,
                -2.3577,
                -2.3590,
                -2.3589,
                -2.3580,
                -2.3565,
                -2.3549,
                -2.3536,
                -2.3524,
                -2.3514,
                -2.3499,
                -2.3477,
                -2.3440,
                -2.3382,
                -2.3296,
            ],
        ],
        device="cuda:0",
    )
    env.soil.soil_height.offset = torch.tensor(
        [[-2.7417], [-1.5927], [-1.8550], [-1.0728], [-2.4842], [-1.5900], [-1.0200], [-2.5614], [-1.2771], [-1.9256]],
        device="cuda:0",
    )
    env.soil.max_depth_height.offset = torch.tensor(
        [[-2.9527], [-2.9802], [-1.9029], [-2.7841], [-2.6754], [-2.3576], [-1.7297], [-2.9423], [-2.0222], [-2.8226]],
        device="cuda:0",
    )

    # Fix joint states
    joint_pos_pre = torch.tensor(
        [
            [-0.4114, 1.8799, 1.2953, -0.3482],
            [-0.2747, 1.0017, 1.5336, 0.4687],
            [-0.1040, 0.8524, 1.0746, 0.6086],
            [-0.2596, 0.8873, 1.0418, 0.1246],
            [-0.1302, 1.7116, 0.2826, -0.3478],
            [-0.6373, 1.7338, 0.9518, 0.3887],
            [-0.1716, 0.8571, 0.2733, 0.7722],
            [-0.0738, 1.5235, 0.4574, -0.1759],
            [-0.4423, 1.5893, 0.5607, -0.2402],
            [-0.1463, 1.1592, 1.4118, -0.1407],
        ],
        device="cuda:0",
    )
    env.m545_measurements.joint_pos = joint_pos_pre.clone()
    joint_vel_pre = torch.tensor(
        [
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
        ],
        device="cuda:0",
    )
    env.m545_measurements.joint_vel = joint_vel_pre.clone()
    # Fix Root state
    base_pos_pre = torch.tensor(
        [
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
        ],
        device="cuda:0",
    )
    env.m545_measurements.root_pos = base_pos_pre.clone()
    base_quat_pre = torch.tensor(
        [
            [1.0, 0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0, 0.0],
            [1.0, 0.0, 0.0, 0.0],
        ],
        device="cuda:0",
    )
    env.m545_measurements.root_quat = base_quat_pre.clone()
    base_lin_vel = torch.tensor(
        [
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
        ],
        device="cuda:0",
    )
    env.m545_measurements.root_lin_vel_w = base_lin_vel
    base_ang_vel = torch.tensor(
        [
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
        ],
        device="cuda:0",
    )
    env.m545_measurements.root_ang_vel_w = base_ang_vel.clone()

    # Fix Measurements
    mm_pre = torch.tensor(
        [
            [
                [4.7693e04, 2.0594e04, 5.3349e03, 2.8955e03],
                [2.0594e04, 2.1605e04, 1.2241e03, 1.8522e03],
                [5.3349e03, 1.2241e03, 1.5195e03, 4.9746e02],
                [2.8955e03, 1.8522e03, 4.9746e02, 5.0555e02],
            ],
            [
                [7.5816e04, 3.6674e04, 4.4470e03, 3.8071e03],
                [3.6674e04, 2.5641e04, 8.1188e02, 2.6814e03],
                [4.4470e03, 8.1188e02, 1.5195e03, 8.5226e01],
                [3.8071e03, 2.6814e03, 8.5226e01, 5.0555e02],
            ],
            [
                [7.1367e04, 3.1947e04, 3.9760e03, 3.5301e03],
                [3.1947e04, 2.0637e04, 7.2705e02, 2.3922e03],
                [3.9760e03, 7.2705e02, 1.5195e03, 3.9310e-01],
                [3.5301e03, 2.3922e03, 3.9310e-01, 5.0555e02],
            ],
            [
                [7.0955e04, 3.1464e04, 4.3560e03, 3.8512e03],
                [3.1464e04, 2.0084e04, 1.0101e03, 2.2617e03],
                [4.3560e03, 1.0101e03, 1.5195e03, 2.8343e02],
                [3.8512e03, 2.2617e03, 2.8343e02, 5.0555e02],
            ],
            [
                [4.5164e04, 1.4886e04, 5.4965e03, 2.7569e03],
                [1.4886e04, 1.2718e04, 1.2240e03, 1.4981e03],
                [5.4965e03, 1.2240e03, 1.5195e03, 4.9730e02],
                [2.7569e03, 1.4981e03, 4.9730e02, 5.0555e02],
            ],
            [
                [4.8182e04, 1.9747e04, 5.1178e03, 2.4220e03],
                [1.9747e04, 1.9422e04, 8.5974e02, 2.3226e03],
                [5.1178e03, 8.5974e02, 1.5195e03, 1.3308e02],
                [2.4220e03, 2.3226e03, 1.3308e02, 5.0555e02],
            ],
            [
                [5.9019e04, 2.2135e04, 3.8901e03, 2.7538e03],
                [2.2135e04, 1.3360e04, 6.2796e02, 1.8497e03],
                [3.8901e03, 6.2796e02, 1.5195e03, -9.8699e01],
                [2.7538e03, 1.8497e03, -9.8699e01, 5.0555e02],
            ],
            [
                [5.0519e04, 1.8393e04, 5.4672e03, 3.0107e03],
                [1.8393e04, 1.4376e04, 1.1567e03, 1.7330e03],
                [5.4672e03, 1.1567e03, 1.5195e03, 4.3005e02],
                [3.0107e03, 1.7330e03, 4.3005e02, 5.0555e02],
            ],
            [
                [4.9868e04, 1.8421e04, 5.4980e03, 2.9895e03],
                [1.8421e04, 1.5084e04, 1.1835e03, 1.7135e03],
                [5.4980e03, 1.1835e03, 1.5195e03, 4.5681e02],
                [2.9895e03, 1.7135e03, 4.5681e02, 5.0555e02],
            ],
            [
                [7.0303e04, 3.2806e04, 5.0962e03, 3.7740e03],
                [3.2806e04, 2.3419e04, 1.1413e03, 2.1890e03],
                [5.0962e03, 1.1413e03, 1.5195e03, 4.1466e02],
                [3.7740e03, 2.1890e03, 4.1466e02, 5.0555e02],
            ],
        ],
        device="cuda:0",
    )
    env.m545_measurements.mm = mm_pre
    jac_lin_T_dof_pre = torch.tensor(
        [
            [
                [-2.4306, 0.0000, -3.8437],
                [-3.5664, 0.0000, -1.2407],
                [0.1021, 0.0000, -0.9948],
                [-0.2996, 0.0000, -0.5344],
            ],
            [
                [-1.6589, 0.0000, -6.1492],
                [-2.4294, 0.0000, -3.4157],
                [0.7472, 0.0000, -0.6646],
                [-0.3390, 0.0000, -0.5103],
            ],
            [
                [-1.9618, 0.0000, -5.7973],
                [-2.2566, 0.0000, -2.9727],
                [0.7327, 0.0000, -0.6805],
                [-0.4166, 0.0000, -0.4492],
            ],
            [
                [-0.8617, 0.0000, -6.0514],
                [-1.5907, 0.0000, -3.3066],
                [0.8094, 0.0000, -0.5873],
                [-0.0873, 0.0000, -0.6064],
            ],
            [
                [-2.3024, 0.0000, -3.6575],
                [-2.6711, 0.0000, -0.8415],
                [-0.0106, 0.0000, -0.9999],
                [-0.3581, 0.0000, -0.4970],
            ],
            [
                [-1.2619, 0.0000, -4.3633],
                [-2.9517, 0.0000, -2.0808],
                [0.4567, 0.0000, -0.8896],
                [-0.4706, 0.0000, -0.3922],
            ],
            [
                [-1.1452, 0.0000, -5.2172],
                [-1.6301, 0.0000, -2.4189],
                [0.7741, 0.0000, -0.6331],
                [-0.4596, 0.0000, -0.4050],
            ],
            [
                [-2.5897, 0.0000, -3.9808],
                [-2.7991, 0.0000, -1.1485],
                [0.1208, 0.0000, -0.9927],
                [-0.3778, 0.0000, -0.4822],
            ],
            [
                [-1.1709, 0.0000, -4.5526],
                [-2.3864, 0.0000, -1.9859],
                [0.4112, 0.0000, -0.9116],
                [-0.1796, 0.0000, -0.5857],
            ],
            [
                [-2.4667, 0.0000, -5.5346],
                [-2.8808, 0.0000, -2.7250],
                [0.5294, 0.0000, -0.8484],
                [-0.1592, 0.0000, -0.5916],
            ],
        ],
        device="cuda:0",
    )
    env.m545_measurements.bucket_jac_lin_T_dof = jac_lin_T_dof_pre.clone()
    bucket_jac_rot_T_dof_pre = torch.tensor(
        [
            [[0.0, 1.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0], [0.0, 1.0, 0.0]],
            [[0.0, 1.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0], [0.0, 1.0, 0.0]],
            [[0.0, 1.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0], [0.0, 1.0, 0.0]],
            [[0.0, 1.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0], [0.0, 1.0, 0.0]],
            [[0.0, 1.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0], [0.0, 1.0, 0.0]],
            [[0.0, 1.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0], [0.0, 1.0, 0.0]],
            [[0.0, 1.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0], [0.0, 1.0, 0.0]],
            [[0.0, 1.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0], [0.0, 1.0, 0.0]],
            [[0.0, 1.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0], [0.0, 1.0, 0.0]],
            [[0.0, 1.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 0.0], [0.0, 1.0, 0.0]],
        ],
        device="cuda:0",
    )
    env.m545_measurements.bucket_jac_rot_T_dof = bucket_jac_rot_T_dof_pre.clone()
    gravity_tau_pre = torch.tensor(
        [
            [105251.8828, 17844.4863, 14827.9658, 5205.3877],
            [140318.0156, 48492.7070, 9906.3818, 4970.7607],
            [137197.3906, 42266.0703, 10143.4912, 4375.4390],
            [138292.2812, 46081.4531, 8754.0752, 5906.7529],
            [106930.3359, 12297.5586, 14905.0850, 4841.7651],
            [106819.2109, 30233.1484, 13260.6035, 3820.8672],
            [128087.0391, 34057.8242, 9436.9365, 3945.2231],
            [111896.0000, 16701.3105, 14796.8389, 4697.6011],
            [114132.6953, 27950.5664, 13587.5723, 5705.3843],
            [133000.0156, 38583.4766, 12645.5029, 5762.7090],
        ],
        device="cuda:0",
    )
    env.m545_measurements.gravity_tau = gravity_tau_pre.clone()
    bucket_pos_w_pre = torch.tensor(
        [
            [4.6775, -0.2920, -2.3191],
            [6.9108, -0.2920, -1.5545],
            [6.4037, -0.2920, -1.8543],
            [7.2196, -0.2920, -0.6392],
            [4.3826, -0.2920, -2.1995],
            [4.8480, -0.2920, -1.1341],
            [5.7278, -0.2920, -1.0232],
            [4.6671, -0.2920, -2.4869],
            [5.5855, -0.2920, -1.0100],
            [6.5987, -0.2920, -2.2938],
        ],
        device="cuda:0",
    )
    env.m545_measurements.bucket_pos_w = bucket_pos_w_pre.clone()
    prev_bucket_pos_w_pre = torch.tensor(
        [
            [4.6775, -0.2920, -2.3191],
            [6.9108, -0.2920, -1.5545],
            [6.4037, -0.2920, -1.8543],
            [7.2196, -0.2920, -0.6392],
            [4.3826, -0.2920, -2.1995],
            [4.8480, -0.2920, -1.1341],
            [5.7278, -0.2920, -1.0232],
            [4.6671, -0.2920, -2.4869],
            [5.5855, -0.2920, -1.0100],
            [6.5987, -0.2920, -2.2938],
        ],
        device="cuda:0",
    )
    env.m545_measurements.prev_bucket_pos_w = prev_bucket_pos_w_pre.clone()
    bp_unit_vec_pre = torch.tensor(
        [
            [0.7683, -0.0000, 0.6400],
            [0.8144, -0.0000, 0.5803],
            [0.8971, -0.0000, 0.4419],
            [0.4867, -0.0000, 0.8736],
            [0.8358, -0.0000, 0.5490],
            [0.9462, -0.0000, 0.3236],
            [0.9369, -0.0000, 0.3495],
            [0.8572, -0.0000, 0.5149],
            [0.6154, -0.0000, 0.7882],
            [0.5876, -0.0000, 0.8091],
        ],
        device="cuda:0",
    )
    env.m545_measurements.bp_unit_vector_w = bp_unit_vec_pre.clone()
    bucekt_vel_pre = torch.tensor(
        [
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0],
        ],
        device="cuda:0",
    )
    env.m545_measurements.bucket_vel_w = bucekt_vel_pre.clone()

    # Like in reset cache
    env.soil.update_1(all_env_ids)
    # Replace env.soil.set_bucket_fill_state() by the lines below
    env.soil.bucket_state.fill_ratio = torch.tensor(
        [[0.6634], [0.0000], [0.0000], [0.0000], [0.0000], [0.0000], [0.4891], [0.0000], [0.0000], [0.0000]],
        device="cuda:0",
    )
    env.soil.bucket_state.fill_area = torch.tensor(
        [[0.2771], [0.0000], [0.0000], [0.0000], [0.0000], [0.0000], [0.2043], [0.0000], [0.0000], [0.0000]],
        device="cuda:0",
    )
    env.soil.bucket_state.swept_area = torch.tensor(
        [[0.2771], [0.0000], [0.0000], [0.0000], [0.0000], [0.0000], [0.2043], [0.0000], [0.0000], [0.0000]],
        device="cuda:0",
    )
    env.soil.update_2()

    # Bucket force and moment
    bucket_force_com = torch.tensor(
        [
            [16180.0068, 0.0000, -1889.7251],
            [0.0000, 0.0000, -19.3903],
            [0.0000, 0.0000, -19.3903],
            [0.0000, 0.0000, -19.3903],
            [0.0000, 0.0000, -19.3903],
            [0.0000, 0.0000, -19.3903],
            [12643.4658, 0.0000, -6104.9521],
            [0.0000, 0.0000, -19.3903],
            [0.0000, 0.0000, -19.3903],
            [0.0000, 0.0000, -19.3903],
        ],
        device="cuda:0",
    ).clone()
    env.bucket_force_com = bucket_force_com.clone()
    bucket_moment_com = torch.tensor(
        [
            [0.0000, -14537.2363, 0.0000],
            [0.0000, 0.0000, 0.0000],
            [0.0000, 0.0000, 0.0000],
            [0.0000, 0.0000, 0.0000],
            [0.0000, 0.0000, 0.0000],
            [0.0000, 0.0000, 0.0000],
            [0.0000, -11120.5703, 0.0000],
            [0.0000, 0.0000, 0.0000],
            [0.0000, 0.0000, 0.0000],
            [0.0000, 0.0000, 0.0000],
        ],
        device="cuda:0",
    )
    env.bucket_moment_com = bucket_moment_com.clone()
    # Update limits for those conditions
    env.limits.update(env.m545_measurements.joint_pos)

    # Check if rejection conditions are false
    env.soil.soil_parameters.aplha = torch.tensor(
        [[-0.1684], [-0.3282], [-0.1036], [0.1329], [0.0645], [0.1522], [0.0748], [-0.1360], [-0.0135], [0.0575]],
        device="cuda:0",
    )
    violating_max_depth = (
        env.m545_measurements.bucket_pos_w[all_env_ids, 2:]
        < env.soil.get_max_depth_height_at_pos(
            env.m545_measurements.bucket_pos_w[all_env_ids, 0:1], env_ids=all_env_ids
        )
        - env.cfg.terminations_excavation.max_depth_overshoot
    )
    too_high_z = env.soil.get_bucket_depth()[all_env_ids] < env.cfg.reset.arm_depth_limits.lower
    too_low_z = env.soil.get_bucket_depth()[all_env_ids] > env.cfg.reset.arm_depth_limits.upper * (
        1.0 - env.cfg.reset.only_above_soil
    )
    too_small_ang = env.soil.get_ssp_ang_to_soil()[all_env_ids] < env.cfg.reset.min_ang_bucket_to_soil
    invalid_soil_model_state = env.soil.is_state_invalid(all_env_ids)
    too_close_x = (
        torch.abs(env.m545_measurements.bucket_pos_w[all_env_ids, 0:1])
        < env.pullup_dist[all_env_ids].unsqueeze(-1) + env.curriculum_excavation.curr_pullup_band
    )

    print("too_close_x", too_close_x)
    print("violating_max_depth", violating_max_depth)
    print("too_high_z", too_high_z)
    print("too_low_z", too_low_z)
    print("too_small_ang", too_small_ang)
    print("invalid_soil_model_state", invalid_soil_model_state)

    # Write the new state
    env.scene.articulations["robot"].write_root_pose_to_sim(
        torch.cat(
            (
                env.m545_measurements.root_pos_w[all_env_ids].clone(),
                env.m545_measurements.root_quat_w[all_env_ids].clone(),
            ),
            dim=-1,
        ),
        env_ids=all_env_ids,
    )
    env.scene.articulations["robot"].write_root_velocity_to_sim(
        torch.zeros((env.num_envs, 6), device=env.device)[all_env_ids], env_ids=all_env_ids
    )
    env.scene.articulations["robot"].write_joint_state_to_sim(
        position=env.m545_measurements.joint_pos[all_env_ids].clone(),
        velocity=env.m545_measurements.joint_vel[all_env_ids].clone(),
        env_ids=all_env_ids,
    )
    env.torques = torch.tensor(
        [
            [-58650.6055, 52052.7188, -18360.4199, 13169.5010],
            [-140437.2500, -48558.9375, -9919.2686, -4980.6553],
            [-137309.7969, -42323.7109, -10156.6865, -4384.1484],
            [-138409.6250, -46145.5664, -8765.4629, -5918.5103],
            [-107001.2578, -12313.8760, -14924.4746, -4851.4028],
            [-106903.8125, -30273.4941, -13277.8535, -3828.4727],
            [-134337.4062, -17094.5938, -23088.9102, 10514.3789],
            [-111973.1875, -16723.5801, -14816.0869, -4706.9517],
            [-114220.9688, -27989.0723, -13605.2480, -5716.7407],
            [-133107.3281, -38636.3164, -12661.9531, -5774.1797],
        ],
        device="cuda:0",
    )
    env.ext_f_tau = torch.tensor(
        [
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
        ],
        device="cuda:0",
    )
    env.ext_m_tau = torch.tensor(
        [
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
        ],
        device="cuda:0",
    )
    env.inertial_tau = torch.tensor(
        [
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
            [0.0, 0.0, 0.0, 0.0],
        ],
        device="cuda:0",
    )

    env.update_derived_measurements()
    # --------- 1 Step
    env.actions[:] = torch.tensor(
        [
            [0.0723, -0.0522, 0.4055, -0.0610],
            [0.1683, 0.1793, -0.5962, -0.2341],
            [0.0374, -0.0800, 0.5763, -0.0225],
            [0.6876, 0.0624, 0.2517, 0.0039],
            [0.0123, 0.2088, -0.5582, 0.3017],
            [0.0412, -0.1894, 0.0069, 0.6626],
            [0.3698, -0.7373, -0.1944, 0.4777],
            [0.1904, 0.0591, -0.5478, 0.4271],
            [-0.0652, -0.2940, -0.1140, 0.6604],
            [-0.4873, -0.0686, 0.3846, -0.1024],
        ],
        device="cuda:0",
    )
    env.soil.soil_parameters.alpha = torch.tensor(
        [[-0.1684], [-0.3282], [-0.1036], [0.1329], [0.0645], [0.1522], [0.0748], [-0.1360], [-0.0135], [0.0575]],
        device="cuda:0",
    )
    env.clipped_scaled_actions[:] = torch.clip(
        env.actions * env.action_scaling, env.vel_limits_lower, env.vel_limits_upper
    )
    env.last_fill_ratio[:] = env.soil.get_fill_ratio()
    env.inter_decimation_soil_model_invalid[:] = False
    env.inter_decimation_self_collision[:] = False

    # perform physics stepping
    # Pre-physics step
    env.pre_physics_step()
    # set actions into buffers, both are doing the same, one is orbit fashion and other Gym fashion
    env.action_manager.apply_action()
    # set actions into simulator
    env.scene.write_data_to_sim()
    # simulate
    env.sim.step(render=False)
    # Post-physics step
    env.post_physics_step()

    # Second one
    # Pre-physics step
    env.pre_physics_step()
    # set actions into buffers, both are doing the same, one is orbit fashion and other Gym fashion
    env.action_manager.apply_action(env)
    # set actions into simulator
    env.scene.write_data_to_sim()
    # simulate
    env.sim.step(render=False)
    # Post-physics step
    env.post_physics_step()
    # Update derived measurements
    env.m545_measurements.update_derived_measurements(all_env_ids, all_env_ids.shape[0])

    # ---------
    # Data comparison done with the debugger

    # close the simulator
    env.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()

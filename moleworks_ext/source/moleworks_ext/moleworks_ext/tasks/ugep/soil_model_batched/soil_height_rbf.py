# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    pass

import matplotlib.pyplot as plt
import numpy as np
import pickle
import torch

# RBF implenentation based on https://towardsdatascience.com/implement-a-gaussian-process-from-scratch-2a074a470bce


class SoilHeightRBF:
    def __init__(self, cfg, cfg_name, device, num_envs, upper_limit=None, log_time=False):
        """init SoilHeightRBF class

        Args:
            x: num_points; min - max distance cabin - bucket (in world frame)
            n_envs:
            theta: rbf length scale
            scale: magnitude of rbf
        """
        self.cfg = cfg
        self.cfg_name = cfg_name
        self.device = device
        self.n_envs = num_envs
        self.class_cfg = vars(self.cfg)[self.cfg_name]
        self.upper_limit = upper_limit
        self.scale = self.class_cfg.scale
        self.theta = self.class_cfg.theta

        steps = int(np.ceil((self.class_cfg.x_max - self.class_cfg.x_min) / self.class_cfg.min_resolution) + 1)
        # size (steps, )
        self.x = torch.linspace(self.class_cfg.x_min, self.class_cfg.x_max, steps, device=self.device)
        # size (n_envs, steps)
        self.x_expanded = self.x.expand(self.n_envs, -1)
        self.x_dim = self.x.shape[0]
        self.x_dist = torch.diff(self.x)[0]
        # size (n_envs, 1)
        self.x_dist_expanded = self.x_dist.expand(self.n_envs).view(self.n_envs, -1)
        # (1, )
        self.max_upper_idx = (self.x_dim - 1) * torch.ones(1, device=self.device, dtype=torch.int64)
        self.min_lower_idx = torch.zeros(1, device=self.device, dtype=torch.int64)

        self.z = torch.zeros(self.n_envs, self.x_dim, device=self.device, dtype=torch.float)
        # correlation matrix is constant if num points and theta are the fixed
        self.cov_mat = torch.diag(self.x)  # values not used, just to initialize matrix
        self.compute_norm_transform(self.theta)
        self.sample()

        self.log_time = log_time
        if self.log_time:
            self.time_log = {}

    def compute_norm_transform(self, theta):
        """Compute new norm_transform defines correleation between points.
        See docs for derivation.

        Args:
            theta (float): 0: flat soil,  > 0 more random
        """
        theta = max(min(theta, 1.0), 0.0)

        for i in range(self.x_dim):
            self.cov_mat[i, :] = torch.exp(-(theta * (self.x[i] - self.x) ** 2))

        # self.scale_tril = torch.linalg.cholesky(
        #     self.cov_mat + torch.diag(torch.ones(self.x_dim) * 1e-5)
        # )
        # self.distribution = torch.distributions.MultivariateNormal(
        #     self.mu, scale_tril=self.scale_tril, validate_args=False
        # )

        # sampling multivariate normal
        # https://stackoverflow.com/questions/6142576/sample-from-multivariate-normal-gaussian-distribution-in-c

        eig_vals, eig_vecs = torch.linalg.eigh(self.cov_mat)
        eig_vals[eig_vals < 0.0] = 0.0  # fix numerical issues
        self.norm_transform = eig_vecs @ torch.diag(torch.sqrt(eig_vals))

    def sample(self, idxs=...):
        """Resamples new soil for given indices.
        Height = mu + scale * A @ rand_vec
        where A is the norm_transform matrix.

        - scales rbf
        - applies offset
            - if value: average is that value,
            - if none and upper limiif: sampled between min and upper_limit.offset
            - if none and no upper limits: sampled between min and max
            -
        - clips by z_min, z_max
        - clips by array if not None (for max depth)

        Args:
            idxs List[int]: Env idx to resample soil
            upper_limit: if not None, array that gives upper limit (with margin from cfg)


        """
        # last dim needed for broadcasting
        if idxs == ...:
            dim0 = self.n_envs
        else:
            dim0 = len(idxs)

        # which type of shape ["rbf", "slope"]
        if self.class_cfg.type == "rbf":
            rand_vec = torch.randn(dim0, self.x_dim, 1, dtype=self.x.dtype, device=self.device)
            self.z[idxs] = self.scale * (self.norm_transform @ rand_vec).squeeze()  # 0-mean

        # for testing purpose
        elif self.class_cfg.type == "slope":
            print(f"[{self.cfg_name}]: Type Slope!")
            self._compute_slope()  # fills self.zc

        if self.class_cfg.offset is None:
            if self.upper_limit != None:
                u = self.upper_limit.offset
            else:
                u = self.class_cfg.z_max
            l = self.class_cfg.z_min
            self.offset = (u - l) * torch.rand(dim0, 1, device=self.device) + l
        elif isinstance(self.class_cfg.offset, dict):
            self.offset = (
                -self.z[idxs, self.class_cfg.offset["height_idx"]] + self.class_cfg.offset["height"]
            ).unsqueeze(-1)

        else:
            self.offset = self.class_cfg.offset

        self.z[idxs] += self.offset

        # clip by min max in cfg
        self.z[idxs] = torch.clip(self.z[idxs], min=self.class_cfg.z_min, max=self.class_cfg.z_max)

        # clip by other array (max depth should always be under soil height)
        if self.upper_limit != None:
            self.z[idxs] = torch.clip(
                self.z[idxs],
                max=self.upper_limit.z[idxs] - self.class_cfg.clip_margin,
            )

    def get_height(self, val_in, env_ids=...):
        """interpolates soil height for given points x

        Args:
            val: (n_envs,n_points) value for which height is requested

        Returns:
            heights: (n_envs,n_points) soil heights at x


        - we assert that we have constant spacing (and this avoids also division by 0!)
        - if val outside of x, returns heights at boundaries

        """
        if env_ids == ...:
            dim0 = self.n_envs
        else:
            dim0 = len(env_ids)

        # ensure that the value is within the range
        val = torch.clip(val_in, self.x_expanded[env_ids, 0:1], self.x_expanded[env_ids, -1:])

        upper, lower = self._find_upper_and_lower_idxs(val)

        nom = val - torch.gather(self.x_expanded[:dim0], 1, lower)
        weights = nom / self.x_dist

        res = torch.lerp(
            torch.gather(self.z[env_ids], 1, lower),
            torch.gather(self.z[env_ids], 1, upper),
            weights,
        )
        return res

    def get_angle_to_world(self, val_in, env_ids=...):
        """gets angle of soil relative to world for point val
                - if exactly at knot (upper==lower) return average slope of the two neighboring segments
                - if outside or at start or and of self.x, return 0 angle (we return constant height in this case)

        Args:
            x: (n_envs,) value for which slope is requested

        Returns:
            slopes: (n_envs,) soil slopes at val in radians

        """
        if env_ids == ...:
            dim0 = self.n_envs
        else:
            dim0 = len(env_ids)

        val = torch.clip(val_in, self.x_expanded[env_ids, 0:1], self.x_expanded[env_ids, -1:])

        upper, lower = self._find_upper_and_lower_idxs(val)
        # get neighbors if exactly at knot and not at upper or lower end
        upper = self._clip_upper_indices(
            torch.where(
                torch.logical_and(
                    torch.logical_and(upper != self.x_dim - 1, lower != 0),
                    upper == lower,
                ),
                upper + 1,
                upper,
            )
        )
        lower = self._clip_lower_indices(
            torch.where(
                torch.logical_and(
                    torch.logical_and(upper != self.x_dim - 1, lower != 0),
                    upper == lower,
                ),
                lower - 1,
                lower,
            )
        )

        # if exactly at knot, 2x dist (if at bounds, dy will be 0 and we do NOT get division by 0)
        dx = torch.where(
            upper == lower,
            2.0 * self.x_dist_expanded[:dim0],
            self.x_dist_expanded[:dim0],
        )

        dy = torch.gather(self.z[env_ids], 1, upper) - torch.gather(self.z[env_ids], 1, lower)
        return torch.atan2(dy, dx)

    def integrate(self, x0, x1):
        """gives the area under the soil
        linear interpolation between the two points (trapezoid with 1 step)
        (we also do not have the shovel position at higher resolution)

        what to do with negative soil -> shovel depth (integration) will be more negative -> positive volume

        Args:
            x0, x1: (n_envs,1) integration interval

        Returns:
            area under the soil (n_envs, 1)
        """

        y0 = self.get_height(x0)
        y1 = self.get_height(x1)

        return 0.5 * (x1 - x0) * (y1 + y0)

        pass

    def update_soil_height(self, z_s, x_s, env_ids=...):
        if (len(z_s) <= 1) or (len(x_s) <= 1):
            return

        # Convert to the appropriate device
        z_s, x_s = z_s.to(self.device), x_s.to(self.device)

        # # Plot the original soil height
        # plt.plot(self.x.cpu().numpy(), self.z[0].cpu().numpy(), label='Soil Height')
        # plt.plot(x_s.cpu().numpy(), z_s.cpu().numpy(), label='Shovel Trajectory')

        if env_ids == ...:
            env_ids = torch.arange(self.n_envs, device=self.device)

        # Find the points where x_s starts to increase again and z_s is increasing
        dx_s = torch.diff(x_s)
        dz_s = torch.diff(z_s)
        # We find where the xs starts to increase
        turning_points = torch.where(dx_s > 0)[0]
        if len(turning_points) > 0:
            # We choose the first turning point as a cut-off
            cut_off_index = turning_points[0].item()
            # Further ensure that z_s is increasing after this turning point to avoid cutting off valid parts
            if dz_s[cut_off_index:].min() > 0:
                x_s = x_s[: cut_off_index + 1]
                z_s = z_s[: cut_off_index + 1]

        # now sort them
        x_s, sort_idx = torch.sort(x_s)
        z_s = z_s[sort_idx]

        # Ensuring the shovel trajectory only updates soil heights within its x range
        # The updated segment will now properly apply only where the shovel position exists
        valid_range = (self.x.unsqueeze(0) >= x_s.min()) & (self.x.unsqueeze(0) <= x_s.max())

        upper, lower = self._find_upper_and_lower_idxs(self.x.unsqueeze(0), ref_array=x_s)
        upper = upper[0].clamp(max=x_s.shape[0] - 1)
        lower = lower[0].clamp(max=x_s.shape[0] - 1)

        nom = (x_s[upper] - x_s[lower]).clamp(min=1e-5)
        weights = ((self.x - x_s[lower]) / nom).clamp(0, 1)

        z_s_interp = torch.lerp(z_s[lower], z_s[upper], weights)
        z_s_interp = z_s_interp.expand(len(env_ids), -1)
        # plt.plot(self.x.cpu().numpy(), z_s_interp[0].cpu().numpy(), label='Interpolated Soil Height')

        # Apply the valid range mask to prevent outside updates
        mask = (self.z[env_ids, :] > z_s_interp) & valid_range
        # print("mask shape = ", mask.shape)
        # print("z_s_interp shape = ", z_s_interp.shape)
        # print("self.z shape = ", self.z.shape)
        self.z[env_ids, :] = self.z[env_ids, :] * (~mask) + z_s_interp * mask

    def make_trench(self, midpoint, depth, bottom_length, slope_tan):
        bottom_ids = bottom_length // self.x_dist
        midpoint_idx = midpoint // self.x_dist
        min_idx = torch.zeros(1, device=self.device)
        max_idx = torch.tensor(self.z.shape[-1] - 1, device=self.device)
        bottom_start_idx = int(torch.maximum(midpoint_idx - bottom_ids // 2, min_idx))
        bottom_end_idx = int(torch.minimum(torch.ceil(midpoint_idx + bottom_ids / 2), max_idx))
        slope_start_idx_left = int(torch.maximum(bottom_start_idx - (depth / slope_tan) // self.x_dist, min_idx))
        slope_end_idx_right = int(
            torch.minimum(torch.ceil(bottom_end_idx + (depth / slope_tan) / self.x_dist), max_idx)
        )

        self.z[:] = 0.0
        slope_ids = torch.arange(
            bottom_start_idx - slope_start_idx_left,
            dtype=torch.float32,
            device=self.device,
        )
        self.z[:, slope_start_idx_left:bottom_start_idx] = 0.0 - slope_tan * slope_ids * self.x_dist
        self.z[:, bottom_start_idx:bottom_end_idx] = -depth
        slope_ids = torch.arange(
            slope_end_idx_right - bottom_end_idx,
            dtype=torch.float32,
            device=self.device,
        )
        self.z[:, bottom_end_idx:slope_end_idx_right] = -depth + slope_tan * slope_ids * self.x_dist

    def _find_upper_and_lower_idxs(self, val, ref_array=None):
        if ref_array is None:
            ref_array = self.x

        ref_array_expanded = ref_array.expand(val.shape[0], -1)

        # searchsorted returns the idx of x, where
        upper = torch.minimum(torch.searchsorted(ref_array, val), torch.tensor(ref_array.shape[0] - 1))
        # upper = upper.unsqueeze(1)  # Ensure upper has the correct shape

        lower = self._clip_lower_indices(
            torch.where(
                torch.gather(ref_array_expanded, 1, upper) == val,
                upper,
                upper - 1,
            )
        )
        return upper, lower

    def _clip_upper_indices(self, upper):
        upper_val = torch.minimum(upper, self.max_upper_idx)
        return upper_val

    def _clip_lower_indices(self, lower):
        lower_val = torch.maximum(lower, self.min_lower_idx)
        return lower_val

    def _compute_slope(self):
        slope_start_idx = int((self.class_cfg.slope_start - self.class_cfg.x_min) / self.x_dist)
        slope_idx_len = int(self.class_cfg.slope_x_len / self.x_dist)
        slope_end_idx = slope_start_idx + slope_idx_len

        slope_ang = torch.tensor(self.class_cfg.slope_ang, device=self.device)
        self.z[:, 0:slope_start_idx] = 0.0
        self.z[:, slope_start_idx:slope_end_idx] = torch.tan(slope_ang) * torch.linspace(
            0, (slope_idx_len - 1) * self.x_dist, slope_idx_len, device=self.device
        )
        self.z[:, slope_end_idx:] = torch.tan(slope_ang) * slope_idx_len * self.x_dist

        self.z += self.class_cfg.slope_start_height

    def compute_soil_amount(self, x_min, x_max, env_ids=...):
        """Computes the amount of soil remaining by integrating the height along the x-axis.

        Args:
            x_min (torch.Tensor): Tensor of minimum x-coordinates of size (N,).
            x_max (torch.Tensor): Tensor of maximum x-coordinates of size (N,).
            env_ids (Optional[torch.Tensor]): Tensor of environment IDs to consider.

        Returns:
            torch.Tensor: Integrated soil amount for each pair of x_min and x_max.
        """
        # Determine the dimensions based on env_ids
        if env_ids == ...:
            dim0 = self.n_envs
        else:
            dim0 = len(env_ids)

        # Clip x_min and x_max to be within bounds
        x_min = torch.clamp(x_min, min=self.class_cfg.x_min, max=self.class_cfg.x_max)
        x_max = torch.clamp(x_max, min=self.class_cfg.x_min, max=self.class_cfg.x_max)

        # Expand x to match batch size of x_min and x_max and apply env_ids filtering
        expanded_x = self.x.unsqueeze(0).expand(dim0, -1)
        expanded_x = expanded_x[env_ids]

        # Create masks for each range in x
        mask = (expanded_x >= x_min.unsqueeze(1)) & (expanded_x <= x_max.unsqueeze(1))

        # Expand z to match the batch size and apply env_ids filtering
        expanded_z = self.z.expand_as(mask)
        expanded_z = expanded_z[env_ids]

        # Apply the mask to the soil heights
        masked_heights = expanded_z * mask.float()

        # Compute the integral using the trapezoidal rule for each range
        integrals = torch.trapz(masked_heights, expanded_x, dim=1)

        return integrals

    def save(self, filename):
        """Save the current configuration and soil heights to a file.

        Args:
            filename (str): Path to the file where data will be saved.
        """
        data = {
            "cfg": self.cfg,
            "cfg_name": self.cfg_name,
            "upper_limit": self.upper_limit,
            "scale": self.scale,
            "theta": self.theta,
            "x": self.x,
            "x_expanded": self.x_expanded,
            "z": self.z,
            "cov_mat": self.cov_mat,
            "norm_transform": self.norm_transform,
            "log_time": self.log_time,
        }
        with open(filename, "wb") as f:
            pickle.dump(data, f)

    def load(self, filename):
        """Load a configuration and soil heights from a file.

        Args:
            filename (str): Path to the file from which data will be loaded.
        """
        with open(filename, "rb") as f:
            data = pickle.load(f)

        self.cfg = data["cfg"]
        self.cfg_name = data["cfg_name"]
        self.upper_limit = data["upper_limit"]
        self.scale = data["scale"]
        self.theta = data["theta"]
        self.x = data["x"]
        self.x_expanded = data["x_expanded"]
        self.z = data["z"]
        self.cov_mat = data["cov_mat"]
        self.norm_transform = data["norm_transform"]
        self.log_time = data["log_time"]

    def plot_soil_height(self, env_idxs, show=True):
        """Plot the soil height for selected environment indices.

        Args:
            env_idxs (list): List of environment indices to plot.
        """
        for idx in env_idxs:
            if idx >= self.n_envs:
                print(f"Skipping index {idx} as it's out of bounds.")
                continue

            plt.plot(self.x.cpu().numpy(), self.z[idx].cpu().numpy(), label=f"Env {idx}")

        plt.xlabel("X Position")
        plt.ylabel("Soil Height")
        plt.title("Soil Height Distribution")
        plt.legend()
        plt.grid()
        if show:
            plt.show()

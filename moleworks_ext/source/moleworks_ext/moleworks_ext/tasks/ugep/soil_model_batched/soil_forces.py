# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .soil import Soil

import numpy as np
import torch


class SoilForces:
    def __init__(self, soil_model: "Soil", cfg):
        self.SM = soil_model
        self.SP = soil_model.soil_parameters
        self.SSP = soil_model.ssp
        self.bucket = soil_model.bucket_state
        self.n_envs = self.SM.env.num_envs
        self.device = self.SM.device
        self.cfg = cfg.soil_forces

        """
        INFO
        - fee only valid if SSP.beta in [0,pi]
            - SSP needs to exit soil!! otherwise fee forces are wrong/nonsense
        """

        # 3d tensors for easier handling in env
        self.zero_tensor = torch.tensor([0, 0, -1], device=self.device)
        self.Rs_unit_vector_w = torch.zeros(self.n_envs, 3, device=self.device)
        self.RF_w = torch.zeros(self.n_envs, 3, device=self.device)
        self.RM_w = torch.zeros(self.n_envs, 3, device=self.device)
        self.ones_vec = torch.ones(self.n_envs, 1, device=self.device)
        self.zeros_vec = torch.zeros(self.n_envs, 1, device=self.device)
        # init all vars
        self.update()

    def update(self):
        # invalid_ssp_beta = torch.logical_or(self.SSP.beta <= 0.0, self.SSP.beta > np.pi)
        # try:  # only invalid if in soil
        #     if torch.logical_and(self.bucket.depth > 0.0, invalid_ssp_beta).any():
        #         raise ValueError(
        #             "SSP Beta NOT in [0,pi] at env numbers: ",
        #             torch.where(invalid_ssp_beta.squeeze() == True),
        #         )
        # except Exception as error:
        #     print("\033[91m" + repr(error) + "\033[0m")
        # with gpu_timer(self.SM.env.cfg, "forces_fee"):
        self._update_fee()
        # with gpu_timer(self.SM.env.cfg, "forces_pen"):
        self._update_penetration()
        # with gpu_timer(self.SM.env.cfg, "forces_deadload"):
        self._update_deadload()
        # with gpu_timer(self.SM.env.cfg, "forces_resultant"):
        self._compute_resultant_COM()

    def _update_fee(self):
        """
        Compute fee forces. The computation is based on chapter 5 and 6 of PhD thesis
        : "Development of a Virtual Reality Excavator Simulator". See Docs for the chapters.
        :return:
        """
        # "unit test", compared to np implementation
        # self.SSP.beta[:] = np.pi / 2
        # self.SSP.alpha[:] = np.pi / 6
        # self.bucket.alpha_max = torch.tensor([0.96], device=self.device)
        # self.SP.alpha[:] = 0.3
        # d = 0.3
        # self.SSP.L[:] = d / torch.sin(self.SSP.beta)
        # self.SP.K0[:] = 0.5

        # soil failure angle
        # simplified exrpession from page 147 of thesis
        self.rho = (np.pi / 4.0 - (self.SP.phi + self.SP.delta) / 2.0) + (np.pi / 4.0 - self.SSP.beta / 2.0)
        # soil wedge surface in contact with shovel
        self.ssp_alpha_bar = self.bucket.alpha_max - self.SSP.alpha
        # beta is the angle between the ssp and the soil surface
        self.k = np.pi - self.SSP.beta - self.ssp_alpha_bar

        # Check for division by zero in sin(self.k), which is in the denominator
        if torch.any(torch.sin(self.k) == 0):
            raise ValueError("_update_fee: Division by zero in computing AX (sin(k) == 0)")

        self.AX = torch.sin(self.ssp_alpha_bar) / torch.sin(self.k) * self.SSP.L
        # see figure 5.10 Ch. 5
        self.ABX = 0.5 * torch.sin(self.SSP.beta) * self.AX * self.SSP.L

        # alternative self.A2 computation (gives the same)
        # i = np.pi - k
        # j = np.pi - i - self.rho
        # BC = torch.sin(self.SSP.beta) / torch.sin(self.rho) * self.SSP.L
        # self.A2_2 = 0.5 * self.SSP.L * BC * torch.sin(self.ssp_alpha_bar + j)
        self.A2 = (
            0.5
            * self.SSP.L
            * self.SSP.L
            * torch.sin(self.SSP.beta)
            * (torch.cos(self.SSP.beta) + torch.sin(self.SSP.beta) / torch.tan(self.rho))
        )
        # check_for_nan(self.A2, "_update_fee: After computing A2")

        self.BCX = self.A2 - self.ABX

        # Checking divisions by A2 in the following computations for c_star and phi_star
        # if torch.any(self.A2 <= 1e-5):
        #     raise ValueError("_update_fee: Division by A2 close to zero or zero in c_star/phi_star calculation")

        # set self.c_start & self.phi_star to 1 if not in soil to avoid nan, R is 0 if L is not in soil (L=0)
        # Eq 5.10 & 5.11, since the failure wedge soil is contanct with both soil and bucket we need a weighted average
        # of the soil cohesion and friction angle
        self.c_star = torch.where(
            self.A2 > 1e-5,
            (self.SP.ca * self.ABX + self.SP.c * self.BCX) / self.A2,
            self.ones_vec,
        )
        self.phi_star = torch.where(
            self.A2 > 1e-5,
            (self.SP.delta * self.ABX + self.SP.phi * self.BCX) / self.A2,
            self.ones_vec,
        )

        # self.z1 = (torchf.cos(self.SSP.beta) + torch.sin(self.SSP.beta) / torch.tan(self.rho)) * torch.sin(self.SP.alpha)
        # self.z2 = torch.cos(self.SSP.beta - self.SP.alpha) + torch.sin(self.SSP.beta - self.SP.alpha) / torch.tan(
        #     self.SP.alpha + self.rho
        # )

        # self.z3_1 = torch.sin(self.SP.alpha) * (
        #     torch.cos(self.SSP.beta) + torch.sin(self.SSP.beta) / torch.tan(self.rho)
        # )
        # self.z3_2 = torch.sin(self.SSP.beta - self.SP.alpha)

        # self.z3 = self.z3_1 * self.z3_1 - self.z3_2 * self.z3_2
        # self.z4 = torch.sin(self.SSP.beta) * (torch.cos(self.SSP.beta) + torch.sin(self.SSP.beta) / torch.tan(self.rho))
        # # if ssp.beta is 0, z4 is 0 and z is nan -> if in soil -> invalid soil model -> if not in soil -> ok, fee & pen == 0
        # self.z = self.SSP.L * (self.z1 - 1.0 / 3.0 * self.z2 * self.z3 / self.z4)

        # # degenerated case if beta - alpha = 0
        # self.z = torch.where(
        #     (self.SSP.beta - self.SP.alpha) == 0.0,
        #     -1.0 / 3.0 * self.SSP.L * torch.sin(self.SSP.beta + self.rho),
        #     self.z,
        # )

        self.L2 = torch.sin(self.rho + self.SSP.beta) / torch.sin(self.rho) * self.SSP.L
        # check_for_nan(self.L2, "_update_fee: After computing L2")
        self.ha = torch.sin(self.SP.alpha) * self.L2

        self.L3 = torch.sin(self.SSP.beta) / torch.sin(self.rho) * self.SSP.L
        # check_for_nan(self.L3, "_update_fee: After computing L3")
        self.hc = torch.sin(self.SP.alpha + self.rho) * self.L3
        self.z = (self.ha + self.hc) / 3.0
        # bottom plate adhesion force
        self.ADF = self.SP.ca * self.bucket.b * self.SSP.L
        # soil wedge weight
        self.W = self.SP.gamma * self.bucket.b * self.A2
        # cohesion between the failed soil surface and the soil
        self.CF1 = self.SP.c * self.bucket.b * self.SSP.L * torch.sin(self.SSP.beta) / torch.sin(self.rho)
        # cohesion/adhesion on the side plane of the failure wedge
        self.ACF = self.c_star * self.A2
        # friction force on the side plane of the failure wedge
        self.SF2 = self.SP.K0 * self.SP.gamma * self.z * torch.tan(self.phi_star) * self.A2

        self.Rs1 = -self.ADF * torch.cos(self.SSP.beta + self.rho + self.SP.phi)
        self.Rs2 = self.W * torch.sin(self.SP.alpha + self.rho + self.SP.phi)
        self.Rs3 = self.CF1 * torch.cos(self.SP.phi)
        self.Rs4 = 2.0 * self.ACF * torch.cos(self.SP.phi)
        self.Rs5 = 2.0 * self.SF2 * torch.cos(self.SP.phi)
        self.Rs6 = torch.sin(self.SSP.beta + self.rho + self.SP.delta + self.SP.phi)
        # eq 5.3 ch 5
        self.Rs = (self.Rs1 + self.Rs2 + self.Rs3 + self.Rs4 + self.Rs5) / self.Rs6
        self.Rs[self.Rs6 < 1e-3] = 0.0
        # check_for_nan(self.Rs, "_update_fee: after computing Rs")
        # set Rs to 0 if not in soil -> should get rid of nan/inf
        self.Rs[self.bucket.depth <= 0.0] = 0.0
        self.Rs *= self.cfg.fee_multiplyer

        # direction of force to be applied to the shovel in the world frame
        fee_force_angle_w = -(np.pi / 2.0 + self.SP.alpha - self.SSP.beta - self.SP.delta)
        # this is the same
        # fee_force_angle_w = -(np.pi / 2.0 - self.SSP.alpha - self.bucket.bp_angle_to_horizon - self.SP.delta)

        self.Rs_unit_vector_w[:, 0] = torch.cos(fee_force_angle_w).view(-1)
        self.Rs_unit_vector_w[:, 2] = torch.sin(fee_force_angle_w).view(-1)

    def _update_penetration(self):
        """Update the penetration force for the bottom and side plates, we don't consider bucket teeth.
        Based on Integration of digging forces in a multi-body-system model of an excavator.
        """
        # plate
        self.p0_2 = (
            0.5
            * self.SP.gamma
            * self.bucket.clipped_average_depth_bp
            * ((1.0 + self.SP.K0) + (1.0 - self.SP.K0) * torch.cos(2.0 * self.bucket.bp_angle_to_horizon))
        )
        self.plate_Rs = (self.SP.ca + self.p0_2 * torch.tan(self.SP.delta)) * self.bucket.bp_soil * self.bucket.b
        # no need to set to 0 if not in soil, pressure is 0, hence force is 0
        self.plate_Rs *= self.cfg.penetration_plate_multiplyer

        # tip
        # using the clipped depth, because we want 0 force if not in soil
        self.p0 = (
            0.5
            * self.SP.gamma
            * self.bucket.clipped_depth
            * ((1.0 + self.SP.K0) + (1.0 - self.SP.K0) * torch.cos(2.0 * self.bucket.bp_angle_to_horizon))
        )
        self.pe = self.p0 * self.SP.CP
        self.edge_Rs = (
            (self.pe + (self.SP.ca + self.pe * torch.tan(self.SP.delta)) / torch.tan(self.bucket.half_angle))
            * self.bucket.b
            * self.bucket.top_width
        )
        # check_for_nan(self.edge_Rs, "_update_penetration: after computing edge_Rs")

        # 0 if moving perpendicular, 1 if moving parallel
        self.edge_Rs *= self.bucket.vel_cos
        # if moving backwards, no penetration
        self.edge_Rs[self.bucket.vel_cos < 0.0] = 0.0
        # set to 0 if not in soil (edge_Rs is not 0 if pressure is 0 (=not in soil))
        self.edge_Rs[self.bucket.depth <= 0.0] = 0.0
        self.edge_Rs *= self.cfg.penetration_edge_multiplyer

    def _update_deadload(self):
        """Update the deadload force, which is the weight of the soil in the bucket."""
        # SSP.B is the area of the soil wedge
        self.deadload_F = self.SP.gamma * self.SSP.B * self.bucket.b
        self.deadload_F *= self.cfg.deadload_multiplyer

    def _compute_resultant_COM(self):
        """
        Compute resultant force and moment at the COM of the bucket.
        """
        # Preallocate tensor on the same device to avoid implicit synchronization

        # Compute Rs_vec, penF_vec, deadloadF_vec, and RF_w
        Rs_vec = self.Rs * self.Rs_unit_vector_w
        penF_vec = (self.edge_Rs + self.plate_Rs) * self.SM.bp_unit_vector_w_3d
        deadloadF_vec = self.deadload_F * self.zero_tensor
        # print("deadloadF value is: ", deadloadF_vec)
        self.RF_w = Rs_vec + penF_vec + deadloadF_vec
        self.RF_w = self.RF_w

        # moment (pos_F_w - pos_COM_w) x F -> only around y axis, since soil model is in x,z
        Rs_pos_w = self.SM.bucket_pos_w + self.SSP.ssp_unit_vector_w * self.SSP.L / 2.0
        penF_pos_w = self.SM.bucket_pos_w
        deadloadF_pos_w = self.SSP.centroid_pos_w

        Rs_diff_pos = Rs_pos_w - self.bucket.COM_pos_w
        penF_diff_pos = penF_pos_w - self.bucket.COM_pos_w
        deadload_diff_pos = deadloadF_pos_w - self.bucket.COM_pos_w
        M_Rs_y = (Rs_diff_pos[:, 1] * Rs_vec[:, 0] - Rs_diff_pos[:, 0] * Rs_vec[:, 1]).unsqueeze(-1)
        M_pen_y = (penF_diff_pos[:, 1] * penF_vec[:, 0] - penF_diff_pos[:, 0] * penF_vec[:, 1]).unsqueeze(-1)
        M_deadload_y = (
            deadload_diff_pos[:, 1] * deadloadF_vec[:, 0] - deadload_diff_pos[:, 0] * deadloadF_vec[:, 1]
        ).unsqueeze(-1)
        self.RM_w[:, 1] = (M_Rs_y + M_pen_y + M_deadload_y).view(-1)
        self.RM_w = self.RM_w
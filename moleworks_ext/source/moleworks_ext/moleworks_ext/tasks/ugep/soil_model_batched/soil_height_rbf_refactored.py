# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    pass

import numpy as np
import torch

from m545_isaac.utils.helpers import gpu_timer

# RBF implenentation based on https://towardsdatascience.com/implement-a-gaussian-process-from-scratch-2a074a470bce


class SoilHeightRBF:
    def __init__(self, cfg, cfg_name, upper_limit=None, log_time=False):
        """init SoilHeightRBF class

        Args:
            x: num_points; min - max distance cabin - bucket (in world frame)
            n_envs:
            theta: rbf length scale
            scale: magnitude of rbf
        """
        self.cfg = cfg
        self.cfg_name = cfg_name
        self.device = self.cfg.gym.sim_device
        self.n_envs = cfg.env.num_envs
        self.class_cfg = vars(self.cfg)[self.cfg_name]
        self.upper_limit = upper_limit
        self.scale = self.class_cfg.scale
        self.theta = self.class_cfg.theta

        steps = int(np.ceil((self.class_cfg.x_max - self.class_cfg.x_min) / self.class_cfg.min_resolution) + 1)
        # size (steps, )
        self.x = torch.linspace(self.class_cfg.x_min, self.class_cfg.x_max, steps, device=self.device)
        # size (n_envs, steps)
        self.x_expanded = self.x.expand(self.n_envs, -1)
        self.x_dim = self.x.shape[0]
        self.x_dist = torch.diff(self.x)[0]
        # size (n_envs, 1)
        self.x_dist_expanded = self.x_dist.expand(self.n_envs).view(self.n_envs, -1)
        # (1, )
        self.max_upper_idx = (self.x_dim - 1) * torch.ones(1, device=self.device, dtype=torch.int64)
        self.min_lower_idx = torch.zeros(1, device=self.device, dtype=torch.int64)

        self.z = torch.zeros(self.n_envs, self.x_dim, device=self.device, dtype=torch.float)
        # correlation matrix is constant if num points and theta are the fixed
        self.cov_mat = torch.diag(self.x)  # values not used, just to initialize matrix
        self.compute_norm_transform(self.theta)
        self.sample()

        self.log_time = log_time
        if self.log_time:
            self.time_log = {}

    def compute_norm_transform(self, theta):
        """Compute new norm_transform defines correleation between points.
        See docs for derivation.

        Args:
            theta (float): 0: flat soil,  > 0 more random
        """
        theta = max(min(theta, 1.0), 0.0)

        for i in range(self.x_dim):
            self.cov_mat[i, :] = torch.exp(-(theta * (self.x[i] - self.x) ** 2))

        # self.scale_tril = torch.linalg.cholesky(
        #     self.cov_mat + torch.diag(torch.ones(self.x_dim) * 1e-5)
        # )
        # self.distribution = torch.distributions.MultivariateNormal(
        #     self.mu, scale_tril=self.scale_tril, validate_args=False
        # )

        # sampling multivariate normal
        # https://stackoverflow.com/questions/6142576/sample-from-multivariate-normal-gaussian-distribution-in-c

        eig_vals, eig_vecs = torch.linalg.eigh(self.cov_mat)
        eig_vals[eig_vals < 0.0] = 0.0  # fix numerical issues
        self.norm_transform = eig_vecs @ torch.diag(torch.sqrt(eig_vals))

    def sample(self, idxs=...):
        """Resamples new soil for given indices.
        Height = mu + scale * A @ rand_vec
        where A is the norm_transform matrix.

        - scales rbf
        - applies offset
            - if value: average is that value,
            - if none and upper limiif: sampled between min and upper_limit.offset
            - if none and no upper limits: sampled between min and max
            -
        - clips by z_min, z_max
        - clips by array if not None (for max depth)

        Args:
            idxs List[int]: Env idx to resample soil
            upper_limit: if not None, array that gives upper limit (with margin from cfg)


        """
        # last dim needed for broadcasting
        if idxs == ...:
            dim0 = self.n_envs
        else:
            dim0 = len(idxs)

        # which type of shape ["rbf", "slope"]
        if self.class_cfg.type == "rbf":
            rand_vec = torch.randn(dim0, self.x_dim, 1, dtype=self.x.dtype, device=self.device)
            self.z[idxs] = self.scale * (self.norm_transform @ rand_vec).squeeze()  # 0-mean

        # for testing purpose
        elif self.class_cfg.type == "slope":
            print(f"[{self.cfg_name}]: Type Slope!")
            self._compute_slope()  # fills self.zc

        if self.class_cfg.offset is None:
            if self.upper_limit != None:
                u = self.upper_limit.offset
            else:
                u = self.class_cfg.z_max
            l = self.class_cfg.z_min
            self.offset = (u - l) * torch.rand(dim0, 1, device=self.device) + l
        elif isinstance(self.class_cfg.offset, dict):
            self.offset = (
                -self.z[idxs, self.class_cfg.offset["height_idx"]] + self.class_cfg.offset["height"]
            ).unsqueeze(-1)

        else:
            self.offset = self.class_cfg.offset

        self.z[idxs] += self.offset

        # clip by min max in cfg
        self.z[idxs] = torch.clip(self.z[idxs], min=self.class_cfg.z_min, max=self.class_cfg.z_max)

        # clip by other array (max depth should always be under soil height)
        if self.upper_limit != None:
            self.z[idxs] = torch.clip(
                self.z[idxs],
                max=self.upper_limit.z[idxs] - self.class_cfg.clip_margin,
            )

    def get_height(self, val_in, env_ids=...):
        """interpolates soil height for given points x

        Args:
            val: (n_envs,n_points) value for which height is requested

        Returns:
            heights: (n_envs,n_points) soil heights at x


        - we assert that we have constant spacing (and this avoids also division by 0!)
        - if val outside of x, returns heights at boundaries

        """
        if env_ids == ...:
            dim0 = self.n_envs
        else:
            dim0 = len(env_ids)

        # with gpu_timer(self.cfg, "soil_get_height_clip"):
        # ensure that the value is within the range
        val = torch.clip(val_in, self.x_expanded[env_ids, 0:1], self.x_expanded[env_ids, -1:])

        # with gpu_timer(self.cfg, "find_upper_lower"):
        upper, lower = self._find_upper_and_lower_idxs(val)

        # with gpu_timer(self.cfg, "soil_get_height_lerp"):
        nom = val - torch.gather(self.x_expanded[:dim0], 1, lower)
        weights = nom / self.x_dist
        # return torch.lerp(torch.gather(self.z[env_ids], 1, lower), torch.gather(self.z[env_ids], 1, upper), weights)
        res = torch.lerp(
            torch.gather(self.z[env_ids], 1, lower),
            torch.gather(self.z[env_ids], 1, upper),
            weights,
        )
        return res

    def get_angle_to_world(self, val_in, env_ids=...):
        """gets angle of soil relative to world for point val
                - if exactly at knot (upper==lower) return average slope of the two neighboring segments
                - if outside or at start or and of self.x, return 0 angle (we return constant height in this case)

        Args:
            x: (n_envs,) value for which slope is requested

        Returns:
            slopes: (n_envs,) soil slopes at val in radians

        """
        if env_ids == ...:
            dim0 = self.n_envs
        else:
            dim0 = len(env_ids)

        val = torch.clip(val_in, self.x_expanded[env_ids, 0:1], self.x_expanded[env_ids, -1:])

        upper, lower = self._find_upper_and_lower_idxs(val)
        # get neighbors if exactly at knot and not at upper or lower end
        upper = self._clip_upper_indices(
            torch.where(
                torch.logical_and(
                    torch.logical_and(upper != self.x_dim - 1, lower != 0),
                    upper == lower,
                ),
                upper + 1,
                upper,
            )
        )
        lower = self._clip_lower_indices(
            torch.where(
                torch.logical_and(
                    torch.logical_and(upper != self.x_dim - 1, lower != 0),
                    upper == lower,
                ),
                lower - 1,
                lower,
            )
        )

        # if exactly at knot, 2x dist (if at bounds, dy will be 0 and we do NOT get division by 0)
        dx = torch.where(
            upper == lower,
            2.0 * self.x_dist_expanded[:dim0],
            self.x_dist_expanded[:dim0],
        )

        dy = torch.gather(self.z[env_ids], 1, upper) - torch.gather(self.z[env_ids], 1, lower)
        return torch.atan2(dy, dx)

    def integrate(self, x0, x1):
        """gives the area under the soil
        linear interpolation between the two points (trapezoid with 1 step)
        (we also do not have the shovel position at higher resolution)

        what to do with negative soil -> shovel depth (integration) will be more negative -> positive volume

        Args:
            x0, x1: (n_envs,1) integration interval

        Returns:
            area under the soil (n_envs, 1)
        """

        y0 = self.get_height(x0)
        y1 = self.get_height(x1)

        return 0.5 * (x1 - x0) * (y1 + y0)

        pass

    def make_trench(self, midpoint, depth, bottom_length, slope_tan):
        bottom_ids = bottom_length // self.x_dist
        midpoint_idx = midpoint // self.x_dist
        min_idx = torch.zeros(1, device=self.device)
        max_idx = torch.tensor(self.z.shape[-1] - 1, device=self.device)
        bottom_start_idx = int(torch.maximum(midpoint_idx - bottom_ids // 2, min_idx))
        bottom_end_idx = int(torch.minimum(torch.ceil(midpoint_idx + bottom_ids / 2), max_idx))
        slope_start_idx_left = int(torch.maximum(bottom_start_idx - (depth / slope_tan) // self.x_dist, min_idx))
        slope_end_idx_right = int(
            torch.minimum(torch.ceil(bottom_end_idx + (depth / slope_tan) / self.x_dist), max_idx)
        )

        self.z[:] = 0.0
        slope_ids = torch.arange(
            bottom_start_idx - slope_start_idx_left,
            dtype=torch.float32,
            device=self.device,
        )
        self.z[:, slope_start_idx_left:bottom_start_idx] = 0.0 - slope_tan * slope_ids * self.x_dist
        self.z[:, bottom_start_idx:bottom_end_idx] = -depth
        slope_ids = torch.arange(
            slope_end_idx_right - bottom_end_idx,
            dtype=torch.float32,
            device=self.device,
        )
        self.z[:, bottom_end_idx:slope_end_idx_right] = -depth + slope_tan * slope_ids * self.x_dist

    def _find_upper_and_lower_idxs(self, val):
        """finds the upper and lower idxs of val in self.x
                - val can have arbitrary dimensionality
                - if val exactly at knot, upper = lower
                - if at upper end: upper = lower = self.x_dim-1
                - if at lower end: upper = lower = 0

                - searchsorted gives one past array index if val larger than largest in array
                - gives 0 if smaller than first value
        Args:
            val: (n_envs,n_points) value for which slope is requested

        Returns:
            tuple (upper, lower) each (n_envs, n_points)

        """
        dim = val.shape[0]
        # with gpu_timer(self.cfg, "find_upper"):
        # searchsorted returns the idx of x, where
        upper = self._clip_upper_indices(torch.searchsorted(self.x, val))

        # with gpu_timer(self.cfg, "find_lower"):
        lower = self._clip_lower_indices(
            torch.where(
                torch.gather(self.x_expanded[:dim], 1, upper) == val,
                upper,
                upper - 1,
            )
        )
        return upper, lower

    def _clip_upper_indices(self, upper):
        upper_val = torch.minimum(upper, self.max_upper_idx)
        return upper_val

    def _clip_lower_indices(self, lower):
        lower_val = torch.maximum(lower, self.min_lower_idx)
        return lower_val

    def _compute_slope(self):
        slope_start_idx = int((self.class_cfg.slope_start - self.class_cfg.x_min) / self.x_dist)
        slope_idx_len = int(self.class_cfg.slope_x_len / self.x_dist)
        slope_end_idx = slope_start_idx + slope_idx_len

        slope_ang = torch.tensor(self.class_cfg.slope_ang, device=self.device)
        self.z[:, 0:slope_start_idx] = 0.0
        self.z[:, slope_start_idx:slope_end_idx] = torch.tan(slope_ang) * torch.linspace(
            0, (slope_idx_len - 1) * self.x_dist, slope_idx_len, device=self.device
        )
        self.z[:, slope_end_idx:] = torch.tan(slope_ang) * slope_idx_len * self.x_dist

        self.z += self.class_cfg.slope_start_height

# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .soil import Soil

import numpy as np
import torch


class SSP:
    def __init__(self, soil_model: "Soil", cfg):
        self.SM = soil_model
        self.n_envs = self.SM.env.num_envs
        self.device = self.SM.device
        self.cfg = cfg.ssp
        self.bucket = soil_model.bucket_state
        """
        constants
        """
        self.alpha_poly_coeffs = torch.tensor(self.cfg.ssp_angle_poly_coeffs, device=self.device)
        self.dL_max = torch.tensor(self.cfg.dL_max, device=self.device)
        self.L_over_max = torch.tensor(self.cfg.L_over_max, device=self.device)
        self.L_over_max_discretized = torch.arange(0, self.L_over_max + 1e-5, self.dL_max, device=self.device)
        self.L_over_max_discretized_expanded = self.L_over_max_discretized.expand(self.n_envs, -1)
        self.L_min = torch.zeros(self.n_envs, 1, device=self.device)
        self.zero_vec = torch.zeros(self.n_envs, 1, device=self.device)

        """
        ssp geometry
        """
        # ssp angle to bottom plate
        self.alpha = torch.zeros(self.n_envs, 1, device=self.device)
        self.update()

        # double beta = 0.0; // angle to soil surface
        # double LMax = 0.0; // to the back of the bucket
        # double L = 0.0; // in contact with soil
        # double LMaxNoBucket = 0.0; // max length for valid c_star / phi_start computation
        # double deadloadX = 0.0; // centroid from shovel edge
        # double deadloadZ = 0.0; // centroid from shovel edge
        # double deadloadForce = 0.0; // force in gravitational direction on centroid of deadload
        # };
        # self.update()

    def update(self):
        # with gpu_timer(self.SM.env.cfg, "soil_ssp_angles"):
        self._update_ssp_angles(self.bucket.fill_area)

        # with gpu_timer(self.SM.env.cfg, "soil_ssp_helper_vars"):
        self._update_helper_vars()

        # with gpu_timer(self.SM.env.cfg, "soil_ssp_max_L"):
        self._update_max_L()

        # with gpu_timer(self.SM.env.cfg, "soil_ssp_L_ang"):
        self._update_L_and_angle_to_soil()

        # with gpu_timer(self.SM.env.cfg, "soil_ssp_centroid"):
        self._update_centroid()

    def _update_centroid(self):
        """
        Compute the centroid of the soil in the bucket.
        The buckets is decomposed into 3 different shapes.
        It's computed first in the bucket bottom plate (frame in the bucket edge pointing x along the plate).
        :return:
        """
        y = torch.where(
            self.h > 1e-5,
            2.0 * self.bucket.r * torch.sin(self.h / 2.0) / (1.5 * self.h),
            self.zero_vec,
        )
        # shape 1 is a circular sector
        x1 = self.bucket.a + torch.sin(self.h / 2.0) * y
        z1 = self.bucket.r - torch.cos(self.h / 2.0) * y
        x2 = 2.0 * self.bucket.a / 3.0
        z2 = self.x / 3.0
        hc = torch.sin(self.h) * self.bucket.r
        xi = torch.cos(self.h) * self.bucket.r
        x3 = self.bucket.a + hc / 3.0
        z3 = self.bucket.r - (xi + self.b) / 3.0
        # the centroid is computed by a weighted average of the centroids of the 3 shapes
        centroid_bucket_x = torch.where(
            self.B > 1.0e-4,
            (x1 * self.B2 - x3 * self.B1 + x2 * self.A1) / self.B,
            self.zero_vec,
        )
        centroid_bucket_z = torch.where(
            self.B > 1.0e-4,
            (z1 * self.B2 - z3 * self.B1 + z2 * self.A1) / self.B,
            self.zero_vec,
        )

        # rotate from bucket into world frame
        bucket_cos = torch.cos(self.bucket.bp_angle_to_horizon)
        bucket_sin = torch.sin(self.bucket.bp_angle_to_horizon)
        self.centroid_w_x = (
            bucket_cos * centroid_bucket_x - bucket_sin * centroid_bucket_z + self.SM.bucket_pos_w[:, 0].unsqueeze(-1)
        )
        self.centroid_w_z = (
            bucket_sin * centroid_bucket_x + bucket_cos * centroid_bucket_z + self.SM.bucket_pos_w[:, 1].unsqueeze(-1)
        )
        self.centroid_pos_w = torch.cat((self.centroid_w_x, self.centroid_w_z), dim=-1)

    def _update_L_and_angle_to_soil(self):
        # this only works for horizontal soil!!!
        # sspGeometry_.beta = bucketGeometry_.bottomPlateAngleToHorizon + sspGeometry_.alpha;
        # 1) discretize L ->  search a long a line longer than max ssp -> otherwise inefficient, self-coded 2d python arange
        # 2) find intersection with soil
        # 3) find slope of soil at the intersection

        # n_envs, num_da, 2
        # broadcasting magic, last dimensions must match or one of them has to be 1
        dL_pos_w = self.SM.bucket_pos_w.view(self.n_envs, 1, 2) + self.L_over_max_discretized.view(
            -1, 1
        ) * self.ssp_unit_vector_w.view(self.n_envs, 1, 2)
        dL_heights_w = dL_pos_w[..., 1]  # n_envs, num_dL
        dL_x_w = dL_pos_w[..., 0]
        dL_soil_heights_w = self.SM.soil_height_rbf.get_height(dL_x_w)

        # find which dL_height is larger than soil height
        # if idx is 0: already tip is not in soil
        # corner case: tip exactly at soil height: considered outside soil
        max_val, max_idxs = torch.max(dL_heights_w >= dL_soil_heights_w, dim=1, keepdim=True)
        # corner cases
        # bottom plate completely in soil..? old model-> just clipped -> ok
        #   max_val = false (always smaller than soil) returned index is 0 (just first max value)
        # bottom plate outside
        #   max_val = true  and index = 0
        max_idxs = torch.where(max_val == False, self.L_over_max_discretized.shape[0] - 1, max_idxs)  # -1 does not work
        self.L = torch.gather(self.L_over_max_discretized_expanded, 1, max_idxs)

        # clip to actually feasible max L
        self.L = torch.clip(self.L, self.L_min, self.L_max)
        x_exit = torch.gather(dL_x_w, 1, max_idxs)
        # angle of soil to world where ssp exits soil
        self.soil_alpha = self.SM.soil_height_rbf.get_angle_to_world(x_exit)
        self.beta = self.ssp_angle_to_horizon - self.soil_alpha

    def _update_max_L(self):
        # L max, if shovel was only triangle
        self.L_max_no_bucket = self.bucket.a / torch.cos(self.alpha)
        # check_for_nan(self.L_max_no_bucket, "_update_max_L: after L_max_no_bucket")
        # L max, with cylindrical part
        self.L_max = self.L_max_no_bucket + self.bucket.r * torch.sin(self.h) / torch.sin(self.m)
        # check_for_nan(self.L_max, "_update_max_L: after L_max")

    def _update_helper_vars(self):
        """
        update helper variables for centroid calculation. See doc ssp_geometry for details.
        :return:
        """
        self.gamma = np.pi / 2.0 - self.alpha
        self.beta2 = np.pi - self.gamma
        self.x = self.bucket.a * torch.tan(self.alpha)
        self.b = self.bucket.r - self.x
        self.delta = torch.asin(torch.clamp(self.b / self.bucket.r * torch.sin(self.beta2), -1, 1))
        # check_for_nan(self.delta, "_udpate_helpers_vars: after delta")
        self.n = np.pi - self.gamma
        self.m = np.pi - self.n
        self.h = np.pi - self.m - self.delta
        # from here on for deadload centroid, there are all areas
        self.A1 = 0.5 * self.x * self.bucket.a
        self.B1 = 0.5 * self.b * self.bucket.r * torch.sin(self.h)
        self.B2 = 0.5 * self.bucket.r * self.bucket.r * self.h
        self.B = self.A1 + self.B2 - self.B1

    def _update_ssp_angles(self, bucket_fill_area):
        # update alpha:= ssp_angle_to_bp
        # horner's rule, highets degree poly coeff first
        self.alpha[:] = 0
        for i in range(len(self.alpha_poly_coeffs) - 1):
            self.alpha = bucket_fill_area * (self.alpha + self.alpha_poly_coeffs[i])
        self.alpha += self.alpha_poly_coeffs[i + 1]

        # update ssp angle to world and get unit vector (for soil intersection computation)
        self.ssp_angle_to_horizon = self.bucket.bp_angle_to_horizon + self.alpha
        self.ssp_unit_vector_w = torch.cat(
            (
                torch.cos(self.ssp_angle_to_horizon),
                torch.sin(self.ssp_angle_to_horizon),
            ),
            dim=-1,
        )

# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from m545_isaac.envs.excavation.env import ExcavationEnv

# debugging
import matplotlib.pyplot as plt
import numpy as np
import torch

from m545_isaac.utils.helpers import gpu_timer
from soil_model_batched.bucket_state_2d import BucketState2D
from soil_model_batched.soil_forces import SoilForces
from soil_model_batched.soil_height_2d import SoilHeightRBF2D
from soil_model_batched.soil_parameters import SoilParameters
from soil_model_batched.ssp import SSP


class Soil2D:
    """
    environment (only interacts with soil model class:sample soil params, set bucket fill volume, reset, fixed soil params)
            |
            Soil model (contains alls model elements)
            ----------
    soil_height | bucket_state | ssp | soil_forces | soil_parameters


    """

    def __init__(self, env: ExcavationEnv, cfg):
        self.env = env
        self.n_envs = env.num_envs
        self.device = env.device

        # measurement buffers, updated with data from env
        # measurements in the arm plane (x-z), in a frame that is gravity aligned
        # attached to boom joint
        self.bucket_pos_w = torch.zeros(self.n_envs, 2, device=self.device)
        self.prev_bucket_pos_w = torch.zeros(self.n_envs, 2, device=self.device)
        # vector from tip towards bucket
        self.bp_unit_vector_w_3d = torch.zeros(self.n_envs, 3, device=self.device)
        self.bp_unit_vector_w = torch.zeros(self.n_envs, 2, device=self.device)
        self.bucket_vel_w = torch.zeros(self.n_envs, 2, device=self.device)

        # soil model elements
        self.soil_height_rbf = SoilHeightRBF2D(cfg, cfg_name="soil_height")
        self.max_depth_height_rbf = SoilHeightRBF2D(cfg, cfg_name="max_depth_height", upper_limit=self.soil_height_rbf)
        self.bucket_state = BucketState2D(self, cfg)
        self.ssp = SSP(self, cfg)
        self.soil_parameters = SoilParameters(self, cfg)
        self.forces = SoilForces(self, cfg)

        self.xz_idx = torch.tensor([0, 2], device=self.device)

        self.false_vec = torch.zeros(self.n_envs, 1, dtype=torch.bool, device=self.device)

    def update(self):
        self.update_bucket_state()
        self.update_soil_model()

    def update_bucket_state(self, idxs=...):
        with gpu_timer(self.env.cfg, "soil_measurements"):
            self._update_measurements()

        with gpu_timer(self.env.cfg, "soil_bucket_state"):
            self.bucket_state.update(idxs)

    def update_soil_model(self):
        with gpu_timer(self.env.cfg, "soil_ssp"):
            self.ssp.update()

        with gpu_timer(self.env.cfg, "soil_soil_params"):
            self.soil_parameters.update()  # get updated soil angle

        with gpu_timer(self.env.cfg, "soil_forces"):
            self.forces.update()

    def reset(self, idxs=...):
        self.bucket_state.fill_area[idxs] = 0.0
        self.bucket_state.swept_area[idxs] = 0.0
        self.bucket_state.fill_ratio[idxs] = 0.0

    def sample(self, idxs=...):
        self.soil_height_rbf.sample(idxs=idxs)
        self.max_depth_height_rbf.sample(idxs=idxs)
        self.soil_parameters.sample(idxs=idxs)

    def is_state_invalid(self, idxs=...):
        """
        Check if the soil state is invalid (e.g., the bucket is not in the soil).
        The Secondary Separation Plate (ssp) is a plane where the separation forces are applied as
        more soil gets into the bucket and forms a wedge (rather than being applied to the bucket metal plate).

        Args:
            idxs (optional): Index values to be used for comparison.
                             If not provided, all elements will be considered.

        Returns:
            Tensor: A boolean tensor indicating if the current state is invalid.
        """
        # Check if the ssp length is greater than the maximum length without a bucket
        too_flat_ssp = self.ssp_L[idxs] > self.ssp_L_max_no_bucket[idxs]

        # Check if the ssp length is greater than the maximum length
        too_long_ssp = self.ssp_L[idxs] > self.ssp_L_max[idxs]

        # Check if the ssp angle to soil is invalid (<= 0 or > pi)
        # This condition can be true even if not in soil, which should be avoided
        invalid_ssp_beta = torch.logical_or(self.ssp_ang_to_soil[idxs] <= 0.0, self.ssp_ang_to_soil[idxs] > np.pi)

        # Check if the soil failure angle is invalid (<= 0)
        invalid_ssp_rho = self.soil_failure_ang[idxs] <= 0.0

        # Check if the bucket is in soil (depth > 0)
        in_soil = self.bucket_state.depth[idxs] > 0.0

        # The ssp state cannot be invalid if the bucket is not in soil
        return torch.where(
            in_soil,
            (too_flat_ssp | too_long_ssp | invalid_ssp_beta | invalid_ssp_rho),
            self.false_vec[idxs],
        )

    def set_bucket_fill_state(self, state, env_ids=None, is_ratio=True):
        """
        set soil model properties (env only interacts with soil, not its subclasses)
        """
        self.bucket_state.set_fill_state(state, env_ids, is_ratio)

    """
    Properties for all soil model subcomponents (env only interacts with soil, not its subclasses)
    """

    def get_min_max_soil_height(self, idx=...):
        min_val, _ = torch.min(self.soil_height_rbf.z[idx], dim=-1)
        max_val, _ = torch.max(self.soil_height_rbf.z[idx], dim=-1)
        return min_val, max_val

    def get_soil_height_at_pos(self, x_pos, env_ids=...):
        return self.soil_height_rbf.get_height(x_pos, env_ids)

    def get_soil_angle_at_pos(self, x_pos, env_ids=...):
        return self.soil_height_rbf.get_angle_to_world(x_pos, env_ids)

    def get_max_depth_height_at_pos(self, x_pos, env_ids=...):
        return self.max_depth_height_rbf.get_height(x_pos, env_ids)

    @property
    def ssp_ang_to_soil(self):
        return self.ssp.beta

    @property
    def ssp_L(self):
        return self.ssp.L

    @property
    def ssp_L_max_no_bucket(self):
        return self.ssp.L_max_no_bucket

    @property
    def ssp_L_max(self):
        return self.ssp.L_max

    @property
    def resultant_force(self):
        return self.forces.RF_w

    @property
    def rs(self):
        return self.forces.Rs

    @property
    def edge_rs(self):
        return self.forces.edge_Rs

    @property
    def plate_rs(self):
        return self.forces.plate_Rs

    @property
    def deadload(self):
        return self.forces.deadload_F

    @property
    def resultant_moment(self):
        return self.forces.RM_w

    @property
    def soil_failure_ang(self):
        return self.forces.rho

    @property
    def bucket_depth(self):
        # negative depth = outside soil
        return self.bucket_state.depth

    @property
    def bucket_com_pos_w(self):
        return self.bucket_state.COM_pos_w

    @property
    def fill_ratio(self):
        return self.bucket_state.fill_ratio

    @property
    def max_fill_area(self):
        return self.bucket_state.max_fill_area

    @property
    def fill_area(self):
        return self.bucket_state.fill_area

    @property
    def swept_area(self):
        return self.bucket_state.swept_area

    @property
    def bucket_vel_cos(self):
        return self.bucket_state.vel_cos

    @property
    def soil_height(self):
        return self.soil_height_rbf.x, self.soil_height_rbf.z

    @property
    def max_depth_height(self):
        return self.max_depth_height_rbf.x, self.max_depth_height_rbf.z

    @property
    def bucket_full_angle_w(self):
        return self.bucket_state.full_angle_to_horizon

    @property
    def bucket_bp_angle_w(self):
        return self.bucket_state.bp_angle_to_horizon

    # Collects necessary measurements from env
    # Only this method uses access to env!
    def _update_measurements(self):
        """Fetch measurements from env.

        This is all we need from the env. The rest is computed internally in the soil model (and subclasses).
        """
        self.bucket_pos_w[:] = self.env.m545_asset.bucket_pos_w[:, self.xz_idx]
        self.prev_bucket_pos_w[:] = self.env.m545_asset.prev_bucket_pos_w[:, self.xz_idx]
        self.bp_unit_vector_w_3d[:] = self.env.m545_asset.bp_unit_vector_w  # Needed for 3D force computation.
        self.bp_unit_vector_w[:] = self.env.m545_asset.bp_unit_vector_w[:, self.xz_idx]
        self.bucket_vel_w[:] = self.env.m545_asset.bucket_vel_w[:, self.xz_idx]

    def plot_state(self, idx, show=True, label=True, ax=None):
        Ry = lambda a: np.array([[np.cos(a), -np.sin(a)], [np.sin(a), np.cos(a)]])  # rot mat 2d
        if ax is None:
            ax = plt.gca()

        for i in idx:
            # soil height
            ax.plot(
                self.soil_height_rbf.x.cpu(),
                self.soil_height_rbf.z[i].cpu(),
                label="soil env nr.  " + str(i) if label else "",
            )
            ax.plot(
                self.soil_height_rbf.x.cpu(),
                self.soil_height_rbf.z[i].cpu() + self.env.curr_manager.curr_spilling_depth_margin.cpu(),
                label="spilling depht margin" if label else "",
                linestyle="dotted",
                linewidth=0.5,
            )
            # max_depth
            ax.plot(
                self.max_depth_height_rbf.x.cpu(),
                self.max_depth_height_rbf.z[i].cpu(),
                label="max depth" if label else "",
            )
            # BUCKET
            # bottom plate
            tip_x = self.bucket_pos_w[i, 0].item()
            tip_z = self.bucket_pos_w[i, 1].item()
            end_x = (self.bp_unit_vector_w[i, 0] * self.bucket_state.a).item() + tip_x
            end_z = (self.bp_unit_vector_w[i, 1] * self.bucket_state.a).item() + tip_z
            ax.plot(
                [tip_x, end_x],
                [tip_z, end_z],
                color="k",
                marker="x",
                markevery=[0],
                linewidth=0.5,
            )
            # bucket in soil
            end_soil_x = (self.bp_unit_vector_w[i, 0] * self.bucket_state.bp_soil[i]).item() + tip_x
            end_soil_z = (self.bp_unit_vector_w[i, 1] * self.bucket_state.bp_soil[i]).item() + tip_z
            ax.plot(
                [tip_x, end_soil_x],
                [tip_z, end_soil_z],
                color="r",
                marker="x",
                linewidth=1.5,
                label="bottom plate" if label else "",
            )
            # bucket cylinder
            # 90° to bottom plate for center of cylinder
            vec_end_bp_center = torch.flip(self.bp_unit_vector_w[i], dims=[-1])
            vec_end_bp_center[0] *= -1

            center = self.bp_unit_vector_w[i] * self.bucket_state.a + vec_end_bp_center * self.bucket_state.r

            bucket_drawing_angs = np.linspace(-np.pi / 2.0, np.pi / 2.0, 20)
            bucket_drawing_vecs = np.vstack((np.cos(bucket_drawing_angs), np.sin(bucket_drawing_angs))).transpose()
            bucket_drawing_vecs = (
                Ry(self.bucket_state.bp_angle_to_horizon[i].cpu().numpy().squeeze())
                @ bucket_drawing_vecs.reshape(-1, 2, 1)
                * self.bucket_state.r.cpu().numpy()
            )

            bucket_drawing_vecs = bucket_drawing_vecs.squeeze()
            ax.scatter(tip_x + center[0].item(), tip_z + center[1].item(), marker="o")
            bucket_drawing_x = bucket_drawing_vecs[:, 0] + center[0].item() + tip_x
            bucket_drawing_z = bucket_drawing_vecs[:, 1] + center[1].item() + tip_z
            ax.plot(bucket_drawing_x, bucket_drawing_z, color="k", linewidth=0.5)

            # SSP
            # ssp over
            end_x_over = tip_x + (self.ssp.ssp_unit_vector_w[i, 0] * self.ssp.L_over_max).item()
            end_z_over = tip_z + (self.ssp.ssp_unit_vector_w[i, 1] * self.ssp.L_over_max).item()
            # ssp max length
            end_x_max = tip_x + (self.ssp.ssp_unit_vector_w[i, 0] * self.ssp.L_max[i]).item()
            end_z_max = tip_z + (self.ssp.ssp_unit_vector_w[i, 1] * self.ssp.L_max[i]).item()
            # ssp in soil
            end_x_ssp = tip_x + (self.ssp.ssp_unit_vector_w[i, 0] * self.ssp.L[i]).item()
            end_z_ssp = tip_z + (self.ssp.ssp_unit_vector_w[i, 1] * self.ssp.L[i]).item()
            ax.plot([tip_x, end_x_over], [tip_z, end_z_over], color="b", linewidth=0.5)
            ax.plot(
                [tip_x, end_x_max],
                [tip_z, end_z_max],
                color="g",
                label="SSP max length" if label else "",
            )
            ax.plot(
                [tip_x, end_x_ssp],
                [tip_z, end_z_ssp],
                color="g",
                marker="x",
                label="SSP" if label else "",
            )

            # centroid
            ax.scatter(
                self.ssp.centroid_w_x[i].item(),
                self.ssp.centroid_w_z[i].item(),
                marker="x",
                label="centroid" if label else "",
            )

            # COM
            ax.scatter(
                self.bucket_state.COM_pos_w_x[i].item(),
                self.bucket_state.COM_pos_w_z[i].item(),
                marker="p",
                label="bucket COM" if label else "",
            )

            # FEE
            # force unit vec
            end_x = tip_x + (self.ssp.ssp_unit_vector_w[i, 0] * self.ssp.L[i] / 2.0).item()
            end_z = tip_z + (self.ssp.ssp_unit_vector_w[i, 1] * self.ssp.L[i] / 2.0).item()
            start_x = end_x - self.forces.Rs_unit_vector_w[i, 0].item()
            start_z = end_z - self.forces.Rs_unit_vector_w[i, 2].item()
            ax.plot(
                [start_x, end_x],
                [start_z, end_z],
                color="b",
                label="FEE force direction" if label else "",
            )

            # soil failure wedge
            failure_plane = (
                Ry((np.pi - self.forces.rho[i] - self.ssp.beta[i]).cpu().numpy().squeeze())
                @ self.ssp.ssp_unit_vector_w[i].view(-1, 2, 1).cpu().numpy()
            )
            failure_plane = failure_plane.squeeze()
            failure_plane *= (self.ssp.L[i] * torch.sin(self.ssp.beta[i]) / torch.sin(self.forces.rho[i])).cpu().numpy()

            ax.plot(
                [tip_x, tip_x + failure_plane[0]],
                [tip_z, tip_z + failure_plane[1]],
                color="C1",
                label="failure_wedge" if label else "",
            )

            # ssp_unit_vector_w from tip to end of bucket
            lin_terrain = (
                Ry(-self.ssp.beta[i].cpu().numpy().squeeze())
                @ -self.ssp.ssp_unit_vector_w[i].view(-1, 2, 1).cpu().numpy()
            )
            lin_terrain = lin_terrain.squeeze()
            lin_terrain *= (
                (
                    self.ssp.L[i]
                    * (torch.cos(self.ssp.beta[i]) + torch.sin(self.ssp.beta[i]) / torch.tan(self.forces.rho[i]))
                )
                .cpu()
                .numpy()
            )
            ax.plot(
                [end_x_ssp, end_x_ssp + lin_terrain[0]],
                [end_z_ssp, end_z_ssp + lin_terrain[1]],
                color="C1",
            )

            # wedge centroid depth (only z)
            wedge_top_x = tip_x + failure_plane[0]
            wedge_top_z = tip_z + failure_plane[1]
            centroid_z = wedge_top_z - self.forces.z[i].item()
            ax.plot(
                [wedge_top_x, wedge_top_x, wedge_top_x + 1.0, wedge_top_x - 1.0],
                [wedge_top_z, centroid_z, centroid_z, centroid_z],
                label="wedge centroid z" if label else "",
                linestyle="dotted",
                linewidth=0.5,
            )

        if show:
            ax.grid()
            ax.legend()
            ax.axis("equal")

            plt.show(block=False)
            input("hit [ENTER] to exit")

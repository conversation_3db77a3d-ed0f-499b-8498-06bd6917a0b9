Derivation of Transformation Matrix for Multivariate Normal Distribution

Given a multivariate normal distribution with mean vector μ and covariance matrix Σ, we want to find a transformation matrix A such that if Z is a vector of independent standard normal random variables, then X = μ + AZ has the desired multivariate normal distribution N(μ, Σ).

Variables:

    X: Random vector with multivariate normal distribution N(μ, Σ)
    μ: Mean vector of the multivariate normal distribution
    Σ: Covariance matrix of the multivariate normal distribution
    A: Transformation matrix to be derived
    Z: Vector of independent standard normal random variables

Proof

    Compute the covariance matrix of X:


```
Cov(X) = E[(X - E[X])(X - E[X])ᵀ]
```
Substitute X with the expression μ + AZ:

```
Cov(μ + AZ) = E[((μ + AZ) - E[μ + AZ])((μ + AZ) - E[μ + AZ])ᵀ]
```

Since μ is a constant vector and E[Z] = 0 (because Z has a mean of 0), we get:

scss
```
Cov(μ + AZ) = E[AZ(ZᵀAᵀ)]
```

Using the fact that E[ZZᵀ] = I (identity matrix) for independent standard normal random variables:


```
Cov(μ + AZ) = A E[ZZᵀ] Aᵀ = AIAᵀ = AAᵀ
```
We want this covariance matrix to be equal to Σ:
```
Σ = AAᵀ
```

Decompose Σ using the eigendecomposition:
````
Σ = VDVᵀ
```
Here, V is the matrix of eigenvectors and D is a diagonal matrix of eigenvalues. Since Σ is a positive definite symmetric matrix, the eigenvalues are non-negative.

To find the transformation matrix A, we can make use of the Cholesky decomposition. The Cholesky decomposition decomposes a symmetric positive definite matrix into the product of a lower triangular matrix and its transpose:
```
Σ = LLᵀ
```

Here, L is a lower triangular matrix. In this case, we can set A = L:


```
    A = L
```
Thus, the transformation matrix A can be obtained using the Cholesky decomposition of the covariance matrix Σ. When this transformation matrix is multiplied by a vector of independent standard normal random variables, it produces correlated random variables with the desired multivariate normal distribution.

Alternatively, the transformation matrix A can be computed using the eigendecomposition:


```
A = VD^(1/2)
```
This is the matrix product of the eigenvector matrix V and the square root of the diagonal matrix of eigenvalues D^(1/2).

# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import numpy as np
import os
import torch

os.environ["CUDA_LAUNCH_BLOCKING"] = "1"


class SoilHeightRBF2D:
    def __init__(self, cfg, cfg_name, upper_limit=None, log_time=False):
        self.cfg = cfg
        self.cfg_name = cfg_name
        self.device = self.cfg.gym.sim_device
        self.n_envs = cfg.env.num_envs
        self.class_cfg = vars(self.cfg)[self.cfg_name]
        self.upper_limit = upper_limit
        self.scale = self.class_cfg.scale
        self.theta = self.class_cfg.theta

        steps_x = int(np.ceil((self.class_cfg.x_max - self.class_cfg.x_min) / self.class_cfg.min_resolution) + 1)
        steps_y = int(np.ceil((self.class_cfg.y_max - self.class_cfg.y_min) / self.class_cfg.min_resolution) + 1)

        self.step = self.class_cfg.min_resolution

        self.x = torch.linspace(self.class_cfg.x_min, self.class_cfg.x_max, steps_x, device=self.device)
        self.y = torch.linspace(self.class_cfg.y_min, self.class_cfg.y_max, steps_y, device=self.device)

        self.x_expanded = self.x.expand(self.n_envs, -1)
        self.y_expanded = self.y.expand(self.n_envs, -1)

        self.x_mesh = torch.meshgrid(self.x_expanded[0], self.y_expanded[0])[0]
        self.y_mesh = torch.meshgrid(self.x_expanded[0], self.y_expanded[0])[1]

        self.x_dim = self.x.shape[0]
        self.y_dim = self.y.shape[0]

        self.x_dist = torch.diff(self.x)[0]
        self.y_dist = torch.diff(self.y)[0]

        self.x_dist_expanded = self.x_dist.expand(self.n_envs).view(self.n_envs, -1)
        self.y_dist_expanded = self.y_dist.expand(self.n_envs).view(self.n_envs, -1)

        self.max_upper_idx_x = (self.x_dim - 1) * torch.ones(1, device=self.device, dtype=torch.int64)
        self.min_lower_idx_x = torch.zeros(1, device=self.device, dtype=torch.int64)

        self.max_upper_idx_y = (self.y_dim - 1) * torch.ones(1, device=self.device, dtype=torch.int64)
        self.min_lower_idx_y = torch.zeros(1, device=self.device, dtype=torch.int64)

        self.z = torch.zeros(self.n_envs, self.x_dim, self.y_dim, device=self.device, dtype=torch.float)

        self.cov_mat = torch.diag(self.x)  # values not used, just to initialize matrix
        self.compute_norm_transform(self.theta)
        self.sample()

        self.log_time = log_time
        if self.log_time:
            self.time_log = {}

    def compute_norm_transform(self, theta):
        theta = max(min(theta, 1.0), 0.0)

        x_grid, y_grid = torch.meshgrid(self.x, self.y)
        x_flat = x_grid.flatten()
        y_flat = y_grid.flatten()

        xy = torch.stack((x_flat, y_flat), dim=-1)

        dist_matrix = torch.cdist(xy, xy, p=2) ** 2
        self.cov_mat = torch.exp(-theta * dist_matrix)

        eig_vals, eig_vecs = torch.linalg.eigh(self.cov_mat)
        eig_vals[eig_vals < 0.0] = 0.0  # fix numerical issues
        self.norm_transform = eig_vecs @ torch.diag(torch.sqrt(eig_vals))

    def sample(self, idxs=...):
        if idxs == ...:
            dim0 = self.n_envs
        else:
            dim0 = len(idxs)

        if self.class_cfg.type == "rbf":
            rand_vec = torch.randn(dim0, self.x_dim * self.y_dim, 1, dtype=self.z.dtype, device=self.device)
            transformed_vec = self.norm_transform @ rand_vec
            transformed_vec = transformed_vec.view(dim0, self.x_dim, self.y_dim)
            self.z[idxs] = self.scale * transformed_vec.squeeze()

        # if self.class_cfg.offset is None:
        if self.upper_limit is not None:
            u = self.upper_limit.offset
        else:
            u = self.class_cfg.z_max
        l = self.class_cfg.z_min
        self.offset = (u - l) * torch.rand(dim0, 1, device=self.device) + l
        # elif isinstance(self.class_cfg.offset, dict):
        #     # the offset is computed at the shovel position,
        #     # done for debugging purposes.
        #     self.offset = (
        #             -self.z[idxs, self.class_cfg.offset["height_idx"]]
        #             + self.class_cfg.offset["height"]
        #     ).unsqueeze(-1)
        #
        # else:
        #     self.offset = self.class_cfg.offset
        # z has dim (n_envs, x_dim, y_dim) and offset has dim (n_envs, 1)
        # sum them
        self.z[idxs] = self.z[idxs] + self.offset.view(-1, 1, 1)

        self.z[idxs] = torch.clip(self.z[idxs], min=self.class_cfg.z_min, max=self.class_cfg.z_max)

        if self.upper_limit is not None:
            self.z[idxs] = torch.clip(
                self.z[idxs],
                max=self.upper_limit.z[idxs] - self.class_cfg.clip_margin,
            )

    def _interpolate_height_grid(self, x, y, env_ids=...):
        """
        Interpolate the height of the terrain on a grid given by x and y.
        :param x: x coordinates of the grid (num_envs, num_x_query)
        :param y: y coordinates of the grid (num_envs, num_y_query)

        :return: interpolated height (num_envs, num_x_query, num_y_query)
        """
        # if grid check that x and y have the same shape
        val_x = torch.clip(x, self.x[0], self.x[-1])
        val_y = torch.clip(y, self.y[0], self.y[-1])

        lower_x = self._find_lower_indices(val_x, self.x)
        lower_y = self._find_lower_indices(val_y, self.y)

        nom_x = val_x - self.x[lower_x]
        nom_y = val_y - self.y[lower_y]
        # assume constant step size
        weights_x = (nom_x / (self.x[1] - self.x[0])).unsqueeze(-1)  # (n_envs, num_x_query, 1)
        weights_y = (nom_y / (self.y[1] - self.y[0])).unsqueeze(1)  # (n_envs, 1, num_y_query)

        z_env = self.z[env_ids]

        num_env, num_x_query, num_y_query = lower_x.shape[0], lower_x.shape[1], lower_y.shape[1]
        num_env, num_x, num_y = z_env.shape[0], z_env.shape[1], z_env.shape[2]
        # Expand x_idx and y_idx to have the same shape
        lower_x_idx_expanded = lower_x.view(num_env, num_x_query, 1).expand(-1, -1, num_y)
        upper_x_idx_expanded = lower_x_idx_expanded + 1
        lower_y_idx_expanded = lower_y.view(num_env, 1, num_y_query).expand(-1, num_x_query, -1)
        upper_y_idx_expanded = lower_y_idx_expanded + 1

        # Use gather to get the desired elements along the x and y dimensions
        z_x_lower_y_lower = z_env.gather(1, lower_x_idx_expanded).gather(
            2, lower_y_idx_expanded
        )  # Shape: (num_env, num_x_query, num_y_query)
        z_x_lower_y_upper = z_env.gather(1, lower_x_idx_expanded).gather(
            2, upper_y_idx_expanded
        )  # Shape: (num_env, num_x_query, num_y_query)
        z_x_upper_y_lower = z_env.gather(1, upper_x_idx_expanded).gather(
            2, lower_y_idx_expanded
        )  # Shape: (num_env, num_x_query, num_y_query)
        z_x_upper_y_upper = z_env.gather(1, upper_x_idx_expanded).gather(
            2, upper_y_idx_expanded
        )  # Shape: (num_env, num_x_query, num_y_query)

        res_x1_y1 = torch.lerp(z_x_lower_y_lower, z_x_upper_y_lower, weights_x)
        res_x2_y1 = torch.lerp(z_x_lower_y_upper, z_x_upper_y_upper, weights_x)

        res_x1_y2 = torch.lerp(z_x_lower_y_lower, z_x_upper_y_lower, weights_x)
        res_x2_y2 = torch.lerp(z_x_lower_y_upper, z_x_upper_y_upper, weights_x)

        res_y1 = torch.lerp(res_x1_y1, res_x2_y1, weights_y)
        res_y2 = torch.lerp(res_x1_y2, res_x2_y2, weights_y)

        interpolated_z = torch.lerp(res_y1, res_y2, weights_y)

        return interpolated_z

    def _interpolate_height(self, x, y, env_ids=...):
        """
        Interpolate the height of the terrain at the given coordinates.
        :param x: x coordinates (num_envs, num_points)
        :param y: y coordinates (num_envs, num_points)
        :param env_ids: environment ids (num_envs, num_points)
        :return: interpolated height (num_envs, num_points)
        """
        # clip them
        val_x = torch.clip(x, self.x[0], self.x[-1])
        val_y = torch.clip(y, self.y[0], self.y[-1])

        lower_x = self._find_lower_indices(val_x, self.x)
        lower_y = self._find_lower_indices(val_y, self.y)

        nom_x = val_x - self.x[lower_x]
        nom_y = val_y - self.y[lower_y]

        # assume constant step size
        weights_x = (nom_x / (self.x[1] - self.x[0])).unsqueeze(-1)
        weights_y = (nom_y / (self.y[1] - self.y[0])).unsqueeze(-1)

        z_env = self.z[env_ids]

        num_env, num_points = lower_x.shape[0], lower_x.shape[1]
        num_env, num_x, num_y = z_env.shape[0], z_env.shape[1], z_env.shape[2]
        # Expand x_idx and y_idx to have the same shape
        lower_x_idx_expanded = lower_x.view(num_env, num_points, 1).expand(-1, -1, num_y)
        upper_x_idx_expanded = lower_x_idx_expanded + 1
        lower_y_idx_expanded = lower_y.view(num_env, num_points, 1)
        upper_y_idx_expanded = lower_y_idx_expanded + 1

        # Use gather to get the desired elements along the x and y dimensions
        z_x_lower_y_lower = z_env.gather(1, lower_x_idx_expanded).gather(
            2, lower_y_idx_expanded
        )  # Shape: (num_env, num_points, num_y)
        z_x_lower_y_upper = z_env.gather(1, lower_x_idx_expanded).gather(
            2, upper_y_idx_expanded
        )  # Shape: (num_env, num_points, num_y)
        z_x_upper_y_lower = z_env.gather(1, upper_x_idx_expanded).gather(
            2, lower_y_idx_expanded
        )  # Shape: (num_env, num_points, num_y)
        z_x_upper_y_upper = z_env.gather(1, upper_x_idx_expanded).gather(
            2, upper_y_idx_expanded
        )  # Shape: (num_env, num_points, num_y)

        res_x1_y1 = torch.lerp(z_x_lower_y_lower, z_x_upper_y_lower, weights_x)
        res_x2_y1 = torch.lerp(z_x_lower_y_upper, z_x_upper_y_upper, weights_x)

        res_x1_y2 = torch.lerp(z_x_lower_y_lower, z_x_upper_y_lower, weights_x)
        res_x2_y2 = torch.lerp(z_x_lower_y_upper, z_x_upper_y_upper, weights_x)

        res_y1 = torch.lerp(res_x1_y1, res_x2_y1, weights_y)
        res_y2 = torch.lerp(res_x1_y2, res_x2_y2, weights_y)

        interpolated_z = torch.lerp(res_y1, res_y2, weights_y).squeeze(-1)

        return interpolated_z

    def _interpolate_cylindrical_grid(self, r, theta, x_0, env_ids=...):
        """
        Interpolate the height of the terrain at the given cylindrical coordinates.
        :param r : r coordinates (num_envs, r_query)
        :param theta: theta coordinates (num_envs, theta_query)
        :param x_0: x_0 coordinates (num_envs, 2)
        :param env_ids: environment ids (num_envs, r_query)
        :return: interpolated height (num_envs, r_query, theta_query)
        """
        # transform in cartesian coordinates make sure the indices are correct for broadcasting
        x = r.unsqueeze(-1) * torch.cos(theta).unsqueeze(1)
        y = r.unsqueeze(-1) * torch.sin(theta).unsqueeze(1)

        # flatten them to (num_envs, r_query * theta_query)
        x = x.flatten(start_dim=1) + x_0[:, 0].unsqueeze(-1)
        y = y.flatten(start_dim=1) + x_0[:, 1].unsqueeze(-1)

        # interpolate
        z = self._interpolate_height(x, y, env_ids=env_ids)

        # reshape to (num_envs, r_query, theta_query)
        z = z.reshape(r.shape[0], r.shape[1], theta.shape[1])

        return z, x.reshape(r.shape[0], r.shape[1], theta.shape[1]), y.reshape(r.shape[0], r.shape[1], theta.shape[1])

    def _find_lower_indices(self, x_query, x):
        """
        Find the indices of the elements in x that are just lower than the corresponding elements in x_query.
        """
        lower = torch.searchsorted(x, x_query, right=True) - 1
        lower = torch.clip(lower, min=0, max=len(x) - 2)
        return lower

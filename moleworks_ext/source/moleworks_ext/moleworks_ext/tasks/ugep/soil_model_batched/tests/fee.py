#!/usr/bin/env python3

# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

import matplotlib.pyplot as plt
import numpy as np


def main():
    ### soil paramters
    # https://www.finesoftware.eu/help/geo5/en/adhesion-of-soil-01/
    # https://www.finesoftware.eu/help/geo5/en/adhesion-coefficient-01/
    # adhsion: anziehende kräfte zwischen zwei verschiedenen materialien
    ca = 10000  # [0-105 kPa] soil adhesion [Pa]

    # https://www.ecorisq.org/docs/USCS_soilclasses.pdf [0-25 kPa]
    # cohesion, dominque: 100-35000 [pa]
    # anziehende kräfte zeischen teilchen des gleichen körpers
    c = 15000

    # http://www.geotechnicalinfo.com/angle_of_internal_friction.html [25-45°]
    # https://www.ecorisq.org/docs/USCS_soilclasses.pdf [22-40°]
    # http://geotechdata.info/parameter/angle-of-friction
    phi = 0.5  # soil internal friction angle dominque: 18-38°

    # https://www.engineeringtoolbox.com/dirt-mud-densities-d_1727.html
    g = 9.81  # [m/s^2]
    gamma = g * 1500  # soil unit weight = density * g [N/m^3] dominique[kg/m^3]: 1000-2200 / geotechdata: 18-21k

    # https://www.finesoftware.eu/help/geo5/en/table-of-ultimate-friction-factors-for-dissimilar-materials-01/
    delta = 0.2  # [11°-22°] = [0.19 - 0.38] soil - metal friction angle ~same as dominique

    # lateral earth pressure at rest
    # http://www.geotechnicalinfo.com/lateral_earth_pressure_coefficient.html
    # http://robbinsfoundationsystems.com/wp-content/uploads/2015/04/Lateral-Earth-Pressures-Table-and-Graphs.pdf 0.46-0.7
    # gschaen: -
    # bennett:
    K0 = 0.5  # [-]

    ### geometry
    B = 1.4  # separation plate width
    ## from ssp

    # beta = np.pi/4 # angle ssp / soil
    # beta = np.linspace(0.0, 120./180.*np.pi, 100)
    beta = np.pi / 2
    rho = (np.pi / 4.0 - (phi + delta) / 2) + (np.pi / 4 - beta / 2)  # soil failure angle

    ssp_ang = np.pi / 6  # angle shovel bottom plate - ssp
    ssp_ang_max = 0.96
    alpha = 0.0  # soil surface inclination

    ## other geometr
    # d = 0.3  # depth shovel tip, perpendicular to soil
    d = np.linspace(-0.1, 0.5, 100)  # depth shovel tip, perpendicular to soil
    L = d / np.sin(beta)  # length of the separation plate IN CONTACT with soil

    # from bucket geometry / ssp / depth / orientation
    ssp_ang_bar = ssp_ang_max - ssp_ang
    k = np.pi - beta - ssp_ang_bar
    AX = np.sin(ssp_ang_bar) / np.sin(k) * L

    ABX = 0.5 * np.sin(beta) * AX * L

    ABC = 0.5 * L * np.sin(beta) * (L * np.cos(beta) + L * np.sin(beta) / np.tan(rho))  # park: A2
    ABC3 = 0.5 * np.sin(np.pi - rho - beta) * L * L * np.sin(beta) / np.sin(rho)

    BCX = ABC - ABX

    i = np.pi - k
    j = np.pi - i - rho
    BX = np.sin(beta) / np.sin(k) * L
    XC = np.sin(j) / np.sin(rho) * BX

    BCX2 = 0.5 * BX * XC * np.sin(i)
    ABC2 = ABX + BCX2
    ABX2 = ABC - BCX2

    # print('BCX: ', BCX, ' / ', BCX2)
    # print('ABC: ', ABC, ' / ', ABC2, ' / ', ABC3)
    # print('ABX: ', ABX, ' / ', ABX2)

    c_star = (ca * ABX + c * BCX) / ABC  # soil adhesion-cohesion
    phi_star = (delta * ABX + phi * BCX) / ABC  # combined friction angle

    z1 = (np.cos(beta) + np.sin(beta) / np.tan(rho)) * np.sin(alpha)
    z2 = np.cos(beta - alpha) + np.sin(beta - alpha) / np.tan(alpha + rho)
    z3 = np.sin(alpha) ** 2 * (np.cos(beta) + np.sin(beta) / np.tan(rho)) ** 2 - np.sin(beta - alpha) ** 2
    z4 = np.sin(beta) * (np.cos(beta) + np.sin(beta) / np.tan(rho))
    z = L * z1 - L / 3.0 * z2 * z3 / z4

    ### fee intermediate values
    ADF = ca * B * L
    W = 0.5 * gamma * B * L**2 * np.sin(beta) * (np.cos(beta) + np.sin(beta) / np.tan(rho))
    CF1 = c * B * L * np.sin(beta) / np.sin(rho)
    ACF = 0.5 * c_star * L**2 * np.sin(beta) * (np.cos(beta) + np.sin(beta) / np.tan(rho))
    SF2 = 0.5 * K0 * gamma * z * np.tan(phi_star) * L**2 * np.sin(beta) * (np.cos(beta) + np.sin(beta) / np.tan(rho))

    Rs1 = -ADF * np.cos(beta + rho + phi)
    Rs2 = W * np.sin(alpha + rho + phi)
    Rs3 = CF1 * np.cos(phi)
    Rs4 = 2.0 * ACF * np.cos(phi)
    Rs5 = 2.0 * SF2 * np.cos(phi)
    Rs6 = np.sin(beta + rho + delta + phi)

    Rs = (Rs1 + Rs2 + Rs3 + Rs4 + Rs5) / Rs6

    # plt.plot(beta/np.pi*180, Rs, label='rs')
    # plt.plot(beta/np.pi*180, Rs1, label='rs1')
    # plt.plot(beta/np.pi*180, Rs2, label='rs2')
    # plt.plot(beta/np.pi*180, Rs3, label='rs3')
    # plt.plot(beta/np.pi*180, Rs4, label='rs4')
    # plt.plot(beta/np.pi*180, Rs5, label='rs5')
    # plt.plot(beta/np.pi*180, Rs6, label='rs6')
    plt.plot(d, Rs, label="rs")
    plt.plot(d, Rs1, label="rs1")
    plt.plot(d, Rs2, label="rs2")
    plt.plot(d, Rs3, label="rs3")
    plt.plot(d, Rs4, label="rs4")
    plt.plot(d, Rs5, label="rs5")
    # plt.plot(d, Rs6, label='rs6')

    # plt.plot(beta/np.pi*180, rho, label='rho')
    plt.legend()
    plt.grid()
    plt.show(block=False)
    input("print [ENTER] to exit")

    print(Rs)


if __name__ == "__main__":
    main()

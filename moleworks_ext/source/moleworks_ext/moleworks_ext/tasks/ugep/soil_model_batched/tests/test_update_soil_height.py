# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

import matplotlib.pyplot as plt

# set seed for torch
import torch

from legged_gym.utils.config_utils import configclass
from soil_model_batched.soil_height_rbf import SoilHeightRBF
from soil_model_batched.tests.cfg import ExcavationEnvCfg

torch.manual_seed(0)

env_cfg = ExcavationEnvCfg()
soil_height_rbf = SoilHeightRBF(env_cfg, cfg_name="soil_height")
soil_height_rbf.compute_norm_transform(env_cfg.soil_height.theta)
soil_height_rbf.sample()
# soil_height_rbf.plot_soil_height([0, 1], show=False)

# subtract
steps = 10
x_min, x_max = env_cfg.soil_height.x_min, env_cfg.soil_height.x_max
# generat z values linearly decreating from x_min to x_max with slope of -0.1 starting at 0
x = torch.linspace(x_min + 3, x_max - 1, steps)
z = torch.linspace(x_min + 3, x_max - 1, steps) * -0.1
# add some noise
z += torch.rand(steps) * 0.1
plt.plot(x, z)
plt.figure()
# substract from env 0
soil_height_rbf.update_soil_height(z, x, torch.tensor([0]))
soil_height_rbf.plot_soil_height([0, 1], show=True)

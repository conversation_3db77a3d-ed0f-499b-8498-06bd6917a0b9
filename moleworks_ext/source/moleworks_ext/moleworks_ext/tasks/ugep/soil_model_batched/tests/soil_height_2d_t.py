# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from m545_isaac.envs.excavation_2d.cfg import ExcavationEnvCfg
from soil_model_batched.soil_height_2d import SoilHeightRBF2D

cfg = ExcavationEnvCfg()
cfg.soil_parameters.type = "s_0_0"
cfg.soil_height.type = "rbf"
cfg.soil_height.theta = 0.7
cfg.soil_height.scale = 0.1
cfg.env.num_envs = 3
cfg.gym.sim_device = "cpu"
soil_height_rbf = SoilHeightRBF2D(cfg, cfg_name="soil_height")
max_depth_height_rbf = SoilHeightRBF2D(cfg, cfg_name="max_depth_height", upper_limit=soil_height_rbf)

# plot x, y using meshgrid of env id 0
import torch

x = soil_height_rbf.x
y = soil_height_rbf.y
x_expanded = x.expand(1, -1)
y_expanded = y.expand(1, -1)
x_mesh, y_mesh = torch.meshgrid(x_expanded[0], y_expanded[0])

# now let's create a diffent meshgrid to test the interpolation at a different location
steps_x = 7
steps_y = 9
x_inter = torch.linspace(cfg.soil_height.x_min, cfg.soil_height.x_max, steps_x)
y_inter = torch.linspace(cfg.soil_height.y_min, cfg.soil_height.y_max, steps_y)
# random x_inter and y_inter
x_inter = torch.rand(steps_x) * (cfg.soil_height.x_max - cfg.soil_height.x_min) + cfg.soil_height.x_min
y_inter = torch.rand(steps_y) * (cfg.soil_height.y_max - cfg.soil_height.y_min) + cfg.soil_height.y_min
# introduce a batch dimension of size 2
x_inter = x_inter.expand(cfg.env.num_envs, -1)
y_inter = y_inter.expand(cfg.env.num_envs, -1)
print("x_inter shape:", x_inter.shape)
print("x_inter values:\n", x_inter.cpu().numpy())
print("y_inter shape:", y_inter.shape)
print("y_inter values:\n", y_inter.cpu().numpy())

# send tensors to the GPU
x_inter = x_inter.to(soil_height_rbf.device)
y_inter = y_inter.to(soil_height_rbf.device)
# let's get the soil height at those location
soil_height_inter = soil_height_rbf._interpolate_height_grid(x_inter, y_inter)
# max_depth_height_inter = max_depth_height_rbf._interpolate_height(x_inter, y_inter)

x_inter_2 = torch.rand(steps_x) * (cfg.soil_height.x_max - cfg.soil_height.x_min) + cfg.soil_height.x_min
y_inter_2 = torch.rand(steps_x) * (cfg.soil_height.y_max - cfg.soil_height.y_min) + cfg.soil_height.y_min
x_inter_2 = x_inter_2.expand(cfg.env.num_envs, -1)
y_inter_2 = y_inter_2.expand(cfg.env.num_envs, -1)

soil_height_inter_2 = soil_height_rbf._interpolate_height(x_inter_2, y_inter_2)

# cilyndrical grid
import numpy as np

step_r = 0.5
step_theta = 0.5
r_inter = torch.linspace(1, 3, 4)
theta_inter = torch.linspace(0, 2 * np.pi, 16)
r_inter = r_inter.expand(cfg.env.num_envs, -1)
theta_inter = theta_inter.expand(cfg.env.num_envs, -1)
x0 = torch.tensor([3, 0])
x0 = x0.expand(cfg.env.num_envs, -1)
soil_height_cilyndrical_inter, x_cil, y_cil = soil_height_rbf._interpolate_cylindrical_grid(r_inter, theta_inter, x0)

# evaluate the soil model
soil_height = soil_height_rbf.z[0]
max_depth_height = max_depth_height_rbf.z[0]

import matplotlib.pyplot as plt

# move the tensor to the CPU and convert to numpy
x_mesh = x_mesh.cpu().numpy()
y_mesh = y_mesh.cpu().numpy()
soil_height = soil_height.cpu().numpy()
max_depth_height = max_depth_height.cpu().numpy()

# interpolation pts for env 0
x_inter_mesh = x_inter[0].cpu().numpy()
y_inter_mesh = y_inter[0].cpu().numpy()
soil_height_inter = soil_height_inter[0].cpu().numpy()

# interpolation pts for env 0
x_inter_2 = x_inter_2[0].cpu().numpy()
y_inter_2 = y_inter_2[0].cpu().numpy()
soil_height_inter_2 = soil_height_inter_2[0].cpu().numpy()


# interpolation pts for env 0 for cilyndrical grid
x_cil_mesh = x_cil[0].cpu().numpy()
y_cil_mesh = y_cil[0].cpu().numpy()
soil_height_cilyndrical_inter = soil_height_cilyndrical_inter[0].cpu().numpy()

# set up the figure
fig = plt.figure()
ax = fig.add_subplot(111, projection="3d")

# create a transparent colormap
cmap = plt.cm.get_cmap("jet")
cmap._init()
cmap._lut[:, -1] = 0.5

# plot the soil height
ax.plot_surface(x_mesh, y_mesh, soil_height, cmap=cmap)
# plot the max depth
# ax.plot_surface(x_mesh, y_mesh, max_depth_height, cmap=cmap)

x_inter_mesh, y_inter_mesh = torch.meshgrid(x_inter[0], y_inter[0])

# plot the interpolation points as dots
ax.scatter(x_inter_mesh, y_inter_mesh, soil_height_inter, c="black")
# ax.scatter(x_inter_mesh, y_inter_mesh, max_depth_height_inter, c="black")
ax.scatter(x_inter_2, y_inter_2, soil_height_inter_2, c="blue")
# plot cilyndrical grid
ax.scatter(x_cil_mesh, y_cil_mesh, soil_height_cilyndrical_inter, c="red")

# set the same limits for all the axis
min_val = min(soil_height.min(), max_depth_height.min())
max_val = max(soil_height.max(), max_depth_height.max())
max_val = 0
min_val = -3
ax.set_xlim3d(x.cpu().numpy().min(), x.cpu().numpy().max())
ax.set_ylim3d(y.cpu().numpy().min(), y.cpu().numpy().max())
ax.set_zlim3d(min_val, max_val)

# set the axis labels
ax.set_xlabel("X")
ax.set_ylabel("Y")
ax.set_zlabel("Height")

# show the plot
plt.show()

# # create a x and y grid where to evaluate the soil model
# x = torch.linspace(cfg.soil_height.x_min, cfg.soil_height.x_max, 100)
# y = torch.linspace(cfg.soil_height.y_min, cfg.soil_height.y_max, 100)
# x_expanded = x.expand(1, -1)
# y_expanded = y.expand(1, -1)
#
# # evaluate the soil model
# soil_height = soil_height_rbf.sample(x_expanded, y_expanded)
# max_depth_height = max_depth_height_rbf(x_expanded, y_expanded)
#
# # plot the soil model
# fig, ax = plt.subplots(1, 2, figsize=(10, 5))
# ax[0].imshow(soil_height.cpu().numpy(), origin="lower")
# ax[0].set_title("soil height")
# ax[1].imshow(max_depth_height.cpu().numpy(), origin="lower")
# ax[1].set_title("max depth height")
# plt.show()

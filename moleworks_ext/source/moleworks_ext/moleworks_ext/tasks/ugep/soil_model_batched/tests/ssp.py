#!/usr/bin/env python3

# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

import matplotlib.pyplot as plt
import numpy as np

r = 0.375
a = 0.525

################
## C_ bucket
################

alpha_max = np.arctan(2 * r / a)
A_max = 0.5 * r**2 * np.pi + 2 * r * 0.5 * a

print("alpha_max: ", alpha_max)
print("A_max: ", A_max)

alpha = np.linspace(0, alpha_max, 100)


gamma = np.pi / 2 - alpha
x = a * np.tan(alpha)
beta = np.pi - gamma
b = r - x
delta = np.arcsin(b / r * np.sin(beta))
theta = np.pi - 2 * delta
phi = np.pi - beta - delta

A1 = 0.5 * x * a
A2 = (theta - np.sin(theta)) / 2 * r * r
A3 = 0.5 * b * r * np.sin(phi)
A4 = 0.5 * r * r * phi

A = A1 + A2 - (A4 - A3)

plt.plot(alpha, A, label="V1")

## version2

n = np.pi - gamma
m = np.pi - n
h = np.pi - m - delta

B1 = 0.5 * b * r * np.sin(h)
B2 = 0.5 * r * r * h

B = A1 - B1 + B2

plt.plot(alpha, B, label="V2")
plt.xlabel("alpha")
plt.ylabel("Area ~ Vol")


## fit polynomial
# c = np.polynomial.Polynomial.fit(alpha, A, 4)
f_alpha = np.polyfit(alpha, A, 3)
f_A = np.polyfit(A, alpha, 6)
# embed()
print("f_alpha: ", f_alpha)
print("f_A: ", f_A)


##################
## Centroid of deadload
##################
idx = 99

ssp_max = a / np.cos(alpha) + r * np.sin(h) / np.sin(m)

y = h.copy()
for i in range(len(h)):
    y[i] = 2.0 * r * np.sin(h[i] / 2.0) / (1.5 * h[i]) if h[i] > 0.0001 else 0.0


x1 = a + np.sin(h / 2.0) * y
z1 = r - np.cos(h / 2) * y

x2 = 2.0 * a / 3.0 * np.ones(shape=x.shape)
z2 = x / 3.0

hc = np.sin(h) * r
xi = np.cos(h) * r

x3 = a + hc / 3.0
z3 = r - (xi + b) / 3.0

x_tot = B.copy()
z_tot = B.copy()
for i in range(len(B)):
    x_tot[i] = (x1[i] * B2[i] - x3[i] * B1[i] + x2[i] * A1[i]) / B[i] if B[i] > 0.0001 else 0.0
    z_tot[i] = (z1[i] * B2[i] - z3[i] * B1[i] + z2[i] * A1[i]) / B[i] if B[i] > 0.0001 else 0.0


xA = 0
zA = 0
xB = a
zB = 0
xC = a
zC = r
xD = np.cos(alpha[idx]) * ssp_max[idx]
zD = np.sin(alpha[idx]) * ssp_max[idx]


ang = np.linspace(-np.pi / 2.0, np.pi / 2.0, 100)
bx = r * np.cos(ang) + xC
bz = r * np.sin(ang) + zC

plt.figure()
plt.plot([xA, xB], [zA, zB])
plt.plot([xA, xD], [zA, zD])
plt.plot([xB, xC], [zB, zC])
plt.plot([xC, xD], [zC, zD])
plt.plot([xC, x1[idx]], [zC, z1[idx]])
plt.plot(
    xC,
    zC,
    marker="o",
    label="center",
)
plt.plot(bx, bz)

plt.plot(x1[idx], z1[idx], marker="X", label="B2")
plt.plot(x2[idx], z2[idx], marker="X", label="B1")
plt.plot(x3[idx], z3[idx], marker="X", label="A1")
plt.plot(x_tot[idx], z_tot[idx], marker="P", label="total")
plt.legend()
plt.axis("equal")


# plt.plot(alpha_fit, A_fit2, label='polyfit')

##################
## square bucket [_ (circle approximated with square)
##################
alpha_thresh = np.arctan(2 * r / (r + a))
alpha_max = np.arctan(2 * r / a)

alpha1 = np.linspace(0, alpha_thresh, 50)
alpha2 = np.linspace(alpha_thresh, alpha_max, 50)

# 0 < alpha < alpha_thresh
x = np.tan(alpha1) * (r + a)
C1 = 0.5 * x * (r + a)

# alpha_thres < alpha < alpha_max
y = 2 * r / np.tan(alpha2)
C2 = 2 * r * (r + a - y) + 0.5 * 2 * r * y

plt.figure()
A_samples = np.linspace(0, A_max, 100)
# alpha_fit, A_fit = c.linspace()
plt.plot(alpha, np.polyval(f_alpha, alpha), label="polyfit f_alpha")
plt.plot(np.polyval(f_A, A_samples), A_samples, label="polyfit f_A")

plt.plot(alpha1, C1, label="square1")
plt.plot(alpha2, C2, label="square2")


## inverse
A_thresh = 0.5 * 2 * r * (r + a)
A_max = 2 * r * r + 0.5 * 2 * r * a

A1 = np.linspace(0, A_thresh, 50)
A2 = np.linspace(A_thresh, A_max, 50)

alpha1_inv = np.arctan(2 * A1 / (r + a) ** 2)
alpha2_inv = np.arctan(2 * r**2 / (-A2 + 2 * r**2 + 2 * a * r))

plt.plot(alpha1_inv, A1, label="inv1", marker="x")
plt.plot(alpha2_inv, A2, label="inv2", marker="x")


plt.legend()
plt.show(block=False)
input("hit [ENTER] to exit")

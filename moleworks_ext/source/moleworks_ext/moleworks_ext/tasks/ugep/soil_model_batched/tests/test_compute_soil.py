# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

import matplotlib.pyplot as plt

# set seed for torch
import torch

from legged_gym.utils.config_utils import configclass
from soil_model_batched.soil_height_rbf import SoilHeightRBF
from soil_model_batched.tests.cfg import ExcavationEnvCfg

torch.manual_seed(0)

env_cfg = ExcavationEnvCfg()
soil_height_rbf = SoilHeightRBF(env_cfg, cfg_name="soil_height")
soil_height_rbf.compute_norm_transform(env_cfg.soil_height.theta)
soil_height_rbf.sample()
x_min = torch.tensor([4.0, 4.0], device="cuda")
x_max = torch.tensor([8.0, 8.0], device="cuda")
soil = soil_height_rbf.compute_soil_amount(x_min, x_max)
print("soil amount: ", soil)
soil_height_rbf.plot_soil_height([0, 1], show=True)

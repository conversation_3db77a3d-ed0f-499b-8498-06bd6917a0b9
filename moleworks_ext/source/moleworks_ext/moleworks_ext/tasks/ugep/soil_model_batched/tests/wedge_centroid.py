#!/usr/bin/env python

# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

import matplotlib.pyplot as plt
import numpy as np


def deg2rad(deg):
    return deg / 180.0 * np.pi


def Ry(a):
    return np.array([[np.cos(a), -np.sin(a)], [np.sin(a), np.cos(a)]])


sp_alpha = deg2rad(-90)

ssp_beta = deg2rad(110.0)
rho = deg2rad(20)
ssp_L = 0.5

##############################################
## original computation
##############################################
fee_sp_alpha = -sp_alpha
z1 = (np.cos(ssp_beta) + np.sin(ssp_beta) / np.tan(rho)) * np.sin(fee_sp_alpha)
z2 = np.cos(ssp_beta - fee_sp_alpha) + np.sin(ssp_beta - fee_sp_alpha) / np.tan(fee_sp_alpha + rho)
z3_1 = np.sin(fee_sp_alpha) * (np.cos(ssp_beta) + np.sin(ssp_beta) / np.tan(rho))
z3_2 = np.sin(ssp_beta - fee_sp_alpha)

z3 = z3_1 * z3_1 - z3_2 * z3_2
z4 = np.sin(ssp_beta) * (np.cos(ssp_beta) + np.sin(ssp_beta) / np.tan(rho))
# if ssp_beta is 0, z4 is 0 and z is nan -> if in soil -> invalid soil model -> if not in soil -> ok, fee & pen == 0
z_original = ssp_L * (z1 - 1.0 / 3.0 * z2 * z3 / z4)
# degenerates if alpha+rho = 0
# z = np.where(fee_sp_alpha + rho == 0.0, -1.0 / 3.0 * ssp_L * np.sin(ssp_beta + rho), z)

#######################################
## alternative computation, average of z of triangle edges relative to tip (0,0)
#######################################

L2 = np.sin(rho + ssp_beta) / np.sin(rho) * ssp_L
ha = np.sin(fee_sp_alpha) * L2

L3 = np.sin(ssp_beta) / np.sin(rho) * ssp_L
hc = np.sin(fee_sp_alpha + rho) * L3
z = (ha + hc) / 3
#####################################


# plotting
soil = Ry(sp_alpha) @ (2 * np.array([-1, 0]))

L_vec = Ry(ssp_beta + sp_alpha) @ (ssp_L * np.array([-1, 0]))
plane_vec = Ry(-(rho - sp_alpha)) @ np.array([-1, 0]) * np.sin(ssp_beta) / np.sin(rho) * ssp_L

tip_x = L_vec[0]
tip_z = L_vec[1]
plt.plot([0, tip_x], [0, tip_z], label="ssp")
plt.plot([0, soil[0]], [0, soil[1]], label="soil")

wedge_top_x = tip_x + plane_vec[0]
wedge_top_z = tip_z + plane_vec[1]

plt.plot([L_vec[0], wedge_top_x], [L_vec[1], wedge_top_z], label="failure plane")

centroid_z = wedge_top_z - z

print("z: ", z)
print("z_original: ", z_original)

plt.plot(
    [wedge_top_x, wedge_top_x, wedge_top_x + 1.0, wedge_top_x - 1.0],
    [wedge_top_z, centroid_z, centroid_z, centroid_z],
    label="wedge centroid z",
    linestyle="dotted",
    linewidth=0.5,
)

# true
C_x = (0 + tip_x + wedge_top_x) / 3.0
C_z = (0 + tip_z + wedge_top_z) / 3.0

plt.scatter(C_x, C_z, marker="x", label="true centroid")

plt.grid()
plt.legend()
plt.axis("equal")
plt.show(block=False)
input("Press [ENTER] to exit")

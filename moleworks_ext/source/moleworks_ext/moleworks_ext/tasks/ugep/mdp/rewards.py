# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
import torch
from typing import TYPE_CHECKING

import isaacsim.core.utils.torch as torch_utils

from isaaclab.assets import Articulation, RigidObject
from isaaclab.managers import SceneEntityCfg
def action_rate_l2_excavation(env) -> torch.Tensor:
    """Penalize the rate of change of the actions using L2-kernel."""
    return torch.sum(torch.square(env.actions - env.last_actions), dim=1)


def const_reward(env):
    return torch.ones(env.num_envs, device=env.device)


def reward_bucket_velocity_penalty(env) -> torch.Tensor:
    """Penalize bucket velocities that exceed the maximum allowed velocity.

    The penalty scales linearly by default, with an optional exponential factor
    to increase the penalty severity for higher violations.
    """
    excess_vel = torch.nn.functional.relu(
        env.m545_measurements.bucket_vel_norm
        - env.cfg.terminations_excavation.max_bucket_vel
    )
    # Use linear scaling with optional exponential factor (e.g., 1.5) for more aggressive penalties
    penalty = -excess_vel * torch.pow(10.0, excess_vel)
    penalty = torch.clamp(penalty, min=-1.0)
    return penalty.squeeze()


def reward_angle_of_attack_penalty(env) -> torch.Tensor:
    """Penalize negative angles of attack of the bucket.

    The penalty scales linearly by default, with an optional exponential factor
    to increase the penalty severity for larger negative angles.
    """
    # Get negative angles only (clip positive angles to 0)
    negative_angle = torch.nn.functional.relu(-env.soil.bucket_bp_angle_w)
    # Use linear scaling with optional exponential factor for more aggressive penalties
    penalty = -negative_angle * torch.pow(2.0, negative_angle)
    penalty = torch.clamp(penalty, min=-1.0)
    return penalty.squeeze()


def reward_soil_spilling_penalty(env) -> torch.Tensor:
    """Penalize soil loss when bucket velocity exceeds the spilling threshold.

    The penalty scales with the amount of soil lost, but only applies
    when moving above the velocity threshold.
    """
    # Calculate how much soil was lost (clip to only consider losses)
    soil_loss = torch.nn.functional.relu(
        env.last_fill_ratio - env.soil.fill_ratio
    ).squeeze()

    # Check if velocity exceeds threshold
    above_threshold = (
        env.m545_measurements.bucket_vel_norm
        > env.cfg.terminations_excavation.bucket_vel_spillig_threshold
    )

    # Only apply soil loss penalty when above velocity threshold
    penalty = torch.where(above_threshold, -soil_loss, torch.zeros_like(soil_loss))
    penalty = torch.clamp(penalty, min=-1.0)
    return penalty.squeeze()


def reward_bucket_filling(env):
    # if full, 0 reward (because it is the normalized, clipped ratio)
    # if loosing soil, large negative reward 0-prev
    fill_delta = (env.soil.fill_ratio - env.last_fill_ratio).squeeze()
    fill_delta = torch.clip(fill_delta, min=0.0)
    close = (
        env.m545_measurements.bucket_pos_w[:, 0]
        < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )
    # if too close, no more positive reward for filling, allow negative reward for losing
    return torch.where(close, torch.clip(fill_delta, max=0.0), fill_delta)


def reward_max_depth_tracking(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0]
        < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (
        env.soil.fill_ratio > env.curriculum_excavation.curr_term_fill_ratio
    ).squeeze()

    err = (
        env.m545_measurements.bucket_pos_w[:, 2].unsqueeze(-1)
        - (
            env.soil.get_max_depth_height_at_pos(
                env.m545_measurements.bucket_pos_w[:, 0].unsqueeze(-1)
            )
            - env.cfg.rewards_excavation.max_depth_tracking_offset
        )
    ).squeeze()

    rew = torch.exp(
        -torch.square(err) / env.cfg.rewards_excavation.max_depth_tracking_sigma
    )

    return torch.where(close | full, torch.zeros(env.num_envs, device=env.device), rew)


def reward_bucket_curl(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0]
        < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (env.soil.fill_ratio > env.curriculum_excavation.curr_curl_ratio).squeeze()

    # give reward while not curled enough
    not_curled = (
        env.m545_measurements.bucket_ang_gac
        > env.cfg.terminations_excavation.max_curl_ang_gac
    ).squeeze()

    return torch.where(
        (close | full) & not_curled,
        env.m545_measurements.j_pitch_ang_vel[:, 1],
        torch.zeros(env.num_envs, device=env.device),
    )


def reward_pitch_up(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0]
        < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (
        env.soil.fill_ratio > env.curriculum_excavation.curr_term_fill_ratio
    ).squeeze()

    # give reward while not high enough (negative depth = outside of soil)
    # not_high = (-self.env.soil.get_bucket_depth() < self.env.curr_manager.end_height_above_soil).squeeze()

    # Bucket COM is exactly at top of bucket (it can be that edge is high enough, but pitch not, and then no rew is given)
    # pitch should be at same hight than bucket edge at end (horizontal bucket full level)
    not_high = (
        env.soil.bucket_com_pos_w[:, 1:2]  # only x-z, 2d vec
        - env.soil.get_soil_height_at_pos(env.m545_measurements.bucket_pos_w[:, 0:1])
        < env.curriculum_excavation.end_height_above_soil
    ).squeeze()

    # return torch.where((close | full) & not_high, self.env.m545_asset.j_pitch_vel[:, 2], self.zero_vec)

    # bottom plate at least horizontal before giving reward for going up
    # it looses all the soil if it out of soil and bp_ang > 0
    curled = (env.soil.bucket_bp_angle_w < 0.0).squeeze()

    return torch.where(
        (close | full) & not_high & curled,
        env.m545_measurements.j_pitch_vel[:, 2],
        torch.zeros(env.num_envs, device=env.device),
    )


def reward_bucket_edge_up(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0]
        < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (
        env.soil.fill_ratio > env.curriculum_excavation.curr_term_fill_ratio
    ).squeeze()

    # give reward while not hight enough (negative depth = outside of soil)
    not_high = (
        -env.soil.bucket_depth < env.curriculum_excavation.end_height_above_soil
    ).squeeze()
    return torch.where(
        (close | full) & not_high,
        env.m545_measurements.bucket_vel_w[:, 2],
        torch.zeros(env.num_envs, device=env.device),
    )


def reward_bucket_edge_down(env):
    not_close = (
        env.m545_measurements.bucket_pos_w[:, 0]
        > env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    not_full = (
        env.soil.fill_ratio < env.cfg.rewards_excavation.go_down_fill_ratio
    ).squeeze()
    # return torch.where(
    #     # clip to 0 -> learns slower
    #     # not_full & not_close, torch.minimum(self.env.m545_asset.bucket_vel_w[:, 2], self.zero_vec), self.zero_vec
    #     not_full & not_close,
    #     self.env.m545_asset.bucket_vel_w[:, 2],
    #     self.zero_vec,
    # )

    bucket_vel_into_soil = torch.sum(
        env.m545_measurements.bucket_vel_w * -env.soil_normal_vec, dim=-1
    )
    return torch.where(
        # clip to 0 -> learns slower
        # not_full & not_close, torch.minimum(self.env.m545_asset.bucket_vel_w[:, 2], self.zero_vec), self.zero_vec
        not_full & not_close,
        -bucket_vel_into_soil,  # reward is negative, because we had in negative z direction before, this is now positive if it is going down
        torch.zeros(env.num_envs, device=env.device),
    )


def reward_power(env):
    not_close = env.m545_measurements.bucket_pos_w[:, 0] > env.pullup_dist  # close
    not_full = (
        env.soil.fill_ratio < env.curriculum_excavation.curr_term_fill_ratio
    ).squeeze()
    clipped_power = torch.clip(
        torch.sum(env.m545_measurements.joint_vel * env.torques, dim=-1), min=0.0
    )

    return torch.where(
        not_full & not_close,
        clipped_power,
        torch.zeros(env.num_envs, device=env.device),
    )


def reward_bucket_curl_and_up(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0]
        < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (env.soil.fill_ratio > env.curriculum_excavation.curr_curl_ratio).squeeze()

    # # give reward while not curled enough
    # not_curled = (self.env.soil.get_bucket_full_angle_w() > self.env.min_terminal_curl_angle).squeeze()

    # # give reward while not hight enough (negative depth = outside of soil)
    # not_high = (-self.env.soil.get_bucket_depth() < self.env.curr_manager.end_height_above_soil).squeeze()

    up_vel = env.m545_measurements.bucket_vel_w[:, 2]
    curl_vel = env.m545_measurements.j_pitch_ang_vel[:, 1]

    return torch.where(
        close | full, up_vel * curl_vel, torch.zeros(env.num_envs, device=env.device)
    )


def termination_reward(env) -> torch.Tensor:
    return env.termination_excavation.compute_reward()


import torch

# Assume this function will be added to your existing rewards python file.
# Make sure necessary imports like `torch` are present.


def reward_pullup_barrier(
    env,
    P_margin: float = 0.2,  # start fading at P - 0.2 m (was 0.5)
    k_fade: float = 10.0,  # slope of the sigmoid
    k_overshoot: float = 5.0,  # softplus gain past P
) -> torch.Tensor:
    """
    Smooth penalty that ramps up before reaching P and grows gently past P.
    Output is in [-1, 0].
    """
    x = env.m545_measurements.bucket_pos_w[:, 0]
    P = env.pullup_dist.squeeze()  # shape (N,)

    # Distance to the fade start (negative = far away, positive = in the fade zone)
    d_fade = x - (P - P_margin)
    # Sigmoid ∈ (0,1) –> smoothly turns on inside the margin
    fade = torch.sigmoid(k_fade * d_fade)

    # Overshoot term: 0 before P, grows ~ linearly after P but stays smooth
    overshoot = torch.nn.functional.softplus(k_overshoot * (x - P))

    # Combine, then squash to [-1,0] so it never dominates other rewards
    penalty = -torch.tanh(fade + overshoot)
    return penalty


def reward_aoa_barrier_near_soil(
    env,
    target_min_aoa: float = 0.2,  # rad
    depth_half: float = 0.05,  # 5 cm – how quickly "near soil" ramps in
    aoa_half: float = 0.05,  # 0.05 rad – how quickly AoA penalty ramps in
) -> torch.Tensor:
    """
    Gives a negative reward if AoA < target when bucket is near/in soil.
    Both "near soil" and "below target AoA" are sigmoids for smooth shaping.
    """
    aoa = env.soil.bucket_bp_angle_w.squeeze()  # (N,)
    depth = env.soil.bucket_depth.squeeze()  # (N,)  positive in soil

    # Soft mask for proximity to soil (0 = far, 1 = deep)
    near_soil = torch.sigmoid((depth + depth_half) / depth_half)

    # Soft mask for AoA below target (0 = ok, 1 = way too small)
    aoa_deficit = torch.sigmoid((target_min_aoa - aoa + aoa_half) / aoa_half)

    # Product → only large when both conditions hold
    penalty = -near_soil * aoa_deficit  # ∈ (-1, 0]
    return penalty
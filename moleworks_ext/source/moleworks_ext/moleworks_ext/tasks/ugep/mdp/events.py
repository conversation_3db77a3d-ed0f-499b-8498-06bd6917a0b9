# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause


from __future__ import annotations

import torch
from typing import TYPE_CHECKING

from isaaclab.assets import Articulation, RigidObject
from isaaclab.managers import SceneEntityCfg
from isaaclab.utils.math import quat_from_euler_xyz, sample_uniform

import moleworks_ext.tasks.ugep.excavation_utils.helpers as helpers
from moleworks_ext.tasks.ugep.excavation_utils.excavation_utils import u_rand

if TYPE_CHECKING:
    from isaaclab.envs import BaseEnv


# def rejection_sampling(
#     env: BaseEnv,
#     env_ids: torch.Tensor,
#     asset_cfg: SceneEntityCfg = SceneEntityCfg("robot"),
# ):
#     # print("start reset_idx")
#     """Reset selected environments.
#     resetting flag for termination in  step? -> only needed if reset_stepping is enabled, not used in latest version, doublecheck
#     Resetting
#         - reset pid
#         - reset soil/fee

#     Soil resampling
#         - if enabled: sample soil with height (excavator config is then relative to the soil)
#         - if enabled: sample max_depth
#         - if enabled: sample pullup distance
#         - if enabled: sample soil_obstacles

#         - if enabled: randomize orientation (pitch, turn?)
#         - if enabled: sample arm config
#             if in soil, sample random fill volume
#         - set wheel support positions

#     Arm Resampling
#         - update measurements
#         - update fee forces (check if in obstacle and change params accordingly)
#         - reset again if forces too large


#     Other
#         Logs episode info
#         Resets selected buffers

#     Args:
#         env_ids (list[int]): List of environment ids which must be reset
#     """
#     asset: RigidObject = env.scene[asset_cfg.name]

#     if len(env_ids) == 0:
#         return
#     # env.pid.reset(idxs=env_ids)
#     env.soil.reset(idxs=env_ids)
#     env.termination_excavation.reset(idxs=env_ids)

#     if env.cfg.reset.sample_soil:
#         env.soil.sample(idxs=env_ids)  # height & params & max depth, random or fixed, depending on cfg

#     if env.cfg.reset.sample_max_depth:
#         # TODO, already in soil.sample, maybe take it out three
#         pass

#     if env.cfg.reset.sample_pullup_dist:
#         env.pullup_dist[env_ids] = u_rand(
#             len(env_ids), env.cfg.reset.pullup_dist_range[0], env.cfg.reset.pullup_dist_range[1], env.device
#         )

#     env.m545_measurements.joint_vel[env_ids] = 0.0  # DOF Vel
#     env.m545_measurements.root_lin_vel_w[env_ids] = 0.0
#     env.m545_measurements.root_ang_vel_w[env_ids] = 0.0
#     env.m545_measurements.j_pitch_vel[env_ids] = 0.0
#     env.m545_measurements.j_pitch_ang_vel[env_ids] = 0.0

#     # rejection "sampling" using cached reset states
#     # all envs to reset are rejected at the beginning
#     rejected = env_ids.clone()

#     # get index bands to sample from
#     rejected_sampling_idx_lower, rejected_sampling_idx_upper = env.reset_cache.get_sampling_idx(rejected)

#     reset_loop_count = 0
#     while len(rejected) > 0:
#         if reset_loop_count > 1000:
#             raise ValueError("Resetting loop count > 1000")
#         reset_loop_count += 1

#         env.soil.reset(rejected)  # can be that before it was in soil, fill state was set but rejected

#         if not env.cfg.reset.fixed_config:
#             sampled = torch.round(
#                 u_rand(len(rejected), rejected_sampling_idx_lower, rejected_sampling_idx_upper, device=env.device)
#             ).to(torch.long)

#         else:
#             sampled = torch.ones(len(rejected), device=env.device, dtype=torch.long)
#             sampled *= env.cfg.reset.configs["idx"]  # env.cfg.reset.configs.__dict__[env.cfg.reset.fixed_config]["idx"]
#             print(f"FIXED reset to sample nr. {sampled[0]}")

#         env.sampled[rejected] = sampled
#         #####
#         ## Get Measurements from Cache
#         #####
#         # when slicing, does not loose reference!
#         env.m545_measurements.joint_pos[rejected] = env.reset_cache.data["dof_pos"][sampled]
#         env.m545_measurements.root_pos_w[rejected] = env.reset_cache.data["base_pos"][sampled]
#         env.m545_measurements.root_quat_w[rejected] = env.reset_cache.data["base_quat"][sampled]

#         # for idx, box_asset in enumerate(env.box_asset_list):
#         #    box_asset.root_pos_w[rejected] = env.reset_cache.data["box_" + str(idx)][sampled] - 0.016
#         env.m545_measurements.mm[rejected] = env.reset_cache.data["mass_matrix"][sampled]
#         env.m545_measurements.bucket_jac_lin_T_dof[rejected] = env.reset_cache.data["bucket_jac_lin"][
#             sampled
#         ].transpose(-2, -1)
#         env.m545_measurements.bucket_jac_rot_T_dof[rejected] = env.reset_cache.data["bucket_jac_rot"][
#             sampled
#         ].transpose(-2, -1)

#         env.m545_measurements.gravity_tau[rejected] = env.reset_cache.data["gravity_torques"][sampled]

#         env.m545_measurements.bucket_pos_w[rejected] = env.reset_cache.data["bucket_pos"][sampled]
#         env.m545_measurements.prev_bucket_pos_w[rejected] = env.reset_cache.data["bucket_pos"][
#             sampled
#         ]  # prev = current
#         env.m545_measurements.bp_unit_vector_w[rejected] = env.reset_cache.data["bp_unit_vec"][sampled]
#         env.m545_measurements.bucket_vel_w[rejected, :] = 0.0

#         # update all env fee (easier than do it per index)
#         env.soil.update_1(
#             rejected
#         )  # until bucket state, we sample fill volume based on depth and finish the soil update

#         # get indices to resample soil fill ratio, if inside soil (depth > 0)
#         # only do it for the ones to resample!!!!! -> changes ssp -> might lead to invalid soil config!!!
#         rejected_in_soil_idx = torch.where(env.soil.get_bucket_depth()[rejected] > env.zero_scalar)[0]

#         env.soil.set_bucket_fill_state(
#             torch.rand(len(rejected_in_soil_idx), 1, device=env.device),
#             rejected[rejected_in_soil_idx],
#             is_ratio=True,
#         )

#         env.soil.update_2()  # update ssp, soil_params, forces

#         # check the conditions
#         too_close_x = (
#             torch.abs(env.m545_measurements.bucket_pos_w[rejected, 0:1])
#             < env.pullup_dist[rejected].unsqueeze(-1) + env.curriculum_excavation.curr_pullup_band
#         )

#         # these should now never be active anymore, because we sample only in the valid range
#         # wrong, its min, max based on all soil heights in env

#         too_high_z = env.soil.get_bucket_depth()[rejected] < env.cfg.reset.arm_depth_limits.lower
#         too_low_z = env.soil.get_bucket_depth()[rejected] > env.cfg.reset.arm_depth_limits.upper * (
#             1.0 - env.cfg.reset.only_above_soil
#         )

#         too_small_ang = env.soil.get_ssp_ang_to_soil()[rejected] < env.cfg.reset.min_ang_bucket_to_soil
#         invalid_soil_model_state = env.soil.is_state_invalid(rejected)

#         # too_large_force = (
#         #     torch.linalg.norm(env.soil.get_resultant_force()[rejected], dim=-1, keepdim=True)
#         #     > env.cfg.reset.max_soil_force
#         # )
#         # too_large_moment = (
#         #     torch.linalg.norm(env.soil.get_resultant_moment()[rejected], dim=-1, keepdim=True)
#         #     > env.cfg.reset.max_soil_moment
#         # )  # only moment in y

#         force_moment = torch.linalg.norm(
#             env.soil.get_resultant_force()[rejected], dim=-1, keepdim=True
#         ) + torch.linalg.norm(env.soil.get_resultant_moment()[rejected], dim=-1, keepdim=True)

#         # max_force = 68.0 - 6.0 * env.m545_measurements.bucket_pos_w[rejected, 0:1] #50-20
#         # max_force = 74.0 - 8.0 * env.m545_measurements.bucket_pos_w[rejected, 0:1]  # 50-10
#         # max_force = 58.0 - 6.0 * env.m545_measurements.bucket_pos_w[rejected, 0:1]  # 40-10
#         # max_force = 42.0 - 4.0 * env.m545_measurements.bucket_pos_w[rejected, 0:1]  # 30-10
#         # max_force = 26.0 - 2.0 * env.m545_measurements.bucket_pos_w[rejected, 0:1]  # 30-10

#         too_large_force_moment = force_moment > env.cfg.reset.max_soil_force_and_moment
#         # too_large_force = force_moment > max_force * 1000.0
#         violating_max_depth = (
#             env.m545_measurements.bucket_pos_w[rejected, 2:]
#             < env.soil.get_max_depth_height_at_pos(env.m545_measurements.bucket_pos_w[rejected, 0:1], env_ids=rejected)
#             - env.cfg.terminations_excavation.max_depth_overshoot
#         )

#         # check joint torques at reset soil + gravity!!

#         env.bucket_force_com[rejected] = env.soil.get_resultant_force()[rejected]
#         env.bucket_moment_com[rejected] = env.soil.get_resultant_moment()[rejected]

#         ext_f_genco_tau = torch.matmul(
#             env.m545_measurements.bucket_jac_lin_T_dof[rejected], env.bucket_force_com[rejected].unsqueeze(-1)
#         )
#         env.ext_f_tau[rejected] = ext_f_genco_tau.squeeze(-1)[:, -env.num_dofs :]

#         ext_m_genco_tau = torch.matmul(
#             env.m545_measurements.bucket_jac_rot_T_dof[rejected], env.bucket_moment_com[rejected].unsqueeze(-1)
#         )
#         env.ext_m_tau[rejected] = ext_m_genco_tau.squeeze(-1)[:, -env.num_dofs :]

#         # des_dof_acc = (torch.tensor([25,30,20,20], device=env.device)*env.clipped_scaled_actions[rejected])
#         # inertial_therm = (env.m545_measurements.mm[rejected].matmul(des_dof_acc.view(len(rejected), 4, 1)).view(len(rejected), 4))

#         env.torques[rejected] = (
#             -env.m545_measurements.gravity_tau[rejected] - env.ext_f_tau[rejected] - env.ext_m_tau[rejected]
#         )

#         env.limits.update(env.m545_measurements.joint_pos)

#         # if only within limits -> isaac crash -> probably too large forces, this is fine 0.22% done after reset + 1 step
#         violating_torque_lower_limits = (
#             env.torques[rejected]
#             < env.limits.curr_torque_limit_lower[rejected] + env.cfg.reset.max_soil_force_and_moment
#         ).any(dim=-1, keepdim=True)
#         violating_torque_upper_limits = (
#             env.torques[rejected]
#             > env.limits.curr_torque_limit_upper[rejected] - env.cfg.reset.max_soil_force_and_moment
#         ).any(dim=-1, keepdim=True)

#         # narrow resetting for trenching tests
#         if env.cfg.reset.narrow.enable:
#             # too_small_ang; done above
#             too_large_ang = env.soil.get_ssp_ang_to_soil()[rejected] > env.cfg.reset.narrow.max_ang_bucket_to_soil
#             # too_small_x; done above
#             too_small_x = env.m545_measurements.bucket_pos_w[rejected, 0:1] < env.cfg.reset.narrow.x_min
#             too_large_x = env.m545_measurements.bucket_pos_w[rejected, 0:1] > env.cfg.reset.narrow.x_max
#             too_small_z = env.m545_measurements.bucket_pos_w[rejected, 2:] < env.cfg.reset.narrow.z_min
#             too_large_z = env.m545_measurements.bucket_pos_w[rejected, 2:] > env.cfg.reset.narrow.z_max
#         else:
#             # set all to false with correct shape
#             too_large_ang = torch.zeros_like(too_close_x)
#             too_small_x = too_large_ang
#             too_large_x = too_large_ang
#             too_small_z = too_large_ang
#             too_large_z = too_large_ang

#         rejected_idx = torch.nonzero(
#             (
#                 too_close_x
#                 | too_high_z
#                 | too_low_z
#                 | too_small_ang
#                 | invalid_soil_model_state
#                 # | too_large_force
#                 # | too_large_moment
#                 | too_large_force_moment  # worse without
#                 | violating_max_depth
#                 | violating_torque_lower_limits  # worse without
#                 | violating_torque_upper_limits
#                 # narrow reset
#                 | too_large_ang
#                 | too_small_x
#                 | too_large_x
#                 | too_small_z
#                 | too_large_z
#             ),
#             as_tuple=True,
#         )[0]

#         rejected = rejected[rejected_idx]  # select which envs need another reset
#         rejected_sampling_idx_lower = rejected_sampling_idx_lower[rejected_idx]
#         rejected_sampling_idx_upper = rejected_sampling_idx_upper[rejected_idx]

#     # Write the new state
#     asset.write_root_pose_to_sim(
#         torch.cat(
#             (
#                 env.m545_measurements.root_pos_w[env_ids].clone() + env.scene.env_origins[env_ids].clone(),
#                 env.m545_measurements.root_quat_w[env_ids].clone(),
#             ),
#             dim=-1,
#         ),
#         env_ids=env_ids,
#     )
#     asset.write_root_velocity_to_sim(torch.zeros((env.num_envs, 6), device=env.device)[env_ids], env_ids=env_ids)

#     asset.write_joint_state_to_sim(
#         position=env.m545_measurements.joint_pos[env_ids].clone(),
#         velocity=env.m545_measurements.joint_vel[env_ids].clone(),
#         env_ids=env_ids,
#     )
#     #

#     # reset measurements used in obs (and logging)
#     env.actions[env_ids] = 0.0  # last action reset in step loop

#     # initial torque ob (soil + gravity)
#     ext_f_genco_tau = torch.matmul(
#         env.m545_measurements.bucket_jac_lin_T_dof[env_ids], env.soil.get_resultant_force()[env_ids].unsqueeze(-1)
#     )
#     ext_f_tau = ext_f_genco_tau.squeeze(-1)[:, -env.num_dofs :]

#     ext_m_genco_tau = torch.matmul(
#         env.m545_measurements.bucket_jac_rot_T_dof[env_ids], env.soil.get_resultant_moment()[env_ids].unsqueeze(-1)
#     )
#     ext_m_tau = ext_m_genco_tau.squeeze(-1)[:, -env.num_dofs :]

#     # inertial therm
#     # des_dof_acc = (torch.tensor([25,30,20,20], device=env.device)*env.clipped_scaled_actions[env_ids])
#     # inertial_therm = (env.m545_measurements.mm[env_ids].matmul(des_dof_acc.view(len(env_ids), 4, 1)).view(len(env_ids), 4))

#     env.torques[env_ids] = (
#         -ext_f_tau
#         - ext_m_tau
#         - env.m545_measurements.gravity_tau[env_ids]
#         + env.m545_measurements.root_physx_view.get_coriolis_and_centrifugal_forces()[env_ids]
#     )
#     env.ext_f_tau[env_ids] = 0.0
#     env.ext_m_tau[env_ids] = 0.0
#     env.inertial_tau[env_ids] = 0.0

#     env.last_fill_ratio[env_ids] = env.soil.fill_ratio()[env_ids]


def ik_reset(
    env: BaseEnv,
    env_ids: torch.Tensor,
    asset_cfg: SceneEntityCfg = SceneEntityCfg("robot"),
):
    # print("start reset_idx")
    """Reset selected environments.
    resetting flag for termination in  step? -> only needed if reset_stepping is enabled, not used in latest version, doublecheck
    Resetting
        - reset pid
        - reset soil/fee

    Soil resampling
        - if enabled: sample soil with height (excavator config is then relative to the soil)
        - if enabled: sample max_depth
        - if enabled: sample pullup distance
        - if enabled: sample soil_obstacles

        - if enabled: randomize orientation (pitch, turn?)
        - if enabled: sample arm config
            if in soil, sample random fill volume
        - set wheel support positions

    Arm Resampling
        - update measurements
        - update fee forces (check if in obstacle and change params accordingly)
        - reset again if forces too large


    Other
        Logs episode info
        Resets selected buffers

    Args:
        env_ids (list[int]): List of environment ids which must be reset
    """
    asset: RigidObject = env.scene[asset_cfg.name]

    if len(env_ids) == 0:
        return
    # env.pid.reset(idxs=env_ids)
    env.soil.reset(idxs=env_ids)
    env.termination_excavation.reset(idxs=env_ids)

    if env.cfg.reset.sample_soil:
        env.soil.sample(
            idxs=env_ids
        )  # height & params & max depth, random or fixed, depending on cfg

    if env.cfg.reset.sample_max_depth:
        # TODO, already in soil.sample, maybe take it out three
        pass
    # TODO: rethink this!
    if env.cfg.reset.sample_pullup_dist:
        x_min_capped = torch.clamp(
            env.m545_measurements.x_min[env_ids].squeeze(-1), max=3.0
        )
        env.pullup_dist[env_ids] = u_rand(
            len(env_ids),
            env.cfg.reset.pullup_dist_range[0] * x_min_capped,
            env.cfg.reset.pullup_dist_range[1] * x_min_capped,
            env.device,
        )
    else:
        env.pullup_dist[env_ids] = torch.tensor(
            env.cfg.reset.pullup_dist, device=env.device
        ).repeat(len(env_ids))
        # for debugging set all pullup dists to 3.5
        # env.pullup_dist[env_ids] = torch.tensor(3.5, device=env.device).repeat(len(env_ids))

        # if env.cfg.reset.sample_pullup_dist:
        # margin = env.cfg.reset.margin  # Assuming margin is defined in the configuration
        # margin = 1.0
        # min_val = env.m545_measurements.x_min[env_ids].squeeze(-1)
        # max_val = env.m545_measurements.x_max[env_ids].squeeze(-1) * 0.95 - margin - env.curriculum_excavation.curr_pullup_band

        # env.pullup_dist[env_ids] = helpers.sample_within_limits_skewed(
        #     min_val=min_val,
        #     max_val=max_val,
        #     num_samples=len(env_ids),
        #     alpha=env.cfg.reset.pullup_beta,  # Adjust alpha for skewing closer to the excavator
        #     beta=env.cfg.reset.pullup_alpha,  # Adjust beta for skewing closer to the excavator
        #     device=env.device,
        # ).squeeze(-1)  # Squeeze the last dimension to match the shape

    env.m545_measurements.joint_vel[env_ids] = 0.0  # DOF Vel
    env.m545_measurements.root_lin_vel_w[env_ids] = 0.0
    env.m545_measurements.root_ang_vel_w[env_ids] = 0.0
    env.m545_measurements.j_pitch_vel[env_ids] = 0.0
    env.m545_measurements.j_pitch_ang_vel[env_ids] = 0.0

    # rejection "sampling" using cached reset states
    # all envs to reset are rejected at the beginning
    rejected = env_ids.clone()

    bucket_distance_min = (
        env.pullup_dist[env_ids] + env.cfg.reset.init_distance_from_pullup / 2
    )
    factor = 0.95
    # bucket_distance_max = torch.tensor(env.cfg.reset.max_distance, device=env.device).repeat(len(env_ids))
    x = helpers.sample_within_limits_skewed(
        bucket_distance_min,
        env.m545_measurements.x_max[env_ids].squeeze(-1) * factor,
        len(env_ids),
        env.cfg.reset.x_alpha,
        env.cfg.reset.x_beta,
        device=env.device,
    )
    # set x to 7.0 for debugging
    # x = torch.tensor(7.0, device=env.device).repeat(len(env_ids))

    # used to convert form [N] to [N, 1]
    if x.dim() == 1:
        x = x.unsqueeze(-1)
    if x.dim() == 1:
        x = x.unsqueeze(0)
    # print("sampled x: ", x)
    # print("shape x  ", x.shape)
    soil_height = env.soil.get_soil_height_at_pos(x, rejected)
    # height between soil height and cfg band, TODO: parallelize soil height limits and pullup band
    # limits are already in world frame (ie. base frame but on the ground)
    z_min = (
        helpers.compute_poly(
            env.m545_measurements.z_min_coeff[env_ids],
            x,
            degree=env.m545_measurements.z_degree,
        )
    ).unsqueeze(-1)
    z_min = z_min
    z_max = (
        helpers.compute_poly(
            env.m545_measurements.z_max_coeff[env_ids],
            x,
            degree=env.m545_measurements.z_degree,
        )
    ).unsqueeze(-1)
    z_max = z_max
    # This transform the z limits in the base frame
    debug = False
    if debug:
        print("Debug: z_min:", z_min)
        print("Debug: z_max:", z_max)
    lower_z_pos = soil_height + env.cfg.reset.height_band
    upper_z_pos = soil_height + env.cfg.reset.arm_depth_limits.upper
    if debug:
        print("Debug: lower_z_pos:", lower_z_pos)
        print("Debug: upper_z_pos:", upper_z_pos)
    # Clamping
    # Clamping using torch.clamp
    lower_z_pos_clamped = torch.clamp(lower_z_pos, min=z_min, max=z_max)
    upper_z_pos_clamped = torch.clamp(upper_z_pos, min=z_min, max=z_max)
    if debug:
        print("Debug: lower_z_pos_clamped:", lower_z_pos_clamped)
        print("Debug: upper_z_pos_clamped:", upper_z_pos_clamped)
    z = helpers.sample_within_limits(
        lower_z_pos_clamped, upper_z_pos_clamped, len(env_ids), device=env.device
    )
    # x = torch.tensor([5.64], device=env.device).unsqueeze(0)
    # z = torch.tensor([-1.379], device=env.device).unsqueeze(0)
    # print("x and z ", x, z)
    if debug:
        print("Debug: Sampled z values:", z)
        print("depth manual ", z - soil_height)

    alpha_min = helpers.compute_poly(
        env.m545_measurements.alpha_min_coeff[env_ids],
        torch.hstack((x, z)),
        degree=env.m545_measurements.alpha_degree,
    )
    alpha_max = helpers.compute_poly(
        env.m545_measurements.alpha_max_coeff[env_ids],
        torch.hstack((x, z)),
        degree=env.m545_measurements.alpha_degree,
    )
    # print("alpha min", alpha_min)
    # print("alpha max", alpha_max)
    # TODO: there is a bug here, alpha is really strange.
    alpha_min = torch.clip(alpha_min, 0.2, 0.9)
    alpha_max = torch.clip(alpha_max, 0.2, 0.9)
    # set alpha to 0.5
    alpha = helpers.sample_within_limits_skewed(
        alpha_min,
        alpha_max,
        len(env_ids),
        env.cfg.reset.alpha_alpha,
        env.cfg.reset.alpha_beta,
        device=env.device,
    )
    # randomly sample alpha from 0.2 to 0.9
    alpha = torch.rand(len(env_ids), 1, device=env.device) * (0.9 - 0.5) + 0.5
    # alpha = torch.tensor(0.5, device=env.device).repeat(len(env_ids), 1)
    # alpha = torch.tensor(1.8, device=env.device).repeat(len(env_ids), 1)
    # print sampled x, z and alpha
    # print("sampled x", x)
    # print("sampled z", z)
    # print("sampled alpha", alpha)
    # overwrite the x, z, alpha for comparison

    # alpha = torch.tensor([1], device=env.device).unsqueeze(0)
    # print the shape of env_ids
    # print("env_ids", env_ids.shape)
    # print("w_P_wb", env.m545_measurements.w_P_wb.shape)
    # print("b_P_bbm", env.m545_measurements.b_P_bbm.shape)
    w_P_wboom = env.m545_measurements.w_P_wb + env.m545_measurements.b_P_bbm
    # w_P_wboom = (
    #     torch.tensor(env.cfg.reset.pos, device=env.device) + env.m545_measurements.b_P_bbm
    # )
    # print("w_P_wboom", w_P_wboom.shape)
    boom_ee_pose = torch.zeros((len(env_ids), 3), device=env.device)
    boom_ee_pose[:, 0] = x.squeeze(-1) - w_P_wboom[env_ids, 0]
    # assume that the world frame is aligned with the arm
    boom_ee_pose[:, 1] = z.squeeze(-1) - w_P_wboom[env_ids, 2]
    # ignore base pitch angle for the momement
    boom_ee_pose[:, 2] = (
        alpha.squeeze(-1) - env.m545_measurements.link_rotations[env_ids, -1, 1]
    )

    links = env.m545_measurements.links_arm[env_ids]
    offsets = env.m545_measurements.link_rotations[env_ids]
    q1, q2, q3 = helpers.inverse_kinematics_analytic_fixed_links_with_offsets(
        boom_ee_pose[:, 0:1], boom_ee_pose[:, 1:2], boom_ee_pose[:, 2:3], links, offsets
    )
    # print("q1 q2 q3 ", q1, q2, q3)
    # q1 = torch.tensor([-0.6275], device=env.device).unsqueeze(0)
    # q2 = torch.tensor([1.844], device=env.device).unsqueeze(0)
    # q3 = torch.tensor([-0.462], device=env.device).unsqueeze(0)
    # check that requested joint angles are within limits
    dof_limits = env.m545_asset.root_physx_view.get_dof_limits().to(env.device)

    # Bind joint angles within their limits using torch.clamp
    limit_scale = 1.00
    # q1_clamped = torch.clamp(
    #     q1, dof_limits[env_ids, 0, 0].unsqueeze(1) * limit_scale, dof_limits[env_ids, 0, 1].unsqueeze(1) * limit_scale
    # )
    # q2_clamped = torch.clamp(
    #     q2, dof_limits[env_ids, 1, 0].unsqueeze(1) * limit_scale, dof_limits[env_ids, 1, 1].unsqueeze(1) * limit_scale
    # )
    # q3_clamped = torch.clamp(
    #     q3, dof_limits[env_ids, 2, 0].unsqueeze(1) * limit_scale, dof_limits[env_ids, 2, 1].unsqueeze(1) * limit_scale
    # )

    # # Check and print values outside the limits
    outside_limits_q1 = (q1 < dof_limits[env_ids, 0, 0].unsqueeze(1)) | (
        q1 > dof_limits[env_ids, 0, 1].unsqueeze(1)
    )
    outside_limits_q2 = (q2 < dof_limits[env_ids, 1, 0].unsqueeze(1)) | (
        q2 > dof_limits[env_ids, 1, 1].unsqueeze(1)
    )
    outside_limits_q3 = (q3 < dof_limits[env_ids, 2, 0].unsqueeze(1)) | (
        q3 > dof_limits[env_ids, 2, 1].unsqueeze(1)
    )

    # if torch.any(outside_limits_q1):
    #     print(f"q1 outside limits: {q1[outside_limits_q1]}")
    # if torch.any(outside_limits_q2):
    #     print(f"q2 outside limits: {q2[outside_limits_q2]}")
    # if torch.any(outside_limits_q3):
    #     print(f"q3 outside limits: {q3[outside_limits_q3]}")

    # q1 = q1_clamped
    # q2 = q2_clamped
    # q3 = q3_clamped

    batch_size = q1.shape[0]
    # Ensure all tensors have the same number of dimensions before stacking
    joint_angles = torch.hstack(
        [
            q1,
            q2,
            q3,
        ]
    )
    _, ee_pose_fk = helpers.forward_kinematics_2D_batch_with_offsets(
        links, joint_angles, offsets
    )  # correct
    # convert back to x, z, alpha to account for the clamping
    x = ee_pose_fk[:, 0].unsqueeze(-1) + w_P_wboom[env_ids, 0].unsqueeze(-1)
    z = ee_pose_fk[:, 1].unsqueeze(-1) + w_P_wboom[env_ids, 2].unsqueeze(-1)
    alpha = ee_pose_fk[:, 2].unsqueeze(-1)
    # Compute element-wise closeness between verification and re-computed ee poses
    closeness = torch.isclose(ee_pose_fk, boom_ee_pose, atol=1e-3)
    # print both poses
    # make an assertion to check if all the elements are close to each other
    # assert torch.all(closeness), "Forward kinematics failed to match the expected result."
    env.m545_measurements.joint_pos[env_ids, 0] = q1.squeeze(-1)
    env.m545_measurements.joint_pos[env_ids, 1] = q2.squeeze(-1)
    env.m545_measurements.joint_pos[env_ids, 2] = q3.squeeze(-1)
    # env.m545_measurements.joint_pos[env_ids, 0] = -0.86
    # env.m545_measurements.joint_pos[env_ids, 1] = 2.03
    # env.m545_measurements.joint_pos[env_ids, 2] = 0.46

    # TODO: replace with init config of the robot
    # pos = [0, 0, 0.747]
    env.m545_measurements.root_pos_w[env_ids] = env.m545_measurements.w_P_wb[env_ids]
    env.m545_measurements.root_quat_w[env_ids] = env.m545_measurements.R_wb[env_ids]
    # update bucket state
    # TODO: m545 dependent, this should rather be base frame, if i clamp i should recompute
    env.m545_measurements.bucket_pos_w[env_ids, 0] = x.squeeze()
    env.m545_measurements.bucket_pos_w[env_ids, 1] = w_P_wboom[env_ids, 1]
    env.m545_measurements.bucket_pos_w[env_ids, 2] = z.squeeze()
    env.m545_measurements.prev_bucket_pos_w[env_ids] = (
        env.m545_measurements.bucket_pos_w[env_ids].clone()
    )
    env.soil.update_bucket_state(env_ids)

    # Write the new state
    asset.write_root_pose_to_sim(
        torch.cat(
            (
                env.m545_measurements.root_pos_w[env_ids].clone()
                + env.scene.env_origins[env_ids].clone(),
                env.m545_measurements.root_quat_w[env_ids].clone(),
            ),
            dim=-1,
        ),
        env_ids=env_ids,
    )
    asset.write_root_velocity_to_sim(
        torch.zeros((env.num_envs, 6), device=env.device)[env_ids], env_ids=env_ids
    )

    asset.write_joint_state_to_sim(
        position=env.m545_measurements.joint_pos[env_ids].clone(),
        velocity=env.m545_measurements.joint_vel[env_ids].clone(),
        env_ids=env_ids,
    )
    # reset measurements used in obs (and logging)
    env.actions[env_ids] = 0.0  # last action reset in step loop

    env.torques[env_ids] = -env.m545_measurements.gravity_tau[env_ids]
    env.ext_f_tau[env_ids] = 0.0
    env.ext_m_tau[env_ids] = 0.0
    env.inertial_tau[env_ids] = 0.0
    # env.last_fill_ratio[env_ids] = env.soil.get_fill_ratio()[env_ids]

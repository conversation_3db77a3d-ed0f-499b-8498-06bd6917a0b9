# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
import torch
from typing import TYPE_CHECKING

import isaacsim.core.utils.torch as torch_utils
import warnings

from isaaclab.assets import Articulation, RigidObject
from isaaclab.managers import SceneEntityCfg


DEBUG = True  # Set this to True to enable NaN checks


def check_for_nans(tensor, function_name):
    if DEBUG:
        if torch.isnan(tensor).any():
            warnings.warn(f"NaN values detected in the output of {function_name}")
        if torch.isinf(tensor).any():
            warnings.warn(f"Infinity values detected in the output of {function_name}")
        if torch.all(tensor == 0):
            warnings.warn(f"All zero values detected in the output of {function_name}")
    return tensor


def dof_pos(env, asset_cfg) -> torch.Tensor:
    asset: RigidObject = env.scene[asset_cfg.name]
    return check_for_nans(asset.data.joint_pos, "dof_pos")


def dof_vel(env, asset_cfg) -> torch.Tensor:
    asset: RigidObject = env.scene[asset_cfg.name]
    return check_for_nans(asset.data.joint_vel, "dof_vel")


def dof_tau(env) -> torch.Tensor:
    return check_for_nans(env.torques, "dof_tau")


def dof_tau_normalized(env) -> torch.Tensor:
    tau_normalized = (env.torques - env.m545_measurements.tau_min) / (env.m545_measurements.tau_max - env.m545_measurements.tau_min)
    # print("tau ", env.torques)
    # print("tau_min ", env.m545_measurements.tau_min)
    # print("tau_max ", env.m545_measurements.tau_max)
    # print("joint_pos ", env.m545_measurements.joint_pos)
    return check_for_nans(tau_normalized, "dof_tau_normalized")


def last_action_excavation(env) -> torch.Tensor:
    return check_for_nans(env.actions, "last_action_excavation")


def bucket_pos_gac(env) -> torch.Tensor:
    # this does not work if base frame is not at 0
    return check_for_nans(env.m545_measurements.bucket_pos_w[:, [0, 2]], "bucket_pos_gac")


def bucket_vel_gac(env) -> torch.Tensor:
    """
    Returns the bucket velocity with added Gaussian noise (±10%).
    """
    base_vel = env.m545_measurements.bucket_vel_w[:, [0, 2]]
    noise = torch.randn_like(base_vel) * 0.0 * env.cfg.terminations_excavation.max_bucket_vel * 0  # 10% of the velocity range [-1.0, 1.0]
    noisy_vel = base_vel + noise
    # set the norm of the noisy velocity to the original norm
    env.m545_measurements.bucket_vel_noisy_norm = torch.norm(noisy_vel, dim=-1)
    return check_for_nans(noisy_vel, "bucket_vel_gac")


def bucket_ang_gac(env) -> torch.Tensor:
    """
    Angle wrt to the base frame and it's angular velocity
    """
    # print 
    # print("bucket_ang_gac", env.m545_measurements.bucket_ang_gac)
    # print("j_pitch_ang_vel", env.m545_measurements.j_pitch_ang_vel)
    return torch.cat(
        (env.m545_measurements.bucket_ang_gac.unsqueeze(-1), env.m545_measurements.j_pitch_ang_vel[:, 1:2]), dim=-1
    )


def bucket_lin_vel_norm(env) -> torch.Tensor:
    return check_for_nans(env.m545_measurements.bucket_vel_noisy_norm.unsqueeze(-1), "bucket_lin_vel_norm")


def base_pitch_gac(env) -> torch.Tensor:
    return check_for_nans(torch.cat(
        (0 * env.m545_measurements.base_pitch_w.unsqueeze(-1), 0 * env.m545_measurements.root_ang_vel_w[:, 1:2]), dim=-1
    ), "base_pitch_gac")  # /sigma


def fill_ratio(env) -> torch.Tensor:
    return check_for_nans(env.soil.fill_ratio, "fill_ratio")


def bucket_aoa(env) -> torch.Tensor:
    return check_for_nans(env.m545_measurements.bucket_aoa.unsqueeze(-1), "bucket_aoa")


def soil_height(env) -> torch.Tensor:
    return check_for_nans(env.soil_height_futures, "soil_height")


def bucket_depth(env) -> torch.Tensor:
    # print("**** bucket_depth ****")
    # print("bucket_depth", env.soil.bucket_depth)
    # # print the bucket pos
    # print("bucket_pos", env.m545_measurements.bucket_pos_w)
    return check_for_nans(env.soil.bucket_depth, "bucket_depth")


def pitch_vel(env) -> torch.Tensor:
    return check_for_nans(env.m545_measurements.j_pitch_vel[:, [0, 2]], "pitch_vel")


def max_depth(env) -> torch.Tensor:
    return check_for_nans(env.max_depth_futures, "max_depth")


def pullup_dist(env) -> torch.Tensor:
    return check_for_nans(env.pullup_dist.unsqueeze(-1), "pullup_dist")


def soil_parameters(env) -> torch.Tensor:
    res_non_scaled = env.soil.get_soil_params()[:, 0 : env.soil.get_n_soil_params_to_sample()]
    return check_for_nans(res_non_scaled, "soil_parameters")


def bucket_dim(env) -> torch.Tensor:
    return check_for_nans(env.m545_measurements.bucket_dims[:, 1].unsqueeze(-1), "bucket_dim")


def base_mass(env) -> torch.Tensor:
    return check_for_nans(env.m545_measurements.base_masses.unsqueeze(-1), "base_mass")


def arm_scale(env) -> torch.Tensor:
    return check_for_nans(env.m545_measurements.arm_scale, "arm_scale")


def base_friction_torque_limits(env) -> torch.Tensor:
    # TODO: check how friction differs from isaac gym
    friction_force = torch.zeros((env.m545_measurements.base_masses.shape[0], 3), device=env.device)
    friction_force[:, 0] = - env.m545_measurements.base_masses * 9.81 * env.cfg.scene.plane.spawn.physics_material.static_friction
    friction_tau = torch.matmul(
            env.m545_measurements.bucket_jac_lin_T_dof,
            friction_force.unsqueeze(-1),
        ).squeeze(-1)
    # normalize the friction
    friction_tau = (friction_tau - env.m545_measurements.tau_min) / (env.m545_measurements.tau_max - env.m545_measurements.tau_min)
    env.m545_measurements.friction_tau = friction_tau
    return check_for_nans(friction_tau, "base_friction_torque_limits")


def base_pullup_torque_limits(env) -> torch.Tensor:
    bucket_pos_w_x = env.m545_measurements.bucket_pos_w[:, 0].unsqueeze(-1)
    
    # Check if bucket_pos_w_x is not initialized (full of zeros)
    if torch.all(bucket_pos_w_x == 0):
        return check_for_nans(torch.ones_like(env.m545_measurements.tau_max), "base_pullup_torque_limits")

    limit_force = torch.zeros((env.m545_measurements.base_masses.shape[0], 3), device=env.device)
    limit_force[:, 2] = -(env.m545_measurements.base_length / (2 * bucket_pos_w_x) * env.m545_measurements.base_masses.unsqueeze(-1) * 9.81).squeeze()
    limit_tau = torch.matmul(
            env.m545_measurements.bucket_jac_lin_T_dof,
            limit_force.unsqueeze(-1),
        ).squeeze(-1)
    # normalize the limits 
    limit_tau = (limit_tau - env.m545_measurements.tau_min) / (env.m545_measurements.tau_max - env.m545_measurements.tau_min)
    return check_for_nans(limit_tau, "base_pullup_torque_limits")


def soil_forces_normalized(env) -> torch.Tensor:
    # should be in base frame 
    soil_forces_w = env.soil.forces.RF_w
    # print("soil forces w", soil_forces_w)
    soil_forces_normalized = torch.zeros_like(soil_forces_w)
    soil_forces_normalized[:, 0] = - soil_forces_w[:, 0] / env.m545_measurements.force_limits[:, 0]
    soil_forces_normalized[:, 2] = - soil_forces_w[:, 2] / env.m545_measurements.force_limits[:, 2]
    # print("soil forces normalized", soil_forces_normalized)
    return check_for_nans(soil_forces_normalized, "soil_forces_normalized")


def target_fill_ratio(env) -> torch.Tensor:
    return check_for_nans(env.curriculum_excavation.curr_term_fill_ratio.repeat(env.num_envs, 1), "target_fill_ratio")
# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

from moleworks_ext.tasks.ugep.excavation_utils.general_excavator import (
    GENERAL_EX_CFG,
    M545ReferenceValues,
)
from moleworks_ext.common.env_cfg.general_env_cfg import transparent_plane

import isaaclab.sim as sim_utils
from isaaclab.assets import AssetBaseCfg, RigidObject
from isaaclab.envs import ManagerBasedRLEnvCfg
from isaaclab.managers import ObservationGroupCfg as ObsGroup
from moleworks_ext.common.managers.observations.obs_with_mean import (
    ObservationWithMeanTermCfg as ObsTerm,
)
from isaaclab.managers import EventTermCfg as EventTerm
from isaaclab.managers import RewardTermCfg as RewTerm
from isaaclab.managers import SceneEntityCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sensors import ContactSensorCfg
from isaaclab.sim.spawners.from_files.from_files_cfg import GroundPlaneCfg
from isaaclab.utils import configclass
from isaaclab.utils.noise.noise_cfg import GaussianNoiseCfg
from moleworks_ext.common.actions.actions_cfg import InverseDynamicsActionCfg
from moleworks_ext.common.controllers.inverse_dynamics import (
    InverseDynamicsControllerCfg,
)
from moleworks_ext.common.sim.spawners.from_files.from_files_cfg import GroundPlaneWithVisualMaterial

import moleworks_ext.tasks.ugep.mdp as mdp

from moleworks_ext.common.env_cfg.general_env_cfg import os1_lidar, raycaster

@configclass
class ExcavationEnvCfg(ManagerBasedRLEnvCfg):
    """Configuration for a reinforcement learning environment for excavation."""

    # PID Tuning
    k_d = [25, 30, 20]
    # k_d = [40, 40, 40]
    # k_d = [100, 100, 100]

    # Timeout
    send_timeouts = True

    @configclass
    class Limits:
        action_scaling = (1.0, 1.0, 1.0)  # (0.1, 0.1, 0.1)
        infinite_torque = False

        @configclass
        class Velocity:
            upper = [0.3, 0.6, 0.8]
            lower = [-0.3, -0.6, -0.8]

        velocity = Velocity()

        @configclass
        class Position:
            upper = [0.4, 2.74, 2.2]
            lower = [-1.20, 0.55, -0.59]

        # TODO: measure true limits on the machine, change also in URDF!!!
        # from excavator model
        # pos lim upper:      0.400769      2.76691          1.8       2.3289
        # pos lim lower:      -1.29665      0.543349             0      -0.59714

        position = Position()

        # from m545_description/m545_cylinder_definitions.hpp
        @configclass
        class CylinderForce:
            upper = [530100.0, 398100.0, 235500.0]
            lower = [-379180.53, -282611.19, -150908.4]

        cylinder_force = CylinderForce()

        @configclass
        class CylinderVelocity:
            upper = [0.118, 0.308, 0.334]
            lower = [-0.132, -0.295, -0.33]

        cylinder_velocity = CylinderVelocity()

    limits = Limits()

    reference_values = M545ReferenceValues

    @configclass
    class Reset:
        sample_soil = True  # True
        sample_max_depth = False  # not needed, sampling happens in sample soil
        sample_pullup_dist = True  # True
        # too close can cause the system to go outside of the joint limits
        pullup_alpha = 1  # 5
        pullup_beta = 2  # 1
        x_alpha = 3
        x_beta = 1
        # pullup factors wrt to the min distance
        pullup_dist_range = [1.0, 1.2]
        pullup_dist = 1.0
        sample_obstacles = False
        fixed_config = False  # False or any of the below (str)
        only_above_soil = True
        attitude_limits = [0.0, 1.5]
        alpha_alpha = 1
        alpha_beta = 4
        height_band = 0.2  # m
        # degree of polinomials used to model the limits
        z_degree = 2
        alpha_degree = 5
        init_distance_from_pullup = 0.5

        # init pos
        # TODO: this maybe it's not needed
        # pos = [0, 0, 0.77]
        # pos = [0.0, 0.0, 0.001]
        # quat = [1.000, 0.0078, 0.0031, 0.0018]
        # reference x_max
        # x_max = 7.5 # for scale 1 menzi muck
        # quat = [1.0]
        # reduced available reset workspace
        x_min_factor = 1.1
        x_max_factor = 0.90

        @configclass
        class Configs:
            # close = {"idx": 184599, "height": -0.31, "height_idx": 37}
            # medium = {"idx": 140603, "height": -0.92, "height_idx": 61}
            # far = {"idx": 191851, "height": -0.2, "height_idx": 76}
            # medium_flat = {"idx": 194844, "height": -0.15, "height_idx": 58}
            # far_flat = {"idx": 164420, "height": -0.58, "height_idx": 57}
            # `close`_flat = {"idx": 36197, "height": -2.57, "height_idx": 40}
            # medium_deep = {"idx": 56064, "height": -2.2, "height_idx": 59}
            close = {"idx": 139745, "height": -0.9422, "height_idx": 45}
            medium = {"idx": 182001, "height": -0.3394, "height_idx": 61}
            far = {"idx": 218501, "height": 0.2043, "height_idx": 79}
            # medium_flat = {"idx": 194844, "height": -0.15, "height_idx": 58}
            # far_flat = {"idx": 164420, "height": -0.58, "height_idx": 57}
            # close_flat = {"idx": 36197, "height": -2.57, "height_idx": 40}
            # medium_deep = {"idx": 56064, "height": -2.2, "height_idx": 59}

        configs = Configs()

        @configclass
        class Arm_depth_limtis:  # depth > 0: in soil, depth < 0: above soil
            lower = -0.4
            upper = 0.4

        arm_depth_limits = Arm_depth_limtis()

        min_ang_bucket_to_soil = (
            0  # negative: pushing with bottom plate; 0: parallel to soil
        )
        max_soil_force = 40000.0
        max_soil_moment = 40000.0
        max_soil_force_and_moment = 40000.0

        @configclass
        class Narrow:
            # additional reset criteria checked for testing/trenching
            enable = False
            max_ang_bucket_to_soil = (
                100  # effectively no limit, only used for deployment
            )
            x_min = 0
            x_max = 100
            z_min = -10
            z_max = 100

        narrow = Narrow()

    reset = Reset()

    class Soil_height:
        type = "rbf"  # "rbf" or "slope"
        x_min = 0  # m, in world frame
        # TODO: fails with 10
        x_max = 10  # m, in world frame
        z_min = -1.8  # -1.1  # m, from the ground
        z_max = 0.5  # 0.5  # m, up from the ground
        # rbf params
        min_resolution = 0.1
        theta = 0.5
        scale = 0.2
        # slope params, start=closer to excavator
        slope_start_height = -0.5
        slope_start = 4  # [m] from excavator
        slope_ang = (-30) * 3.14 / 180  # TODO: Use deg2grad fct
        slope_x_len = 3  # [m]

        # if value: average is that value,
        # if none and upper limit: sampled between min and upper_limit.offset
        # if none and no upper limits: sampled between min and max
        # if dict [height, idx]: sets height at pos[idx]
        offset = None  # {"height": -0.5, "idx": -1}

    soil_height = Soil_height()

    @configclass
    class Soil_forces:
        # for debugging
        fee_multiplyer = 1.0
        penetration_edge_multiplyer = 1.0
        penetration_plate_multiplyer = 1.0
        deadload_multiplyer = 1.0

    soil_forces = Soil_forces()

    @configclass
    class Max_depth_height:
        type = "rbf"
        x_min = 0  # m, in world frame
        x_max = 10  # m, in world frame
        # it was -3 in gym, but w_P_wb was also 0.7 -> -2.3
        z_min = -2.3  # m, in world frame
        z_max = 0.5  # m, in world frame
        # rbf params
        min_resolution = 0.1
        theta = 0.5
        scale = 0.2
        # slope params, start=closer to excavator
        slope_start_height = -0.5
        slope_start = 4  # [m] from excavator
        slope_ang = (-30) * 3.14 / 180  # TODO: Replace by deg2grad
        slope_x_len = 3  # [m]

        offset = None  # if value: average is that value, if none: sampled between min and max
        clip_margin = 0.05  # margin, if upper_limit clipped

    max_depth_height = Max_depth_height()

    bucket = reference_values.bucket

    @configclass
    class Ssp:
        # highest power first, scalar end, alpha = f^-1(A) ~ poly(A)
        ssp_angle_poly_coeffs = [
            -6.56853263e02,
            9.55497849e02,
            -5.49159120e02,
            1.63194692e02,
            -2.76530025e01,
            4.64763769e00,
            4.98590828e-03,
        ]
        dL_max = 0.01  # ssp discretization for finding intersection with soil
        L_over_max = 1.1  # on this length, we check if ssp exits soil

    ssp = Ssp()

    @configclass
    class Soil_parameters:
        type = "random"  # "S_0_0"  # random or specific type

        @configclass
        class S_0_0:
            c = 0  # [0-105 kPa] soil cohesion [Pa]
            ca_f = 0  # [0-100 %] soil adhesion fraction of cohesion
            phi = 0.55  # soil internal friction angle [17-45°]
            gamma = 19500  # soil unit weight = density * g [N/m^3] 17-22kN/m^3
            delta = 0.4  # [11°-22°] = [0.19 - 0.38] soil - metal friction angle
            alpha = 0  # soil surface inclination
            CP = 1  # factor lumping cavity expansion model - limit pressure p = f*p0 [50-350]

        s_0_0 = S_0_0()

        @configclass
        class S_0_1:
            c = 0
            ca_f = 0
            phi = 0.77
            gamma = 21500
            delta = 0.4
            alpha = 0
            CP = 300

        s_0_1 = S_0_1()

        @configclass
        class S_1_0:
            c = 20000
            ca_f = 0.5
            phi = 0.47
            gamma = 18000
            delta = 0.3
            alpha = 0
            CP = 1

        s_1_0 = S_1_0()

        @configclass
        class S_1_1:
            c = 20000
            ca_f = 0.5
            phi = 0.59
            gamma = 18000
            delta = 0.3
            alpha = 0
            CP = 300

        s_1_1 = S_1_1()

        @configclass
        class S_2_0:
            c = 60000
            ca_f = 0.5
            phi = 0.31
            gamma = 18000
            delta = 0.4
            alpha = 0
            CP = 1

        s_2_0 = S_2_0()

        @configclass
        class S_2_1:
            c = 60000
            ca_f = 0.5
            phi = 0.56
            gamma = 21000
            delta = 0.4
            alpha = 0
            CP = 300

        s_2_1 = S_2_1()

        @configclass
        class S_3_0:
            c = 105000
            ca_f = 0.5
            phi = 0.31
            gamma = 18000
            delta = 0.4
            alpha = 0
            CP = 1

        s_3_0 = S_3_0()

        @configclass
        class S_3_1:
            c = 105000
            ca_f = 0.5
            phi = 0.56
            gamma = 21000
            delta = 0.4
            alpha = 0
            CP = 300

        s_3_1 = S_3_1()

    soil_parameters = Soil_parameters()

    @configclass
    class Curriculum_Utils:
        exp_f = 0.01
        start_curl_fill_ratio = 0.05  # 0.4
        end_curl_fill_ratio = 0.9
        start_term_fill_ratio = 0.1  # 0.5
        end_term_fill_ratio = 0.98
        start_height_above_soil = 0.0
        end_height_above_soil = 0.9
        start_pullup_band = 2.0  # [m] band to allow for positive termination (furter away than pullup dist)
        end_pullup_band = 0.5  # [m] band to allow for positive termination (furter away than pullup dist)
        start_spilling_depth_margin = 0.05
        end_spilling_depth_margin = 0.05
        rbf_theta_cf = 1

    curriculum_utils = Curriculum_Utils()

    @configclass
    class Terminations_Excavation:
        @configclass
        class Negative_terminations:
            """
            The order matters, because the negative terminations are checked in order.
            Only one termination condition is active at the same time.
            """

            torque_limits = True
            bucket_aoa = True
            bucket_vel = True
            base_vel = True
            limit_forces = False
            joint_vel = True
            bucket_height = True
            invalid_soil_model = True
            self_collision = False
            max_depth = True
            pullup = True
            spilling_soil = False
            stuck = True

        negative_terminations = Negative_terminations()

        @configclass
        class Positive_terminations:
            desired_close = 2.0  # 3.7
            desired_full = 9.0  # 28
            desired_partial = 0.0

        positive_terminations = Positive_terminations()

        # config
        disable_negative_termination = False
        neg_term_rew = -1.0  # -0.9

        # other params
        max_bucket_vel = 0.4  # m/s
        termination_bucket_vel = 2.0  # 1.8 m/s
        bucket_vel_aoa_threshold = 0.10  # m/s
        bucket_vel_spillig_threshold = 0.075  # m/s
        max_base_vel = 0.1  # 0.11  Fo# m/s, lin vel
        max_joint_vel = 1
        max_bucket_height_above_soil = 1.0
        term_above_soil_fill_ratio = 0.05
        max_depth_overshoot = (
            0.05  # terminate if depth > max_depth + max_depth_overshoot
        )
        # this is different from pascals remember for deployment
        max_curl_ang = 0.1  # want smaller than this = more curled
        min_curl_ang = -0.2  # want larger than this = less curled, no overcurling!!
        max_curl_ang_gac = -0.9
        min_curl_ang_gac = -1.2

    terminations_excavation = Terminations_Excavation()

    @configclass
    class Observations_Excavation:
        num_soil_height_futures = 5  # current max depth + future points
        soil_height_futures_spacing = 0.2  # m
        num_max_depth_futures = 5  # current max depth + future points
        max_depth_futures_spacing = 0.2  # m
        num_soil_normal_futures = 0  # current max depth + future points
        max_soil_normal_futures_spacing = 0.5  # m

        @configclass
        class Enable:
            soil_normals = False  # TODO: add to obs? infer from soil heights?
            max_depth = True
            pullup_dist = True
            soil_parameters = False
            torques = True

        enable = Enable()

    observations_excavation = Observations_Excavation()

    @configclass
    class Rewards_Excavation:
        # config
        # if true negative total rewards are clipped at zero (avoids early termination problems)
        only_positive_rewards = False
        print_debug = False

        # exponential factors
        max_depth_tracking_sigma = 0.005  # 0.01 tracking reward = exp(-error^2/sigma)
        max_depth_tracking_offset = (
            0.0  # get full reward for tracking max_depth + max_depth_tracking_offset
        )
        # other params
        go_down_fill_ratio = 0.01  # if less than this, encourage to go down

    rewards_excavation = Rewards_Excavation()

    # logging
    log_asset_specific_lengths = True


@configclass
class MySceneCfg(InteractiveSceneCfg):
    """Configuration for the terrain scene with a humanoid robot."""

    plane = AssetBaseCfg(
        prim_path="/World/ground",
        init_state=AssetBaseCfg.InitialStateCfg(pos=[0, 0, 0]),  # Asset spawned at (0,0,0)
        spawn=GroundPlaneWithVisualMaterial(
            physics_material=sim_utils.RigidBodyMaterialCfg(
                friction_combine_mode="average",
                restitution_combine_mode="multiply",
                static_friction=0.8,  # 0.8, # should be 0.8
                dynamic_friction=0.8,  # 0.8,
                restitution=0.8,
            ),
            visual_material=sim_utils.GlassMdlCfg(glass_ior=1.0003),
        ),
    )


    # robot = M545_DOF_ARM_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")
    robot = GENERAL_EX_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")

    # sensors
    contact_forces = ContactSensorCfg(prim_path="{ENV_REGEX_NS}/Robot/.*")

    # lights
    light = AssetBaseCfg(
        prim_path="/World/light",
        spawn=sim_utils.DistantLightCfg(color=(0.75, 0.75, 0.75), intensity=3000.0),
    )

    sky_light = AssetBaseCfg(
        prim_path="/World/skyLight",
        spawn=sim_utils.DomeLightCfg(color=(0.13, 0.13, 0.13), intensity=1000.0),
    )

    # TODO: this gives an error!
    # # Frame Transformer with Debug Visualization
    # frame_transformer = FrameTransformerCfg(
    #     prim_path="{ENV_REGEX_NS}/Robot/BASE",
    #     target_frames=[
    #         FrameTransformerCfg.FrameCfg(
    #             name="BASE_USER",
    #             prim_path="{ENV_REGEX_NS}/Robot/BASE",
    #             offset=OffsetCfg(pos=[0, 0, 0], rot=[1, 0, 0, 0]),
    #         ),
    #         FrameTransformerCfg.FrameCfg(
    #             name="BOOM_USER",
    #             prim_path="{ENV_REGEX_NS}/Robot/BOOM",
    #             offset=OffsetCfg(pos=[0, 0, 0], rot=[1, 0, 0, 0]),
    #         ),
    #         FrameTransformerCfg.FrameCfg(
    #             name="DIPPER_USER",
    #             prim_path="{ENV_REGEX_NS}/Robot/DIPPER",
    #             offset=OffsetCfg(pos=[0, 0, 0], rot=[1, 0, 0, 0]),
    #         ),
    #         FrameTransformerCfg.FrameCfg(
    #             name="SHOVEL_USER",
    #             prim_path="{ENV_REGEX_NS}/Robot/SHOVEL",
    #             offset=OffsetCfg(pos=[0, 0, 0], rot=[1, 0, 0, 0]),
    #         ),
    #     ],
    #     debug_vis=True
    # )


@configclass
class CommandsCfg:
    """Command specifications for the MDP."""

    command = (
        mdp.NullCommandCfg()
    )  # No command is used for M545, specyfying it is needed


@configclass
class ActionsCfg:
    """Action specifications for the MDP."""

    inv_dyn_cfg = InverseDynamicsActionCfg(
        asset_name="robot",
        joint_names=["J_BOOM", "J_STICK", "J_EE_PITCH"],
        controller_cfg=InverseDynamicsControllerCfg(
            command_type="vel",
            k_p=[0, 0, 0],
            k_d=[25, 30, 20],
            dof_limits=[
                [-0.5, 0.5],
                [-0.6, 0.6],
                # [-0.4, 0.4],
                [-0.8, 0.8],
            ],
            dof_efforts_limits=[
                [-2e6, 2e6],
                [-1e6, 1e6],
                # [-1e6, 1e6],
                [-1e6, 1e6],
            ],
        ),
    )


@configclass
class RewardsCfg:
    """Reward terms for the MDP."""

    bucket_velocity_penalty = RewTerm(
        func=mdp.reward_bucket_velocity_penalty, weight=0.002
    )  # Adjust weight as needed
    # angle_of_attack_penalty = RewTerm(func=mdp.reward_angle_of_attack_penalty, weight=0.1)  # Adjust weight as needed
    soil_spilling_penalty = RewTerm(
        func=mdp.reward_soil_spilling_penalty, weight=0.01
    )  # Adjust weight as needed

    # --------- Soil indep
    # RewTerm(func=mdp.action_rate_l2, weight=-1e-3)s
    action_rate = RewTerm(func=mdp.action_rate_l2_excavation, weight=-0.001)  #
    ## Constant reward
    constant = RewTerm(func=mdp.const_reward, weight=-0.001)  #
    # Soil dep reward
    bucket_filling = RewTerm(func=mdp.reward_bucket_filling, weight=0.5)  # 0.77
    max_depth_tracking = RewTerm(func=mdp.reward_max_depth_tracking, weight=0.01)
    bucket_curl = RewTerm(func=mdp.reward_bucket_curl, weight=0.02)  #
    pitch_up = RewTerm(func=mdp.reward_pitch_up, weight=0.02)  #
    bucket_edge_up = RewTerm(func=mdp.reward_bucket_edge_up, weight=0.0)  #
    bucket_edge_down = RewTerm(func=mdp.reward_bucket_edge_down, weight=-0.05)  #
    power = RewTerm(func=mdp.reward_power, weight=2e-7)  #
    bucket_curl_and_up = RewTerm(func=mdp.reward_bucket_curl_and_up, weight=0.0)  #
    # Termination conditions reward
    termination_reward = RewTerm(func=mdp.termination_reward, weight=0.2)
    # New barrier penalty for pullup distance
    # --- pull-up barrier (smooth version) ---
    pullup_barrier_penalty = RewTerm(
        func=mdp.reward_pullup_barrier,
        weight=0.01,  # small overall scale since tanh output ∈ [-1,0]
        params={
            "P_margin": 0.5,  # start ramping 0.5 m before pull-up dist
            "k_fade": 10.0,  # how sharply the fade turns on
            "k_overshoot": 5.0,  # softplus gain past the wall
        },
    )

    # --- AoA barrier near soil (smooth version) ---
    aoa_barrier_penalty = RewTerm(
        func=mdp.reward_aoa_barrier_near_soil,
        weight=0.01,  # you can go 0.01–0.05 and tune from there
        params={
            "target_min_aoa": 0.20,  # radians
            "depth_half": 0.05,  # 5 cm soft threshold
            "aoa_half": 0.05,  # 0.05 rad soft threshold
        },
    )

@configclass
class ObservationsCfg:
    """Observation specifications for the MDP."""

    @configclass
    class PolicyCfg(ObsGroup):
        """Observations for policy group."""

        # # # Joint Velocities, 3
        # dof_pos = ObsTerm(func=mdp.dof_pos, params={"asset_cfg": SceneEntityCfg("robot", joint_names=[".*"])})
        # # Joint positions, 3
        # dof_vel = ObsTerm(
        #     func=mdp.dof_vel, params={"asset_cfg": SceneEntityCfg("robot", joint_names=[".*"])}, scale=[0.1, 0.1, 0.2]
        # )
        # Joint Torques, 3
        # dof_tau = ObsTerm(func=mdp.dof_tau, mean=[-2.66e4, 2.45e4, 1.19e4], scale=[1.6e5, 1.44e5, 5.47e4])
        dof_tau = ObsTerm(
            func=mdp.dof_tau_normalized, mean=[0.0, 0.0, 0.0], divisor=[1.0, 1.0, 1.0]
        )
        # previous action 3
        prev_action = ObsTerm(func=mdp.last_action_excavation)
        # Bucket lin gac, 4
        bucket_pos_gac = ObsTerm(
            func=mdp.bucket_pos_gac, mean=[6.0, 0.0], divisor=[2.0, 1.75]
        )
        bucket_vel_gac = ObsTerm(
            func=mdp.bucket_vel_gac, mean=[0.0, 0.0], divisor=[1.0, 1.0]
        )
        bucket_lin_vel_norm = ObsTerm(
            func=mdp.bucket_lin_vel_norm, mean=[0.0], divisor=[1.0]
        )
        # Bucket ang gac, 2
        bucket_ang_gac = ObsTerm(func=mdp.bucket_ang_gac)
        # 2
        fill_ratio = ObsTerm(func=mdp.fill_ratio, mean=[0.0], divisor=[1.0])
        aoa = ObsTerm(func=mdp.bucket_aoa, mean=[0.0], divisor=[1.0])
        # 5
        soil_height = ObsTerm(
            func=mdp.soil_height,
            mean=[-1.25, -1.25, -1.25, -1.25, -1.25],
            divisor=[1.75, 1.75, 1.75, 1.75, 1.75],
        )
        # 1
        bucket_depth = ObsTerm(func=mdp.bucket_depth, mean=[-0.25], divisor=[0.75])
        # 5
        max_depth = ObsTerm(
            func=mdp.max_depth,
            mean=[-1.25, -1.25, -1.25, -1.25, -1.25],
            divisor=[1.75, 1.75, 1.75, 1.75, 1.75],
        )
        # 1
        pullup_dist = ObsTerm(func=mdp.pullup_dist, mean=[2.75], divisor=[0.75])
        bucket_dim = ObsTerm(func=mdp.bucket_dim, mean=[0.0], divisor=[1.0])
        target_soil_volume = ObsTerm(
            func=mdp.target_fill_ratio, mean=[0.0], divisor=[1.0]
        )
        soil_forces = ObsTerm(
            func=mdp.soil_forces_normalized,
            mean=[0.0, 0.0, 0.0],
            divisor=[1.0, 1.0, 1.0],
        )

        def __post_init__(self):
            self.enable_corruption = True
            self.concatenate_terms = True

    # observation groups
    policy: PolicyCfg = PolicyCfg()


@configclass
class EventCfg:
    """Configuration for events."""

    # Reject sampling using a cache
    rejection_sampling = EventTerm(
        func=mdp.ik_reset,
        mode="reset",
    )


@configclass
class TerminationsCfg:
    """Termination terms for the MDP."""

    # NOTE: : Termination manager special for excavation is implemented because termination are checked differently
    # TODO: Change the manager to implement terminations here


@configclass
class CurriculumCfg:
    """Curriculum terms for the MDP."""

    # NOTE: Curriculum manager special for excavation is implemented because curriculum buffers need
    #       to be updated over iterations


@configclass
class AssetMetadataCfg:
    """Asset metadata for the MDP."""

    model_coefficients_name = "model_coefficients.json"
    torque_limits_name = "torque_limits.yaml"
    scale_factors_name = "scale_factors.yaml"


@configclass
class ugepEnvCfg(ExcavationEnvCfg):
    """Configuration for the locomotion velocity-tracking environment."""

    # Scene settings
    # note: replicate physics to false allow different physicacoriolis_centrifugal_force properties for different robots in different envs
    scene: MySceneCfg = MySceneCfg(
        num_envs=512, env_spacing=15, replicate_physics=False
    )  # 15
    # Basic settings
    observations: ObservationsCfg = ObservationsCfg()
    actions: ActionsCfg = ActionsCfg()
    commands: CommandsCfg = CommandsCfg()
    # MDP settings
    rewards: RewardsCfg = RewardsCfg()
    terminations: TerminationsCfg = TerminationsCfg()
    events: EventCfg = EventCfg()
    curriculum: CurriculumCfg = CurriculumCfg()
    asset_metadata: AssetMetadataCfg = AssetMetadataCfg()

    # gains PID Tuning
    k_d = [25, 30, 20]
    arm_joints_names = ["J_BOOM", "J_STICK", "J_EE_PITCH"]
    ee_body_name = "ROTO_BASE"
    ik_mode = True
    
    enable_lidar = False

    def __post_init__(self):
        """Post initialization."""
        # general settings
        if self.enable_lidar:
            self.scene.lidar = raycaster
        self.decimation = 4
        self.episode_length_s = 19.95
        # simulation settings
        self.sim.dt = 0.04
        # self.sim.disable_contact_processing = True
        # with 10 still sinks for heavy machines.
        # self.sim.physx.min_velocity_iteration_count = 20
        # for 128k need 0.6 M patches (4x the num of envs)
        self.sim.physx.gpu_max_rigid_patch_count = 260000 * 2
        self.sim.physx.bounce_threshold_velocity = 1.0
        self.sim.physx.solver_type = 1.0
        self.sim.render_interval = 10
        self.sim.physx.gpu_max_rigid_patch_count = 100000 * 4

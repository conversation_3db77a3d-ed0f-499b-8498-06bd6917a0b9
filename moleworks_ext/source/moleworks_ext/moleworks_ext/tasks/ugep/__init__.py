# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
"""
M545 Excavation environment
"""

import gymnasium as gym

from . import agents, ugep_env_cfg

##
# Register Gym environments.
##

gym.register(
    id="Isaac-ugep-v0",
    entry_point="moleworks_ext.tasks.ugep.excavation_env:ExcavationEnv",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": ugep_env_cfg.ugepEnvCfg,
        "rsl_rl_cfg_entry_point": agents.rsl_rl_cfg.M545PPORunnerCfg,
    },
)

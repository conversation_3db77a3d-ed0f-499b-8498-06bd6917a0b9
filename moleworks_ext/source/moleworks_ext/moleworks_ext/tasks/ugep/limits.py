import torch
from moleworks_ext.tasks.ugep.excavation_utils import helpers


class Limits:
    def __init__(self, m545_measurements):
        self.m545_measurements = m545_measurements
        self.num_envs = m545_measurements.num_envs
        self.num_dofs = m545_measurements.num_dofs
        self.device = m545_measurements.device
        
        # Initialize current torque limits
        self.curr_torque_limit_lower = torch.zeros(self.num_envs, self.num_dofs, device=self.device)
        self.curr_torque_limit_upper = torch.zeros(self.num_envs, self.num_dofs, device=self.device)
        
    def update(self, joint_pos):
        """
        Update the current torque limits based on joint positions.
        
        Args:
            joint_pos: Current joint positions of shape (N, num_dofs)
        """
        self.compute_torque_limits(joint_pos)
        
    def compute_torque_limits(self, joint_pos=None):
        """
        Compute the torque limits for the joints using the vector self.torque_coeffs of size (N, M, 3) and the joint positions.
        M is the number of coefficients of the polynomial. Since we assume the torque limits of a joint to be independent of other joints positions,
        the degree of the polynomial m = size(M) - 1.

        Args:
            joint_pos: Joint positions of shape (N, 3). If None, uses self.m545_measurements.joint_pos
        """
        if joint_pos is None:
            joint_pos = self.m545_measurements.joint_pos
            
        target_coeffs = self.m545_measurements.torque_coeffs
        target_force_max = self.m545_measurements.force_max
        target_force_min = self.m545_measurements.force_min

        # Compute polynomial factor based on joint positions
        torque_factor = helpers.compute_indipendent_poly(
            target_coeffs, joint_pos, self.m545_measurements.torque_coeff_degree
        )
        self.m545_measurements.torque_factors[:] = torque_factor

        # Create a mask for positive torque factors
        positive_mask = torque_factor >= 0

        # Compute tau_max and tau_min based on the sign of torque_factor
        # When torque_factor is positive: tau_max = force_max * factor, tau_min = force_min * factor
        # When torque_factor is negative: tau_max = force_min * factor, tau_min = force_max * factor (signs flip)
        tau_max = torch.where(
            positive_mask,
            target_force_max * torque_factor,
            target_force_min * torque_factor,
        )
        tau_min = torch.where(
            positive_mask,
            target_force_min * torque_factor,
            target_force_max * torque_factor,
        )
        
        # Update current limits
        self.curr_torque_limit_upper[:] = tau_max
        self.curr_torque_limit_lower[:] = tau_min
        
        # Also update the m545_measurements limits for backward compatibility
        self.m545_measurements.tau_max[:] = tau_max
        self.m545_measurements.tau_min[:] = tau_min
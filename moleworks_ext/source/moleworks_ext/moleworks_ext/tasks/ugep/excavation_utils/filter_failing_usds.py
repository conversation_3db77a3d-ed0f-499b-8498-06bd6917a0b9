import os
import json
import glob
import argparse
import shutil


def get_latest_directory(base_path):
    directories = [d for d in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, d))]
    latest_directory = max(directories, key=lambda d: os.path.getmtime(os.path.join(base_path, d)))
    return os.path.join(base_path, latest_directory)


def parse_overall_stats(stats_path):
    with open(stats_path, 'r') as file:
        data = json.load(file)
    return data


def list_failing_assets(data, threshold):
    failing_assets = [asset_id for asset_id, failure_rate in data['failure_rates_by_asset'].items() if failure_rate > threshold]
    percentage_failing = len(failing_assets) / len(data['failure_rates_by_asset']) * 100
    print(f"Percentage of failing assets: {percentage_failing:.2f}%")
    return failing_assets


def remove_failing_usds(usd_path, failing_assets):
    for asset_id in failing_assets:
        asset_folder = os.path.join(usd_path, str(asset_id))
        usd_files = glob.glob(os.path.join(asset_folder, 'urdf', '*.usd'))
        for usd_file in usd_files:
            os.remove(usd_file)
            print(f"Removed: {usd_file}")
        # Remove the main folder and all its contents
        if os.path.exists(asset_folder):
            shutil.rmtree(asset_folder)
            print(f"Removed folder and all its contents: {asset_folder}")


def rename_asset_folders(usd_path):
    asset_folders = sorted([d for d in os.listdir(usd_path) if os.path.isdir(os.path.join(usd_path, d))], key=int)
    for new_id, folder in enumerate(asset_folders):
        old_path = os.path.join(usd_path, folder)
        new_path = os.path.join(usd_path, str(new_id))
        if old_path != new_path:
            os.rename(old_path, new_path)
            print(f"Renamed: {old_path} to {new_path}")


def main(directory_path=None, threshold=2.0, usd_path=None):
    base_path = '/home/<USER>/git/orbit/logs/rsl_rl/Isaac-ugep-v0'  # Update this to your base directory
    if directory_path is None:
        directory_path = get_latest_directory(base_path)

    stats_path = os.path.join(directory_path, 'overall_stats.json')
    data = parse_overall_stats(stats_path)

    failing_assets = list_failing_assets(data, threshold)
    if usd_path is None:
        usd_path = '/path/to/usd_path'  # Update this to your default USD path
    remove_failing_usds(usd_path, failing_assets)
    rename_asset_folders(usd_path)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Filter and remove failing USDs.')
    parser.add_argument('--directory', type=str, help='Directory path to inspect')
    parser.add_argument('--threshold', type=float, default=0.1, help='Failure rate threshold')
    parser.add_argument('--usd_path', type=str, help='USD path directory')
    args = parser.parse_args()

    main(args.directory, args.threshold, args.usd_path)
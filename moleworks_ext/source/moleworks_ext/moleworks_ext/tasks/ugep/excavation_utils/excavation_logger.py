# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
Logger designed to log data of 1 environment when played.
Reflects changes to Soil API (getters -> properties) and ObservationManager API.
"""

from __future__ import annotations

import torch
from typing import TYPE_CHECKING

# Use TYPE_CHECKING to avoid circular import issues, common in large projects
if TYPE_CHECKING:
    # Assuming this is the correct path to your specific ExcavationEnv implementation
    # Adjust if your environment class is located elsewhere
    from moleworks_ext.tasks.excavation.excavation_env import ExcavationEnv
    # Or if it's the UGEP version:
    # from moleworks_ext.tasks.ugep.excavation_env import ExcavationEnv

import csv
import matplotlib.pyplot as plt
import numpy as np
import os
import pickle as pkl
from collections import defaultdict
from matplotlib.gridspec import GridSpec
from cycler import cycler # Import cycler for color palettes

# Assuming these utils exist in your moleworks_ext project structure
from moleworks_ext.common.utils.utils import get_log_dict, multipage

# Configure Matplotlib settings
plt.rcParams["figure.max_open_warning"] = 0
plt.rcParams["figure.figsize"] = [10, 5] # Default figure size

# Define and set a color-blind friendly palette
# https://seaborn.pydata.org/tutorial/color_palettes.html#qualitative-color-palettes
colorblind_palette = [
    '#0072B2',  # Blue
    '#E69F00',  # Orange
    '#56B4E9',  # Sky Blue
    '#F0E442',  # Yellow
    '#009E73',  # Bluish Green
    '#D55E00',  # Vermillion
    '#CC79A7',  # Purple
    '#999999'   # Grey
]
plt.rcParams['axes.prop_cycle'] = cycler(color=colorblind_palette)


class Logger:
    def __init__(self, env: ExcavationEnv, num_steps, robot_idx=0, use_custom_bucket_state_axes=False, title=None):
        # Use env.unwrapped to access the underlying environment if wrapped (e.g., by Gym wrappers)
        # If your ExcavationEnv is the base env, self.env = env is fine.
        # If using Isaac Lab wrappers like RslRlVecEnvWrapper, env.unwrapped is crucial.
        # Let's assume env might be wrapped for safety.
        self.env = env.unwrapped if hasattr(env, "unwrapped") else env
        self.N = num_steps
        self.idx = robot_idx # Index of the specific environment instance to log
        self.num_episodes = 0

        # Per-episode tracking
        self.episode_step = 0
        self.episode_steps = np.zeros(self.N) * np.nan
        self.reset_indices = [] # Track steps where a reset occurred

        # Reward logging
        self.rew_log = defaultdict(list)

        # Joint names - Ensure this matches the articulation in your ExcavationEnv config
        # Example: self.env.articulations["robot"].joint_names might be more robust
        # self.joint_names = self.env.articulations["robot"].joint_names[self.env.articulations["robot"].find_joints("arm_.*")[0]] # Example if joints have prefix
        # Using the hardcoded list for now, adjust as needed:
        self.joint_names = ["boom", "stick", "pitch"] # Make sure this order and names are correct!
        self.num_joints = len(self.joint_names)

        # --- State Logging Arrays (initialized with NaN) ---
        # Bucket Position (World Frame)
        self.bucket_pos_w_x = np.zeros(self.N) * np.nan
        self.bucket_pos_w_y = np.zeros(self.N) * np.nan # Often unused if 2D (XZ) focus
        self.bucket_pos_w_z = np.zeros(self.N) * np.nan

        # Bucket Angles & State
        self.bucket_top_ang_to_hori = np.zeros(self.N) * np.nan # Angle of line connecting tip and back point
        self.bucket_aoa = np.zeros(self.N) * np.nan # Angle of attack (velocity vs bottom plate)
        self.bp_angle_w = np.zeros(self.N) * np.nan # Bottom plate angle relative to world horizontal

        # Curriculum / Reset Parameters
        self.pullup_dist = np.zeros(self.N) * np.nan
        self.pullup_band = np.zeros(self.N) * np.nan

        # Bucket Fill State
        self.bucket_max_fill_area = np.zeros(self.N) * np.nan
        self.bucket_fill_area = np.zeros(self.N) * np.nan
        self.bucket_swept_area = np.zeros(self.N) * np.nan

        # Soil State & Interaction
        self.soil_height_w = np.zeros(self.N) * np.nan # Soil height at current bucket x-pos
        self.max_depth_height_w = np.zeros(self.N) * np.nan # Max depth limit at current bucket x-pos
        self.soil_rs = np.zeros(self.N) * np.nan # Soil resistance force magnitude (resultant)
        self.soil_edge_rs = np.zeros(self.N) * np.nan # Soil resistance (edge component)
        self.soil_plate_rs = np.zeros(self.N) * np.nan # Soil resistance (plate component)
        self.soil_deadload = np.zeros(self.N) * np.nan # Force due to soil weight in bucket
        self.soil_bucket_vel_cos = np.zeros(self.N) * np.nan # Cosine of angle between bucket vel and soil normal?

        # Bucket Kinematics (World Frame)
        self.bp_unit_vector_w = np.zeros((self.N, 3)) * np.nan # Bottom plate direction vector
        self.bucket_lin_vel_w_x = np.zeros(self.N) * np.nan
        self.bucket_lin_vel_w_y = np.zeros(self.N) * np.nan
        self.bucket_lin_vel_w_z = np.zeros(self.N) * np.nan
        self.bucket_lin_vel_norm = np.zeros(self.N) * np.nan
        self.bucket_ang_vel_w_y = np.zeros(self.N) * np.nan # Angular velocity around Y (pitching)

        # Pitch Joint State (World Frame) - Assuming 'pitch' is the last actuated joint holding the bucket
        self.j_pitch_pos_x = np.zeros(self.N) * np.nan
        self.j_pitch_pos_y = np.zeros(self.N) * np.nan
        self.j_pitch_pos_z = np.zeros(self.N) * np.nan
        self.j_pitch_lin_vel_w_x = np.zeros(self.N) * np.nan
        self.j_pitch_lin_vel_w_y = np.zeros(self.N) * np.nan
        self.j_pitch_lin_vel_w_z = np.zeros(self.N) * np.nan
        self.j_pitch_quat_w = np.zeros(self.N) * np.nan
        self.j_pitch_quat_x = np.zeros(self.N) * np.nan
        self.j_pitch_quat_y = np.zeros(self.N) * np.nan
        self.j_pitch_quat_z = np.zeros(self.N) * np.nan
        self.j_pitch_ang_vel_x = np.zeros(self.N) * np.nan
        self.j_pitch_ang_vel_y = np.zeros(self.N) * np.nan
        self.j_pitch_ang_vel_z = np.zeros(self.N) * np.nan

        # Base/Root State (World Frame)
        self.base_lin_vel_w_x = np.zeros(self.N) * np.nan
        self.base_lin_vel_w_y = np.zeros(self.N) * np.nan
        self.base_lin_vel_w_z = np.zeros(self.N) * np.nan
        self.base_ang_vel_w_x = np.zeros(self.N) * np.nan
        self.base_ang_vel_w_y = np.zeros(self.N) * np.nan
        self.base_ang_vel_w_z = np.zeros(self.N) * np.nan

        # DOF (Joint) State
        self.dof_pos = np.zeros((self.N, self.num_joints)) * np.nan
        self.dof_vel = np.zeros((self.N, self.num_joints)) * np.nan
        self.actions = np.zeros((self.N, self.num_joints)) * np.nan # Raw actions from policy
        self.actions_clipped_scaled = np.zeros((self.N, self.num_joints)) * np.nan # Processed actions sent to controller

        # DOF Torques/Forces
        self.dof_torque = np.zeros((self.N, self.num_joints)) * np.nan # Applied/measured torque
        self.dof_gravity = np.zeros((self.N, self.num_joints)) * np.nan # Gravity compensation torque
        self.dof_inertial = np.zeros((self.N, self.num_joints)) * np.nan # Inertial forces torque (Coriolis/Centrifugal)
        self.dof_soil = np.zeros((self.N, self.num_joints)) * np.nan # Torque due to external soil forces

        # DOF Limits (verify existence of `env.limits` and its attributes)
        self.torque_limit_lower = np.zeros((self.N, self.num_joints)) * np.nan
        self.torque_limit_upper = np.zeros((self.N, self.num_joints)) * np.nan
        self.vel_limit_lower = np.zeros((self.N, self.num_joints)) * np.nan
        self.vel_limit_upper = np.zeros((self.N, self.num_joints)) * np.nan

        # Bucket End Effector Position relative to Pitch joint? (Check definition)
        self.w_r_pe = np.zeros((self.N, 3)) * np.nan

        # --- Observation and Reward Logging ---
        # Use the new observation manager API for initialization
        self.obs = {}
        # Assuming 'policy' is the only/relevant observation group
        if "policy" in self.env.observation_manager.group_obs_term_dim:
            for obs_idx, obs_name in enumerate(self.env.observation_manager.active_terms["policy"]):
                obs_dim = self.env.observation_manager.group_obs_term_dim["policy"][obs_idx][0]
                self.obs[obs_name] = np.zeros((self.N, obs_dim)) * np.nan
        else:
            print("Warning: Observation group 'policy' not found in observation manager.")

        # Use helper for reward logging initialization
        # Ensure reward manager terms are available before calling this
        if hasattr(self.env, "reward_manager") and self.env.reward_manager.one_step_rew:
             self.rews = get_log_dict(self.env.reward_manager.one_step_rew, self.N)
        else:
             print("Warning: Reward manager or one_step_rew not found/initialized. Reward logging disabled.")
             self.rews = {}

        # Termination Logging
        # Ensure termination manager exists and has the counts attributes
        self.terms_neg = {}
        self.terms_pos = {}
        if hasattr(self.env, "termination_excavation"):
            if hasattr(self.env.termination_excavation, "episode_neg_term_counts"):
                 self.terms_neg = get_log_dict(self.env.termination_excavation.episode_neg_term_counts, 1, dim=1)
            if hasattr(self.env.termination_excavation, "episode_pos_term_counts"):
                 self.terms_pos = get_log_dict(self.env.termination_excavation.episode_pos_term_counts, 1, dim=1)
        else:
            print("Warning: termination_excavation manager not found. Termination logging disabled.")


        # Bucket state axes setup for combined plotting
        self.bucket_state_ax = None
        self.gs = None # Initialize gridspec to None
        if use_custom_bucket_state_axes:
            plt.rcParams["figure.figsize"] = [20, 10] # Larger figure for multi-panel plot
            fig = plt.figure(constrained_layout=True) # Use constrained_layout for better spacing
            if title:
                 fig.suptitle(title)
            # Create a 4 row, 2 column grid
            self.gs = GridSpec(4, 2, figure=fig)
            # Bucket state plot occupies top-left 3 rows, 1 column
            self.bucket_state_ax = fig.add_subplot(self.gs[0:3, 0])
            # Other plots will be added to self.gs later in their respective functions

    def log_states(self, i):
        """Logs the environment state for the specified robot index at timestep i."""
        # Check if environment index is valid
        if self.idx >= self.env.num_envs:
            print(f"Error: Logger index {self.idx} is out of bounds for {self.env.num_envs} environments.")
            return

        # Track episode steps and resets
        if self.env.reset_buf[self.idx]:
            self.episode_step = 0
            self.reset_indices.append(i) # Log step number where reset happens
        else:
            self.episode_step += 1
        self.episode_steps[i] = self.episode_step

        # Plot soil state periodically if using custom axes
        if i % 15 == 0 and self.bucket_state_ax is not None:
            # Pass the specific index to plot
            self.env.soil.plot_state([self.idx], show=False, label=(i == 0), ax=self.bucket_state_ax)

        # --- Access Data using self.idx ---
        # Make sure all accessed attributes exist and are per-environment tensors/arrays

        # Bucket Position
        self.bucket_pos_w_x[i] = self.env.m545_measurements.bucket_pos_w[self.idx, 0].item()
        self.bucket_pos_w_y[i] = self.env.m545_measurements.bucket_pos_w[self.idx, 1].item()
        self.bucket_pos_w_z[i] = self.env.m545_measurements.bucket_pos_w[self.idx, 2].item()

        # Curriculum / Reset Vars
        # Ensure these attributes exist on the env or curriculum manager
        if hasattr(self.env, "pullup_dist"):
             self.pullup_dist[i] = self.env.pullup_dist[self.idx].item()
        if hasattr(self.env, "curriculum_excavation") and hasattr(self.env.curriculum_excavation, "curr_pullup_band"):
             # curr_pullup_band might be scalar, handle potential indexing error
             band_tensor = self.env.curriculum_excavation.curr_pullup_band
             self.pullup_band[i] = band_tensor.item() if band_tensor.numel() == 1 else band_tensor[self.idx].item()

        # Bucket Angles & Fill State - Use NEW property API for soil
        self.bucket_top_ang_to_hori[i] = self.env.soil.bucket_full_angle_w[self.idx].item()
        self.bucket_aoa[i] = self.env.m545_measurements.bucket_aoa[self.idx].item()
        self.bp_angle_w[i] = self.env.soil.bucket_bp_angle_w[self.idx].item()

        # Ensure max_fill_area is per-env; if scalar, remove [self.idx]
        max_fill_area_tensor = self.env.soil.max_fill_area
        self.bucket_max_fill_area[i] = max_fill_area_tensor.item() if max_fill_area_tensor.numel() == 1 else max_fill_area_tensor[self.idx].item()

        self.bucket_fill_area[i] = self.env.soil.fill_area[self.idx].item()
        self.bucket_swept_area[i] = self.env.soil.swept_area[self.idx].item()

        # Soil Interaction - Use methods for position-dependent queries, properties otherwise
        current_bucket_x = self.env.m545_measurements.bucket_pos_w[self.idx, 0] # Keep as tensor for input
        self.soil_height_w[i] = self.env.soil.get_soil_height_at_pos(
            current_bucket_x, env_ids=[self.idx] # Pass list even for one
        ).item()
        self.max_depth_height_w[i] = self.env.soil.get_max_depth_height_at_pos(
            current_bucket_x, env_ids=[self.idx]
        ).item()

        # Soil forces - Use NEW property API
        self.soil_rs[i] = self.env.soil.rs[self.idx].item()
        self.soil_edge_rs[i] = self.env.soil.edge_rs[self.idx].item()   # Corrected order based on property names
        self.soil_plate_rs[i] = self.env.soil.plate_rs[self.idx].item() # Corrected order based on property names
        self.soil_deadload[i] = self.env.soil.deadload[self.idx].item()
        self.soil_bucket_vel_cos[i] = self.env.soil.bucket_vel_cos[self.idx].item()

        # Bucket Kinematics
        self.bp_unit_vector_w[i] = self.env.m545_measurements.bp_unit_vector_w[self.idx].cpu().numpy()
        self.bucket_lin_vel_w_x[i] = self.env.m545_measurements.bucket_vel_w[self.idx, 0].item()
        self.bucket_lin_vel_w_y[i] = self.env.m545_measurements.bucket_vel_w[self.idx, 1].item()
        self.bucket_lin_vel_w_z[i] = self.env.m545_measurements.bucket_vel_w[self.idx, 2].item()
        self.bucket_lin_vel_norm[i] = self.env.m545_measurements.bucket_vel_norm[self.idx].item()
        self.bucket_ang_vel_w_y[i] = self.env.m545_measurements.j_pitch_ang_vel[self.idx, 1].item() # Assuming pitch joint ang vel y is bucket ang vel y

        # Pitch Joint State
        self.j_pitch_pos_x[i] = self.env.m545_measurements.j_pitch_pos[self.idx, 0].item()
        self.j_pitch_pos_y[i] = self.env.m545_measurements.j_pitch_pos[self.idx, 1].item()
        self.j_pitch_pos_z[i] = self.env.m545_measurements.j_pitch_pos[self.idx, 2].item()
        self.j_pitch_lin_vel_w_x[i] = self.env.m545_measurements.j_pitch_vel[self.idx, 0].item()
        self.j_pitch_lin_vel_w_y[i] = self.env.m545_measurements.j_pitch_vel[self.idx, 1].item()
        self.j_pitch_lin_vel_w_z[i] = self.env.m545_measurements.j_pitch_vel[self.idx, 2].item()
        self.j_pitch_quat_w[i] = self.env.m545_measurements.j_pitch_quat[self.idx, 0].item()
        self.j_pitch_quat_x[i] = self.env.m545_measurements.j_pitch_quat[self.idx, 1].item()
        self.j_pitch_quat_y[i] = self.env.m545_measurements.j_pitch_quat[self.idx, 2].item()
        self.j_pitch_quat_z[i] = self.env.m545_measurements.j_pitch_quat[self.idx, 3].item()
        self.j_pitch_ang_vel_x[i] = self.env.m545_measurements.j_pitch_ang_vel[self.idx, 0].item()
        self.j_pitch_ang_vel_y[i] = self.env.m545_measurements.j_pitch_ang_vel[self.idx, 1].item()
        self.j_pitch_ang_vel_z[i] = self.env.m545_measurements.j_pitch_ang_vel[self.idx, 2].item()

        # Base State
        self.base_lin_vel_w_x[i] = self.env.m545_measurements.root_lin_vel_w[self.idx, 0].item()
        self.base_lin_vel_w_y[i] = self.env.m545_measurements.root_lin_vel_w[self.idx, 1].item()
        self.base_lin_vel_w_z[i] = self.env.m545_measurements.root_lin_vel_w[self.idx, 2].item()
        self.base_ang_vel_w_x[i] = self.env.m545_measurements.root_ang_vel_w[self.idx, 0].item()
        self.base_ang_vel_w_y[i] = self.env.m545_measurements.root_ang_vel_w[self.idx, 1].item()
        self.base_ang_vel_w_z[i] = self.env.m545_measurements.root_ang_vel_w[self.idx, 2].item()

        # DOFs State & Actions
        self.dof_pos[i] = self.env.m545_measurements.joint_pos[self.idx].cpu().numpy()
        self.dof_vel[i] = self.env.m545_measurements.joint_vel[self.idx].cpu().numpy()
        self.actions[i] = self.env.actions[self.idx].cpu().numpy() # Raw policy actions
        # Check if clipped_scaled_actions exists before accessing
        if hasattr(self.env, "clipped_scaled_actions"):
            self.actions_clipped_scaled[i] = self.env.clipped_scaled_actions[self.idx].cpu().numpy()
        else:
             # Handle case where actions might not be clipped/scaled in play mode
             self.actions_clipped_scaled[i] = self.actions[i] # Log raw if processed not available

        # DOF Torques
        self.dof_torque[i] = self.env.torques[self.idx].cpu().numpy() # Applied torque
        self.dof_gravity[i] = self.env.m545_measurements.gravity_tau[self.idx].cpu().numpy()
        # Check for inertial_tau, ext_f_tau, ext_m_tau before accessing
        if hasattr(self.env, "inertial_tau"):
            self.dof_inertial[i] = self.env.inertial_tau[self.idx].cpu().numpy()
        if hasattr(self.env, "ext_f_tau") and hasattr(self.env, "ext_m_tau"):
            self.dof_soil[i] = (self.env.ext_f_tau[self.idx] + self.env.ext_m_tau[self.idx]).cpu().numpy()

        # DOF Limits (Check existence of 'limits' manager and attributes)
        if hasattr(self.env, "limits"):
             if hasattr(self.env.limits, "curr_torque_limit_lower"):
                 self.torque_limit_lower[i] = self.env.limits.curr_torque_limit_lower[self.idx].cpu().numpy()
             if hasattr(self.env.limits, "curr_torque_limit_upper"):
                 self.torque_limit_upper[i] = self.env.limits.curr_torque_limit_upper[self.idx].cpu().numpy()
             if hasattr(self.env.limits, "curr_vel_limit_lower"):
                 self.vel_limit_lower[i] = self.env.limits.curr_vel_limit_lower[self.idx].cpu().numpy()
             if hasattr(self.env.limits, "curr_vel_limit_upper"):
                 self.vel_limit_upper[i] = self.env.limits.curr_vel_limit_upper[self.idx].cpu().numpy()
        else:
            # Fill limits with defaults (e.g., from articulation) if manager doesn't exist or is different
            # This part depends heavily on your specific env setup
            pass # Add fallback logic if needed


        self.w_r_pe[i] = self.env.m545_measurements.w_r_pe[self.idx].cpu().numpy()


        # Log Rewards
        if self.rews: # Check if reward dict exists
            pass

        # Log Observations using the new API
        if "policy" in self.env.observation_manager.active_terms:
            policy_obs_tensor = self.env.obs_buf["policy"][self.idx]
            start_idx = 0
            for obs_idx, obs_name in enumerate(self.env.observation_manager.active_terms["policy"]):
                obs_shape = self.env.observation_manager.group_obs_term_dim["policy"][obs_idx][0]
                end_idx = start_idx + obs_shape
                # Ensure indices are within bounds of the observation tensor
                if end_idx <= policy_obs_tensor.shape[0]:
                    self.obs[obs_name][i] = policy_obs_tensor[start_idx:end_idx].cpu().numpy()
                else:
                    print(f"Warning: Observation slicing error for {obs_name}. Index out of bounds.")
                    # Fill with NaN or handle error appropriately
                    self.obs[obs_name][i] = np.full(obs_shape, np.nan)
                start_idx = end_idx

    def save_state_to_csv(self, file_path):
        """Saves the logged state history to a CSV file."""
        try:
            with open(file_path, mode="w", newline="") as csv_file:
                csv_writer = csv.writer(csv_file)

                # Create headers dynamically
                headers = (
                    [f"dof_pos_{self.joint_names[j]}" for j in range(self.num_joints)]
                    + [f"dof_vel_{self.joint_names[j]}" for j in range(self.num_joints)]
                    + [
                        "bucket_pos_x", "bucket_pos_y", "bucket_pos_z",
                        "bucket_vel_x", "bucket_vel_y", "bucket_vel_z", "bucket_vel_norm",
                        "bucket_ang_vel_y", "bp_angle_w", "bucket_aoa", "bucket_top_ang_to_hori",
                        "fill_area", "max_fill_area", "swept_area",
                        "soil_height_at_bucket", "max_depth_at_bucket",
                    ]
                    + ["j_pitch_pos_x", "j_pitch_pos_y", "j_pitch_pos_z"]
                    + ["j_pitch_vel_x", "j_pitch_vel_y", "j_pitch_vel_z"]
                    + ["j_pitch_quat_w", "j_pitch_quat_x", "j_pitch_quat_y", "j_pitch_quat_z"]
                    + ["j_pitch_ang_vel_x", "j_pitch_ang_vel_y", "j_pitch_ang_vel_z"]
                    + ["w_r_pe_x", "w_r_pe_y", "w_r_pe_z"] # End effector relative pos
                    + [f"action_{self.joint_names[j]}" for j in range(self.num_joints)]
                    + [f"action_scaled_{self.joint_names[j]}" for j in range(self.num_joints)]
                    + [f"tau_applied_{self.joint_names[j]}" for j in range(self.num_joints)]
                    + [f"tau_gravity_{self.joint_names[j]}" for j in range(self.num_joints)]
                    + [f"tau_inertial_{self.joint_names[j]}" for j in range(self.num_joints)]
                    + [f"tau_soil_{self.joint_names[j]}" for j in range(self.num_joints)]
                    + [f"tau_limit_lower_{self.joint_names[j]}" for j in range(self.num_joints)]
                    + [f"tau_limit_upper_{self.joint_names[j]}" for j in range(self.num_joints)]
                    + [f"vel_limit_lower_{self.joint_names[j]}" for j in range(self.num_joints)]
                    + [f"vel_limit_upper_{self.joint_names[j]}" for j in range(self.num_joints)]
                     # Add Base Velocity? Soil Forces? Add as needed
                )
                csv_writer.writerow(headers)

                # Write data rows
                for i in range(self.N):
                    row = (
                        [self.dof_pos[i, j] for j in range(self.num_joints)]
                        + [self.dof_vel[i, j] for j in range(self.num_joints)]
                        + [
                            self.bucket_pos_w_x[i], self.bucket_pos_w_y[i], self.bucket_pos_w_z[i],
                            self.bucket_lin_vel_w_x[i], self.bucket_lin_vel_w_y[i], self.bucket_lin_vel_w_z[i], self.bucket_lin_vel_norm[i],
                            self.bucket_ang_vel_w_y[i], self.bp_angle_w[i], self.bucket_aoa[i], self.bucket_top_ang_to_hori[i],
                            self.bucket_fill_area[i], self.bucket_max_fill_area[i], self.bucket_swept_area[i],
                            self.soil_height_w[i], self.max_depth_height_w[i],
                        ]
                        + [self.j_pitch_pos_x[i], self.j_pitch_pos_y[i], self.j_pitch_pos_z[i]]
                        + [self.j_pitch_lin_vel_w_x[i], self.j_pitch_lin_vel_w_y[i], self.j_pitch_lin_vel_w_z[i]]
                        + [self.j_pitch_quat_w[i], self.j_pitch_quat_x[i], self.j_pitch_quat_y[i], self.j_pitch_quat_z[i]]
                        + [self.j_pitch_ang_vel_x[i], self.j_pitch_ang_vel_y[i], self.j_pitch_ang_vel_z[i]]
                        + [self.w_r_pe[i, 0], self.w_r_pe[i, 1], self.w_r_pe[i, 2]]
                        + [self.actions[i, j] for j in range(self.num_joints)]
                        + [self.actions_clipped_scaled[i, j] for j in range(self.num_joints)]
                        + [self.dof_torque[i, j] for j in range(self.num_joints)]
                        + [self.dof_gravity[i, j] for j in range(self.num_joints)]
                        + [self.dof_inertial[i, j] for j in range(self.num_joints)]
                        + [self.dof_soil[i, j] for j in range(self.num_joints)]
                        + [self.torque_limit_lower[i, j] for j in range(self.num_joints)]
                        + [self.torque_limit_upper[i, j] for j in range(self.num_joints)]
                        + [self.vel_limit_lower[i, j] for j in range(self.num_joints)]
                        + [self.vel_limit_upper[i, j] for j in range(self.num_joints)]
                    )
                    csv_writer.writerow(row)

            print(f"State data saved to {file_path}")
        except IOError as e:
            print(f"Error saving state data to CSV: {e}")
        except Exception as e:
            print(f"An unexpected error occurred during CSV saving: {e}")


    def log_dones(self):
        """Logs the final termination counts for the episode."""
        # Check if termination manager and counts exist
        if not hasattr(self.env, "termination_excavation"):
            return

        term_manager = self.env.termination_excavation
        if hasattr(term_manager, "episode_neg_term_counts"):
            for key in self.terms_neg: # Iterate through keys initialized earlier
                if key in term_manager.episode_neg_term_counts:
                    self.terms_neg[key][0] = term_manager.episode_neg_term_counts[key][self.idx].item() # Use .item() for scalar tensor

        if hasattr(term_manager, "episode_pos_term_counts"):
             for key in self.terms_pos:
                 if key in term_manager.episode_pos_term_counts:
                     self.terms_pos[key][0] = term_manager.episode_pos_term_counts[key][self.idx].item()

    def plot_states(self, show=False):
        """Generates all standard plots."""
        self._plot_bucket_state() # Call first if it modifies shared axes (like self.bucket_state_ax)
        self._plot_rewards()
        self._plot_terminations()
        self._plot_fill_state()
        self._plot_soil_forces()
        self._plot_bucket_vel()
        self._plot_base_vel()
        self._plot_obs()
        self._plot_dof_vel()
        self._plot_dof_pos()
        self._plot_dof_torques()
        self._plot_episode_step() # Plot episode steps last or on its own figure

        if show:
            plt.show(block=False) # Use False to keep script running

    def plot_states_multi_tester(self, show=False):
        """Generates plots suitable for the multi-panel layout."""
        # Assumes __init__ set up self.gs and self.bucket_state_ax
        if self.gs is None or self.bucket_state_ax is None:
             print("Error: Multi-tester plotting requires use_custom_bucket_state_axes=True.")
             # Fallback to standard plotting maybe?
             self.plot_states(show=show)
             return

        # Bucket state plot was already created and potentially populated by log_states
        # Add the overlay plots to the existing bucket_state_ax
        self._plot_bucket_state_overlays(ax=self.bucket_state_ax)

        # Add other plots to their designated grid locations
        self._plot_fill_state(make_subplot=True) # Will use self.gs
        self._plot_dof_torques(make_subplot=True) # Will use self.gs
        # Add more plots to the grid as needed, e.g.:
        # self._plot_dof_vel(make_subplot=True, grid_pos=(0, 1)) # Example: Top right
        # self._plot_rewards(make_subplot=True, grid_pos=(1, 1)) # Example: Second row right

        if show:
            plt.show(block=False)


    def log_rewards(self, rewards_dict, num_episodes):
        """Appends rewards from a dictionary (e.g., episode totals) to the log."""
        for key, value in rewards_dict.items():
            # Could add more filtering based on key names if needed
            if "rew" in key.lower() and isinstance(value, torch.Tensor):
                # Assuming value is a tensor, get item and scale
                self.rew_log[key].append(value.item() * num_episodes)
            elif isinstance(value, (int, float)):
                 self.rew_log[key].append(value * num_episodes) # Handle scalar values
        self.num_episodes += num_episodes

    def print_rewards(self):
        """Prints the average rewards accumulated over logged episodes."""
        if self.num_episodes == 0:
            print("No episodes logged. Cannot print average rewards.")
            return

        print("Average rewards per episode:")
        total_sum_rew = 0
        for key, values in self.rew_log.items():
            if values: # Ensure list is not empty
                 mean_val = np.sum(np.array(values)) / self.num_episodes
                 print(f" - {key}: {mean_val:.4f}")
                 if "sum" not in key.lower(): # Avoid double counting if a "sum" key exists
                      total_sum_rew += mean_val
            else:
                 print(f" - {key}: No data")
        print(f"Average total reward (sum of terms): {total_sum_rew:.4f}")
        print(f"Total number of episodes logged: {self.num_episodes}")

    def save_plots(self, save_path, file_name="plots.pdf", open_file=False): # Renamed open -> open_file
        """Saves all generated matplotlib figures to a multi-page PDF."""
        filename = os.path.join(save_path, file_name)
        try:
            # Ensure the save directory exists
            os.makedirs(save_path, exist_ok=True)
            # Use the multipage utility to save all open figures
            multipage(filename)
            print(f"Plots saved to {filename}")
            if open_file:
                try:
                    # Try to open the file using the default system viewer
                    if os.name == 'nt': # Windows
                        os.startfile(filename)
                    elif sys.platform == 'darwin': # macOS
                        subprocess.call(('open', filename))
                    else: # Linux/other Unix
                        subprocess.call(('xdg-open', filename))
                except Exception as e:
                    print(f"Could not open file '{filename}': {e}")
        except Exception as e:
            print(f"Error saving plots to PDF: {e}")
        finally:
            # Close all figures to free memory, regardless of save success
            plt.close("all")


    def save_for_ros_plotter(self, save_path):
        """Saves data in a pickle format compatible with a specific ROS plotter."""
        # Ensure save directory exists
        os.makedirs(save_path, exist_ok=True)
        output_filepath = os.path.join(save_path, "data.pkl")

        data = {}
        data["control_steps"] = self.episode_steps # Timesteps within the episode

        # --- Observations ---
        # Use the new observation manager API data
        obs_total_dim = 0
        if "policy" in self.env.observation_manager.group_obs_dim:
             obs_total_dim = self.env.observation_manager.group_obs_dim["policy"][0]

        if obs_total_dim > 0 and "policy" in self.env.observation_manager.active_terms:
            data["obs"] = np.zeros((self.N, obs_total_dim), dtype=np.float64) * np.nan # Initialize
            start_idx = 0
            active_terms = self.env.observation_manager.active_terms["policy"]
            term_dims = self.env.observation_manager.group_obs_term_dim["policy"]

            for obs_idx, obs_name in enumerate(active_terms):
                if obs_name in self.obs: # Check if logged
                    obs_shape = term_dims[obs_idx][0]
                    end_idx = start_idx + obs_shape
                    if end_idx <= data["obs"].shape[1]: # Bounds check
                        data["obs"][:, start_idx:end_idx] = self.obs[obs_name]
                    else:
                        print(f"Warning: Indexing error while saving obs '{obs_name}' for ROS plotter.")
                    start_idx = end_idx
                else:
                     print(f"Warning: Obs term '{obs_name}' not found in logger data for ROS plotter.")
                     # Need to handle missing dims if skipping
                     obs_shape = term_dims[obs_idx][0]
                     start_idx += obs_shape # Skip the dimension


            data["ob_dims"] = [dim[0] for dim in term_dims]
            data["ob_names"] = active_terms
        else:
            print("Warning: No observations found or logged for ROS plotter.")
            data["obs"] = np.zeros((self.N, 0), dtype=np.float64)
            data["ob_dims"] = []
            data["ob_names"] = []


        # --- Joint Data ---
        data["joint_pos"] = self.dof_pos
        data["joint_vel"] = self.dof_vel
        data["joint_vel_limits_lower"] = self.vel_limit_lower
        data["joint_vel_limits_upper"] = self.vel_limit_upper
        data["joint_torque"] = self.dof_torque
        data["joint_torque_limits_lower"] = self.torque_limit_lower
        data["joint_torque_limits_upper"] = self.torque_limit_upper

        # --- Bucket Data ---
        # Assuming 'w' frame is Isaac world (often at base for fixed robots)
        # Assuming 'gac' (Gravity Aligned Cabin?) frame is equivalent for this 2D focus
        bucket_pos_xz = np.hstack((self.bucket_pos_w_x.reshape(-1, 1),
                                   self.bucket_pos_w_z.reshape(-1, 1)))
        # Add a zero Y component
        data["bucket_pos_w"] = np.insert(bucket_pos_xz, 1, 0.0, axis=1)
        data["bucket_pos_gac"] = data["bucket_pos_w"] # Assuming same for now

        bucket_vel_xz = np.hstack((self.bucket_lin_vel_w_x.reshape(-1, 1),
                                   self.bucket_lin_vel_w_z.reshape(-1, 1)))
        data["bucket_vel_w"] = np.insert(bucket_vel_xz, 1, 0.0, axis=1)
        data["bucket_vel_gac"] = data["bucket_vel_w"] # Assuming same

        data["bucket_ang_vel_gac"] = self.bucket_ang_vel_w_y # Ang vel around Y

        # --- Other Data ---
        data["cabin_pitch_rate"] = self.base_ang_vel_w_y # Base angular vel around Y
        data["vel_command"] = self.actions_clipped_scaled # Processed actions
        data["action"] = self.actions # Raw actions
        data["swept_area"] = self.bucket_swept_area
        data["fill_area"] = self.bucket_fill_area
        data["max_fill_area"] = self.bucket_max_fill_area
        data["bucket_ang_vel_diff"] = self.bucket_aoa # Angle of attack
        data["soil_height_w"] = self.soil_height_w # At bucket x-pos
        data["soil_height_base"] = self.soil_height_w # Assuming same frame
        data["bottom_plate_unit_vecor_w"] = self.bp_unit_vector_w # Already (N, 3)
        data["bottom_plate_unit_vecor_gac"] = self.bp_unit_vector_w # Assuming same
        data["max_depth_w"] = self.max_depth_height_w # At bucket x-pos
        data["max_depth_base"] = self.max_depth_height_w # Assuming same

        # --- Save Pickle File ---
        try:
            with open(output_filepath, "wb") as f:
                pkl.dump(data, f)
            print(f"Data saved for ROS plotter: {output_filepath}")
        except IOError as e:
            print(f"Error saving data for ROS plotter: {e}")
        except Exception as e:
            print(f"An unexpected error occurred during pickle saving: {e}")

    # --- Plotting Helper Functions ---

    def _add_reset_lines(self, ax=None):
        """Adds vertical dashed lines to indicate episode resets on the given axes."""
        if ax is None:
            ax = plt.gca() # Get current axes if none provided
        # Get current plot limits to set line height appropriately
        ylim = ax.get_ylim()
        for reset_idx in self.reset_indices:
            ax.plot([reset_idx, reset_idx], ylim, linestyle='--', color='k', alpha=0.6, linewidth=1)
        ax.set_ylim(ylim) # Restore original y-limits

    def _plot_bucket_state(self):
        """ Creates the base soil plot if using custom axes. Called by plot_states/plot_states_multi_tester """
        # This function now mainly ensures the axis exists.
        # The actual soil plotting happens periodically in log_states.
        # The overlay plots (tip path, velocities etc.) are added by _plot_bucket_state_overlays
        if self.bucket_state_ax is None:
            # If not using the multi-tester layout, create a standard figure/axes
            # for the combined bucket/soil state plot.
            fig, self.bucket_state_ax = plt.subplots()
            self.bucket_state_ax.set_title("Bucket and Soil State")
            self.bucket_state_ax.set_xlabel("X world [m]")
            self.bucket_state_ax.set_ylabel("Z world [m]")
            # Plot soil profile *once* here if not plotting periodically in log_states
            # self.env.soil.plot_state([self.idx], show=False, label=True, ax=self.bucket_state_ax)
            self._plot_bucket_state_overlays(ax=self.bucket_state_ax) # Add overlays
        # If self.bucket_state_ax exists (from __init__ or above), overlays will be added later

    def _plot_bucket_state_overlays(self, ax):
        """Adds bucket path, velocity, angles etc. onto an existing axes."""
        # Use the colorblind palette cycler implicitly
        ax.plot(self.bucket_pos_w_x, self.bucket_pos_w_z, label="Tip Path (XZ)")
        ax.plot(self.bucket_pos_w_x, self.bucket_lin_vel_norm, label="Tip Vel Norm")
        # Plot velocity limit if available and valid
        if hasattr(self.env.cfg, "terminations_excavation") and hasattr(self.env.cfg.terminations_excavation, "max_bucket_vel"):
             max_vel = self.env.cfg.terminations_excavation.max_bucket_vel
             if max_vel is not None and max_vel > 0:
                 ax.plot(self.bucket_pos_w_x, np.ones_like(self.bucket_pos_w_x) * max_vel,
                         label="Max Vel Threshold", linestyle='--', linewidth=1)

        # Scatter plots for angles - use small markers
        ax.scatter(self.bucket_pos_w_x, self.bucket_top_ang_to_hori, label="Top Angle", marker=".", s=5)
        ax.scatter(self.bucket_pos_w_x, self.bucket_aoa, label="AoA", marker=".", s=5)
        ax.plot(self.bucket_pos_w_x, self.bp_angle_w, label="BP Angle")

        # Plot Pullup distance/band (usually static, plot first 2 points)
        if not np.isnan(self.pullup_dist[0:2]).any():
             ax.plot(self.pullup_dist[0:2], [-1.5, 1.5], label="Pullup Dist", linestyle='-.')
        if not np.isnan(self.pullup_band[0:2]).any() and not np.isnan(self.pullup_dist[0:2]).any():
             ax.plot(self.pullup_dist[0:2] + self.pullup_band[0:2], [-1.5, 1.5], label="Pullup Band", linestyle=':')

        # Finalize axes created by this function
        ax.legend(fontsize='small')
        ax.grid(True, linestyle='--', alpha=0.6)
        ax.axis("equal") # Maintain aspect ratio
        self._add_reset_lines(ax) # Add reset indicators


    def _plot_fill_state(self, make_subplot=False, grid_pos=(3, 0)):
        """Plots bucket fill area, max fill area, and swept area."""
        if make_subplot and self.gs:
            # Create subplot using gridspec location
            ax = plt.gcf().add_subplot(self.gs[grid_pos[0], grid_pos[1]]) # gcf() gets current figure
            ax.set_title("Fill/Swept Area")
        else:
            # Create a new figure
            fig, ax = plt.subplots()
            ax.set_title("Bucket Fill and Swept Area")

        ax.plot(self.bucket_max_fill_area, label="Max Fill Area")
        ax.plot(self.bucket_fill_area, label="Fill Area")
        ax.plot(self.bucket_swept_area, label="Swept Area") # Corrected typo

        ax.set_xlabel("Time Step")
        ax.set_ylabel("Area [m^2]") # Assuming units
        ax.legend(fontsize='small')
        ax.grid(True, linestyle='--', alpha=0.6)
        self._add_reset_lines(ax)


    def _plot_soil_forces(self, make_subplot=False, grid_pos=None):
        """Plots different components of soil resistance forces."""
        if make_subplot and self.gs and grid_pos:
            ax = plt.gcf().add_subplot(self.gs[grid_pos[0], grid_pos[1]])
            ax.set_title("Soil Forces")
        else:
            fig, ax = plt.subplots()
            ax.set_title("Soil Interaction Forces/Metrics")

        # Scaling factor for visualization if forces are large
        force_scale = 0.0001
        ax.plot(self.soil_rs * force_scale, label=f"Resultant Rs (x{force_scale:.0e})")
        ax.plot(self.soil_edge_rs * force_scale, label=f"Edge Rs (x{force_scale:.0e})")
        ax.plot(self.soil_plate_rs * force_scale, label=f"Plate Rs (x{force_scale:.0e})")
        ax.plot(self.soil_deadload * force_scale, label=f"Deadload (x{force_scale:.0e})")
        # Vel cos might have different scale, plot separately or on secondary axis if needed
        ax.plot(self.soil_bucket_vel_cos, label="Bucket Vel Cos", linestyle=':')

        ax.set_xlabel("Time Step")
        ax.set_ylabel(f"Force Magnitude (x{force_scale:.0e} N) / Cos Angle") # Adjust label if using secondary axis
        ax.legend(fontsize='small')
        ax.grid(True, linestyle='--', alpha=0.6)
        self._add_reset_lines(ax)


    def _plot_bucket_vel(self, make_subplot=False, grid_pos=None):
        """Plots bucket linear and angular velocities."""
        if make_subplot and self.gs and grid_pos:
            ax = plt.gcf().add_subplot(self.gs[grid_pos[0], grid_pos[1]])
            ax.set_title("Bucket Velocities")
        else:
            fig, ax = plt.subplots()
            ax.set_title("Bucket Tip Velocities (World Frame)")

        ax.plot(self.bucket_lin_vel_norm, label="Linear Vel Norm")
        ax.plot(self.bucket_lin_vel_w_x, label="Linear Vel X")
        ax.plot(self.bucket_lin_vel_w_z, label="Linear Vel Z")
        # Optionally plot pitch joint velocity for comparison
        # ax.plot(self.j_pitch_lin_vel_w_x, label="Pitch Joint Vel X", linestyle='--')
        # ax.plot(self.j_pitch_lin_vel_w_z, label="Pitch Joint Vel Z", linestyle='--')
        ax.plot(self.bucket_ang_vel_w_y, label="Angular Vel Y (Pitch)")

        ax.set_xlabel("Time Step")
        ax.set_ylabel("Velocity [m/s or rad/s]")
        ax.legend(fontsize='small')
        ax.grid(True, linestyle='--', alpha=0.6)
        self._add_reset_lines(ax)


    def _plot_base_vel(self, make_subplot=False, grid_pos=None):
        """Plots base/root linear and angular velocities."""
        if make_subplot and self.gs and grid_pos:
            ax = plt.gcf().add_subplot(self.gs[grid_pos[0], grid_pos[1]])
            ax.set_title("Base Velocities")
        else:
            fig, ax = plt.subplots()
            ax.set_title("Base Velocities (World Frame)")

        ax.plot(self.base_lin_vel_w_x, label="Base Lin Vel X")
        # ax.plot(self.base_lin_vel_w_y, label="Base Lin Vel Y") # Often zero for fixed base
        ax.plot(self.base_lin_vel_w_z, label="Base Lin Vel Z")
        # ax.plot(self.base_ang_vel_w_x, label="Base Ang Vel X") # Often zero
        ax.plot(self.base_ang_vel_w_y, label="Base Ang Vel Y (Pitch)")
        # ax.plot(self.base_ang_vel_w_z, label="Base Ang Vel Z") # Often zero

        ax.set_xlabel("Time Step")
        ax.set_ylabel("Velocity [m/s or rad/s]")
        ax.legend(fontsize='small')
        ax.grid(True, linestyle='--', alpha=0.6)
        self._add_reset_lines(ax)


    def _plot_rewards(self, make_subplot=False, grid_pos=None):
        """Plots individual reward terms and their sum over time."""
        if make_subplot and self.gs and grid_pos:
            ax = plt.gcf().add_subplot(self.gs[grid_pos[0], grid_pos[1]])
            ax.set_title("Rewards")
        else:
            fig, ax = plt.subplots()
            ax.set_title("Reward Components Over Time")

        total_reward = np.zeros(self.N) * np.nan
        if self.rews: # Check if rewards were logged
            for key, value in self.rews.items():
                 # Check for NaNs before summing, handle potential all-NaN slices
                 valid_mask = ~np.isnan(value)
                 if valid_mask.any():
                     total_reward[valid_mask] = np.nansum([total_reward[valid_mask], value[valid_mask]], axis=0)
                 ax.plot(value, label=key, alpha=0.8) # Use alpha for overlapping lines

            ax.plot(total_reward, label="Summed Reward", color='black', linewidth=1.5, zorder=10)

        ax.set_xlabel("Time Step")
        ax.set_ylabel("Reward Value")
        ax.legend(fontsize='small')
        ax.grid(True, linestyle='--', alpha=0.6)
        self._add_reset_lines(ax)


    def _plot_terminations(self, make_subplot=False, grid_pos=None):
        """Plots a bar chart of termination counts for the last episode."""
        if make_subplot and self.gs and grid_pos:
            ax = plt.gcf().add_subplot(self.gs[grid_pos[0], grid_pos[1]])
            ax.set_title("Terminations")
        else:
            fig, ax = plt.subplots()
            ax.set_title("Termination Counts (Last Episode)")

        labels = []
        values = []
        colors = [] # Assign colors based on positive/negative

        term_data = {**self.terms_neg, **self.terms_pos} # Combine dicts

        for key, value_array in term_data.items():
            # value_array has shape (1,) due to get_log_dict dim=1
            value = value_array[0].item() # Get scalar value
            if value > 0: # Only plot non-zero counts
                labels.append(key.replace("time_out", "timeout")) # Shorten name
                values.append(value)
                # Color bars based on type (optional)
                if key in self.terms_pos:
                    colors.append(colorblind_palette[4]) # Greenish
                else:
                    colors.append(colorblind_palette[5]) # Reddish/Vermillion

        if values: # Check if there's anything to plot
            x_pos = np.arange(len(labels))
            ax.bar(x_pos, values, tick_label=labels, color=colors if colors else colorblind_palette[0])
            ax.tick_params(axis='x', rotation=45, labelsize=8) # Rotate labels if long
            ax.set_ylabel("Count")
        else:
            ax.text(0.5, 0.5, "No terminations recorded", ha='center', va='center', transform=ax.transAxes)

        ax.grid(True, axis='y', linestyle='--', alpha=0.6)


    def _plot_obs(self, make_subplot=False, grid_pos=None):
        """Plots each observation term over time in separate figures or subplots."""
        if make_subplot:
             print("Warning: Plotting all observations as subplots is not recommended due to potential high count.")
             # Implement subplot logic here if truly desired, e.g., create a new figure per obs.
             fig_per_obs = True
        else:
             fig_per_obs = True # Default to one figure per observation term

        for key, value in self.obs.items():
            if fig_per_obs:
                 fig, ax = plt.subplots()
                 ax.set_title(f"Observation: {key}")
            # else: -> subplot logic needed here if make_subplot=True and fig_per_obs=False

            # Plot each dimension of the observation vector if multi-dimensional
            if value.ndim > 1 and value.shape[1] > 1:
                for dim_idx in range(value.shape[1]):
                    ax.plot(value[:, dim_idx], label=f"{key}_{dim_idx}")
            else:
                 # Plot as single line if 1D
                 ax.plot(value, label=key)

            ax.set_xlabel("Time Step")
            ax.set_ylabel("Observation Value")
            if value.ndim > 1 and value.shape[1] > 1: # Only add legend if multiple lines
                 ax.legend(fontsize='small')
            ax.grid(True, linestyle='--', alpha=0.6)
            self._add_reset_lines(ax)

    def _plot_dof_pos(self, make_subplot=False, grid_spec_start_index=None):
        """Plots DOF positions and limits, potentially in subplots."""
        if make_subplot and self.gs and grid_spec_start_index is not None:
            # Plot each DOF in its own subplot within the grid
            fig = plt.gcf()
            for i, name in enumerate(self.joint_names):
                 row = grid_spec_start_index[0] + i // self.gs.ncols
                 col = grid_spec_start_index[1] + i % self.gs.ncols
                 if row < self.gs.nrows and col < self.gs.ncols: # Check bounds
                      ax = fig.add_subplot(self.gs[row, col])
                      ax.set_title(f"{name} Pos", fontsize=10)
                      self._plot_single_dof_pos(i, name, ax)
                 else:
                      print(f"Warning: GridSpec out of bounds for DOF {name} position plot.")
        else:
            # Create separate figures for each DOF
            for i, name in enumerate(self.joint_names):
                fig, ax = plt.subplots()
                ax.set_title(f"DOF Position: {name}")
                self._plot_single_dof_pos(i, name, ax)

    def _plot_single_dof_pos(self, i, name, ax):
        """Helper to plot position for a single DOF on given axes."""
        ax.plot(self.dof_pos[:, i], label=f"{name} Pos")
        # Check if limits exist in env.limits and are valid before plotting
        # Static limits are often stored directly on articulation, e.g., env.robot.dof_pos_limits
        # Dynamic limits might be in env.limits if used. Adapt this logic.
        if hasattr(self.env, 'limits') and hasattr(self.env.limits, 'pos_limits_lower'):
             lower_limit = self.env.limits.pos_limits_lower[i].item()
             upper_limit = self.env.limits.pos_limits_upper[i].item()
             ax.plot(np.ones_like(self.dof_pos[:, i]) * lower_limit, label="Limit Lower", linestyle='--', marker='x', markersize=3, alpha=0.7)
             ax.plot(np.ones_like(self.dof_pos[:, i]) * upper_limit, label="Limit Upper", linestyle='--', marker='x', markersize=3, alpha=0.7)
        # Add dynamic limits if logged and available
        # Example: ax.plot(self.dyn_pos_limit_lower[:, i], label="Dyn Lower", linestyle=':', color='red')

        ax.set_xlabel("Time Step")
        ax.set_ylabel("Position [rad or m]")
        ax.legend(fontsize='small')
        ax.grid(True, linestyle='--', alpha=0.6)
        self._add_reset_lines(ax)

    def _plot_dof_vel(self, make_subplot=False, grid_spec_start_index=None):
        """Plots DOF velocities, limits, and actions."""
        if make_subplot and self.gs and grid_spec_start_index is not None:
            fig = plt.gcf()
            for i, name in enumerate(self.joint_names):
                 row = grid_spec_start_index[0] + i // self.gs.ncols
                 col = grid_spec_start_index[1] + i % self.gs.ncols
                 if row < self.gs.nrows and col < self.gs.ncols:
                      ax = fig.add_subplot(self.gs[row, col])
                      ax.set_title(f"{name} Vel", fontsize=10)
                      self._plot_single_dof_vel(i, name, ax)
                 else:
                      print(f"Warning: GridSpec out of bounds for DOF {name} velocity plot.")
        else:
            for i, name in enumerate(self.joint_names):
                fig, ax = plt.subplots()
                ax.set_title(f"DOF Velocity: {name}")
                self._plot_single_dof_vel(i, name, ax)

    def _plot_single_dof_vel(self, i, name, ax):
        """Helper to plot velocity for a single DOF on given axes."""
        ax.plot(self.dof_vel[:, i], label=f"{name} Vel")

        # Plot limits (check source: env.vel_limits_lower or env.limits.curr_vel_limit_lower?)
        if not np.isnan(self.vel_limit_lower[:, i]).all(): # Check if logged
             ax.plot(self.vel_limit_lower[:, i], label="Limit Lower", linestyle='--', alpha=0.7)
        if not np.isnan(self.vel_limit_upper[:, i]).all():
             ax.plot(self.vel_limit_upper[:, i], label="Limit Upper", linestyle='--', alpha=0.7)
        # Example static limits from articulation:
        # lower_static = self.env.robot.dof_vel_limits[i, 0].item() ... plot ones_like * lower_static

        # Plot actions for comparison
        ax.plot(self.actions[:, i], label="Action Raw", alpha=0.5, linestyle=':')
        ax.plot(self.actions_clipped_scaled[:, i], label="Action Scaled", alpha=0.7)

        ax.set_xlabel("Time Step")
        ax.set_ylabel("Velocity / Action")
        ax.legend(fontsize='small')
        ax.grid(True, linestyle='--', alpha=0.6)
        self._add_reset_lines(ax)

    def _plot_dof_torques(self, make_subplot=False, grid_spec_start_index=None):
        """Plots DOF torques and their components."""
        if make_subplot and self.gs: # Check if gs exists
            fig = plt.gcf()
            # Determine starting position if not provided (e.g., below bucket state)
            if grid_spec_start_index is None:
                # Example: Place below bucket state (row 3, col 0) and in right column (row 0, col 1)
                start_row, start_col = 3, 0
                alt_start_row, alt_start_col = 0, 1
            else:
                start_row, start_col = grid_spec_start_index
                alt_start_row, alt_start_col = start_row, start_col + 1 # Assume space to the right

            for i, name in enumerate(self.joint_names):
                 # Alternate placement between left and right columns if using multi-tester layout
                 current_row = start_row + i // 2 # Two plots per row (left/right)
                 current_col = start_col if i % 2 == 0 else alt_start_col

                 if current_row < self.gs.nrows and current_col < self.gs.ncols:
                      ax = fig.add_subplot(self.gs[current_row, current_col])
                      ax.set_title(f"{name} Torque", fontsize=10)
                      self._plot_single_dof_torque(i, name, ax)
                 else:
                      print(f"Warning: GridSpec out of bounds for DOF {name} torque plot at ({current_row}, {current_col}).")

        else:
            for i, name in enumerate(self.joint_names):
                fig, ax = plt.subplots()
                ax.set_title(f"DOF Torque: {name}")
                self._plot_single_dof_torque(i, name, ax)

    def _plot_single_dof_torque(self, i, name, ax):
        """Helper to plot torque for a single DOF on given axes."""
        ax.plot(self.dof_torque[:, i], label="Applied Torque", linewidth=1.5)

        # Plot limits (check source: env.effort_limits or env.limits.curr_torque_limit_lower?)
        if not np.isnan(self.torque_limit_lower[:, i]).all():
             ax.plot(self.torque_limit_lower[:, i], label="Limit Lower", linestyle='--', alpha=0.7)
        if not np.isnan(self.torque_limit_upper[:, i]).all():
             ax.plot(self.torque_limit_upper[:, i], label="Limit Upper", linestyle='--', alpha=0.7)

        # Plot components (negated, as they oppose the applied torque typically)
        ax.plot(-self.dof_gravity[:, i], label="Gravity Comp.", alpha=0.7)
        ax.plot(-self.dof_inertial[:, i], label="Inertial Comp.", alpha=0.7)
        ax.plot(-self.dof_soil[:, i], label="Soil Comp.", alpha=0.7)

        ax.set_xlabel("Time Step")
        ax.set_ylabel("Torque [Nm]")
        ax.legend(fontsize='small')
        ax.grid(True, linestyle='--', alpha=0.6)
        self._add_reset_lines(ax)

    def _plot_episode_step(self, make_subplot=False, grid_pos=None):
        """Plots the step count within each episode."""
        if make_subplot and self.gs and grid_pos:
            ax = plt.gcf().add_subplot(self.gs[grid_pos[0], grid_pos[1]])
            ax.set_title("Episode Step")
        else:
            fig, ax = plt.subplots()
            ax.set_title("Step within Episode")

        ax.plot(self.episode_steps, label="Episode Step")
        ax.set_xlabel("Total Time Steps")
        ax.set_ylabel("Step Count in Episode")
        ax.grid(True, linestyle='--', alpha=0.6)
        self._add_reset_lines(ax) # Resets are clearly visible here
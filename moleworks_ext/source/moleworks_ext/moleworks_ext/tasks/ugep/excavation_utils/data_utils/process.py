import pandas as pd
import matplotlib.pyplot as plt

# now let's merge all the components that add to the base mass and create two new columns "Overall Base Mass" and "Overall Arm Mass"
# "Overall Base Mass": "Base Mass", "Boom Cyl. Mass", "Op. and Fuel Mass", "Track Shoes Mass", "Counterweight", "Swing Frame", "Undercarriage", "Blades", "Swing Frame".
# "Overall Arm Mass": "Boom Mass", "Dipper Mass", "Shovel Mass", "Quick Coupler Mass"
file_path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/merged_excavator_info_processed.csv'
# load the data
data = pd.read_csv(file_path)

# Calculate the overall base mass by summing the relevant columns
data['Overall Base Mass'] = data[['Base Mass', 'Boom Cyl. Mass', 'Op. and Fuel Mass', 'Track Shoes Mass', 'Counterweight', 'Swing Frame', 'Undercarriage', 'Blades']].sum(axis=1)

# Calculate the overall arm mass by summing the relevant columns
data['Overall Arm Mass'] = data[['Boom Mass', 'Dipper Mass', 'Shovel Mass', 'Quick Coupler Mass']].sum(axis=1)

# save the data to a new csv
data.to_csv('/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/merged_excavator_info_processed.csv', index=False)

# # scatter plots of the overall base mass and overall arm mass
# plt.scatter(data['Overall Base Mass'], data['Overall Arm Mass'])
# plt.xlabel('Overall Base Mass')
# plt.ylabel('Overall Arm Mass')
# plt.show() 
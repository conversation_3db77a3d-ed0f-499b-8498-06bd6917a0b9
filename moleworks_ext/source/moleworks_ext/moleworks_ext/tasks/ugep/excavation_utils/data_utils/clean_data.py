# Removing near duplicates using a 5% tolerance level for each numerical column
# For each column, we'll calculate the tolerance based on 5% of the value
import pandas as pd

# Load the data
file_path = '/mnt/data/excavators_data_minimal.csv'
excavator_data = pd.read_csv(file_path)

numerical_columns = excavator_data.select_dtypes(include=['float64', 'int64']).columns

def remove_near_duplicates(df, tolerance_percentage=5):
    # Create a boolean mask to keep rows
    keep_mask = pd.Series([True] * len(df))
    
    for i in range(len(df)):
        # If this row is already marked as False, skip it
        if not keep_mask[i]:
            continue
        
        # Get the current row to compare with the others
        current_row = df.iloc[i]
        
        for j in range(i + 1, len(df)):
            # Skip already removed rows
            if not keep_mask[j]:
                continue
            
            # Calculate the differences
            differences = abs((df.iloc[j][numerical_columns] - current_row[numerical_columns]) / current_row[numerical_columns])
            
            # Check if all differences are within the tolerance
            if all(differences <= tolerance_percentage / 100):
                # Mark the row as False if it's considered a near duplicate
                keep_mask[j] = False
                
    return df[keep_mask]

# Apply the function to remove near duplicates
cleaned_data = remove_near_duplicates(excavator_data)

# Saving the cleaned data
cleaned_file_path_v2 = '/mnt/data/excavators_data_cleaned_v2.csv'
cleaned_data.to_csv(cleaned_file_path_v2, index=False)

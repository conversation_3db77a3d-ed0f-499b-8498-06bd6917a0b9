import os

def change_usd_names(root_folder, old_name, new_name):
    for dirpath, dirnames, filenames in os.walk(root_folder):
        for filename in filenames:
            if filename.endswith(".usd") and old_name in filename:
                old_file_path = os.path.join(dirpath, filename)
                new_file_path = os.path.join(dirpath, filename.replace(old_name, new_name))
                os.rename(old_file_path, new_file_path)
                print(f'Renamed: {old_file_path} to {new_file_path}')

# Usage
root_folder = "/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_assets/data/Robots/Gravis/cat_and_m545_linear"
old_name = "m545_fixed_tele_narrow_merged_modified_no_visual.usd"
new_name = "linear.usd"

change_usd_names(root_folder, old_name, new_name)

old_name_2 = "cat323_rl_merged_modified_no_visual.usd"
new_name_2 = "linear.usd"

change_usd_names(root_folder, old_name_2, new_name_2)
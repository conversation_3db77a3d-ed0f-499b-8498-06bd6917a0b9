import fitz  # PyMuPDF
import camelot
import pandas as pd
import re

# Load the CSV file (this is the file you uploaded with excavator data)
csv_file = "/mnt/data/excavators_data_with_all_configurations.csv"
df = pd.read_csv(csv_file)


# Function to extract text from a PDF file using PyMuPDF
def extract_text_from_pdf(pdf_path):
    text = ""
    with fitz.open(pdf_path) as doc:
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text += page.get_text("text")
    return text


# Function to extract tables using Camelot
def extract_tables_from_pdf(pdf_path):
    tables = camelot.read_pdf(pdf_path, pages="all", strip_text="\n")
    return tables


# Function to search for dimension and mass data in extracted text
def find_dimensions_and_masses(text):
    dimensions = {}

    # Regular expressions for finding dimensions and masses
    patterns = {
        'operating_weight': r"Operating weight\s*(\d+[\.,]?\d*\s?kg)",
        'bucket_capacity': r"Bucket capacity\s*(\d+[\.,]?\d*\s?m³)",
        'undercarriage_length': r"Undercarriage length\s*(\d+[\.,]?\d*\s?mm)",
        'track_pad_width': r"Track pad width\s*(\d+[\.,]?\d*\s?mm)",
        'uppercarriage_width': r"Uppercarriage width\s*(\d+[\.,]?\d*\s?mm)"
    }

    # Apply regex search
    for key, pattern in patterns.items():
        match = re.search(pattern, text)
        if match:
            dimensions[key] = match.group(1).replace(",", "")

    return dimensions


# Path to the PDFs
pdf_files = [
    "/mnt/data/NTB_A910Compact_G6_AGSV-Tier4f_enGB.pdf",
    "/mnt/data/R926-G7-StageIIIA-EN-PI-2024-02.pdf"
]

# Iterate through each PDF and extract text and tables
all_data = []

for pdf_file in pdf_files:
    print(f"Processing {pdf_file}...")

    # Extract text from the PDF
    text = extract_text_from_pdf(pdf_file)

    # Extract dimensions and masses from text
    dimensions_and_masses = find_dimensions_and_masses(text)
    print(f"Extracted dimensions and masses: {dimensions_and_masses}")

    # Extract tables from the PDF using Camelot
    tables = extract_tables_from_pdf(pdf_file)

    for i, table in enumerate(tables):
        print(f"Extracted table {i+1} from {pdf_file}")
        print(table.df)  # Print the extracted table (can be further processed)

    # Store extracted data for later use
    all_data.append(dimensions_and_masses)

# Convert the extracted data to a DataFrame
df_extracted = pd.DataFrame(all_data)

# Optionally, merge the extracted data with the CSV data if needed
# df_final = df.merge(df_extracted, on='some_key_column', how='left')

# Save the final data to a new CSV file
output_csv_path = "/mnt/data/excavators_extracted_data.csv"
df_extracted.to_csv(output_csv_path, index=False)

print(f"Extraction complete. Data saved to {output_csv_path}.")

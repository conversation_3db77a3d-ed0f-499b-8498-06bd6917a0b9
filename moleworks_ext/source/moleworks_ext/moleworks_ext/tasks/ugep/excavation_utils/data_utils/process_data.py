import pandas as pd
import numpy as np

# Load the data from the provided file
file_path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/merged_excavator_info.ods'
data = pd.read_excel(file_path, engine='odf')

print("Original data columns: ", data.columns)

# Define component prefixes
component_prefixes = ['Track Shoes Mass', 'Counterweight', 'Swing Frame', 'Boom Mass', 'Dipper Mass', 'Shovel Mass', 'Undercarriage', 'Quick Coupler Mass', 'Blades']

# Calculate average for each component and remove individual columns
for prefix in component_prefixes:
    cols = [col for col in data.columns if col.startswith(prefix)]
    if cols:
        data[prefix] = data[cols].mean(axis=1)
        data = data.drop(columns=cols)

# Define base mass components and arm mass components
base_mass_components = ['Base Mass', 'Boom Cyl. Mass', 'Op. and Fuel Mass', 'Track Shoes Mass', 'Counterweight', 'Swing Frame', 'Undercarriage', 'Blades']
arm_mass_components = ['Boom Mass', 'Dipper Mass', 'Shovel Mass', 'Quick Coupler Mass']

# Calculate the overall base mass and arm mass using only existing columns
data['Overall Base Mass'] = data[[col for col in base_mass_components if col in data.columns]].sum(axis=1)
data['Overall Arm Mass'] = data[[col for col in arm_mass_components if col in data.columns]].sum(axis=1)

print("Final data columns: ", data.columns)

# Save the data to a new csv
data.to_csv('/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/excavators_data_v2.csv', index=False)

# minimal sheet keys 
minimal_sheet_keys = ['Brand_x', 'Class_x', 'Name', 'Base Mass', 'Boom Mass', 'Dipper Mass', 'Shovel Mass', 'Boom Length', 'Dipper Length', 'Shovel Length', 'Engine Power', 'Max Reach Ground', 'Track Length', 'Bucket Capacity']

# Create a new dataframe with only the minimal sheet keys
minimal_data = data[minimal_sheet_keys].copy()

# Save the minimal data to a new csv
minimal_data.to_csv('/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/excavators_data_minimal.csv', index=False)

print("Minimal data columns: ", minimal_data.columns)


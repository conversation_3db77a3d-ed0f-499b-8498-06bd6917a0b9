from itertools import product
from tqdm import tqdm

import pandas as pd

# Load the data from the provided file
file_path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/merged_excavator_info.ods'
data = pd.read_excel(file_path, engine='odf')


# Function to generate all possible configurations and reshape data
def reshape_longitudinal_all_configs(df, column_prefixes):
    # Extract identifier columns
    id_cols = ['Brand_x', 'Class_x', 'Name']
    other_cols = [col for col in df.columns if col not in id_cols and not any(col.startswith(prefix) for prefix in column_prefixes)]
    identifiers = df[id_cols].drop_duplicates().reset_index(drop=True)

    # Dictionary to hold all configurations for each component
    components_dict = {prefix: df.filter(like=prefix).dropna(axis=1).columns.tolist() for prefix in column_prefixes}

    # Cartesian product of all configurations
    all_combinations = list(product(*components_dict.values()))
    print("num of combinations: ", len(all_combinations))

    # Prepare the final DataFrame
    long_df = pd.DataFrame()

    for combo in tqdm(all_combinations, desc="Processing combinations"):
        temp_df = df[id_cols + other_cols].copy()
        config = {}
        
        for col in combo:
            component = next(prefix for prefix in column_prefixes if col.startswith(prefix))
            temp_df[component] = pd.to_numeric(df[col], errors='coerce')
            config[component] = col.replace(component, '').strip()
        
        temp_df['Configuration'] = str(config)
        long_df = pd.concat([long_df, temp_df], ignore_index=True)
    
    return long_df


# List of component prefixes based on column names seen
component_prefixes = ['Track Shoes Mass', 'Counterweight', 'Swing Frame', 'Boom Mass', 'Dipper Mass', 'Shovel Mass', 'Undercarriage', 'Quick Coupler Mass', 'Blades']

# Reshape data into longitudinal format with all configurations
longitudinal_data_all_configs = reshape_longitudinal_all_configs(data, component_prefixes)
print(longitudinal_data_all_configs.head())
longitudinal_data_all_configs.to_csv(file_path.replace('.ods', '_processed.csv'))
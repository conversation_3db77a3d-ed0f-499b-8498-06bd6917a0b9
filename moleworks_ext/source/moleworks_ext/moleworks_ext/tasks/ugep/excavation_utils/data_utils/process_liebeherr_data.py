import os
import pandas as pd
import tabula

# Specify the directory containing PDF files
pdf_dir = '/home/<USER>/Downloads/liebeherr_excavators'

# Initialize an empty list to store dataframes
data_frames = []

# Iterate over all PDF files in the directory
for file in os.listdir(pdf_dir):
    if file.endswith('.pdf'):
        file_path = os.path.join(pdf_dir, file)
        
        # Extract tables from PDF using tabula
        tables = tabula.read_pdf(file_path, pages='all', multiple_tables=True)
        
        # Print file name and number of tables detected
        print(f"File: {file}")
        print(f"Number of tables detected: {len(tables)}")
        
        # Iterate over extracted tables and append to data_frames list
        for i, df in enumerate(tables):
            print(f"\nTable {i+1}:")
            print(df.head())  # Print first few rows of each table
            print(f"Columns: {df.columns.tolist()}")  # Print column names
            
            # Clean and preprocess dataframe as needed
            data_frames.append(df)

# Combine all dataframes into a single dataframe
combined_df = pd.concat(data_frames, ignore_index=True)

# Print combined dataframe info
print("\nCombined DataFrame:")
print(combined_df.info())

# Instead of assuming column names, print all column names
print("\nAll columns in combined DataFrame:")
print(combined_df.columns.tolist())

# Comment out the final dataframe creation and CSV export for now
# final_df = combined_df[['Dimension Column', 'Mass Column']].rename(columns={
#     'Dimension Column': 'Dimension',
#     'Mass Column': 'Mass'
# })
# final_df.to_csv('liebeherr_excavator_dimensions_masses.csv', index=False)
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.mixture import GaussianMixture

# Load the data
file_path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/merged_excavator_info_processed.csv'
merged_data = pd.read_csv(file_path)


# Fit a GMM for the 'Reach' variable
def fit_gmm(data, n_components=3):
    gmm = GaussianMixture(n_components=n_components, random_state=0)
    gmm.fit(data)
    return gmm


def plot_gmm_1d(gmm, data, title=''):
    # Generate a range of values for the main variable based on its observed range
    x = np.linspace(data.min(), data.max(), 1000).reshape(-1, 1)
    # Calculate the log probabilities and corresponding densities
    logprob = gmm.score_samples(x)
    pdf = np.exp(logprob)

    plt.figure(figsize=(10, 6))
    plt.hist(data, bins=30, density=True, alpha=0.6, color='g', edgecolor='black')
    plt.plot(x, pdf, '-k', lw=2)
    plt.title(title)
    plt.xlabel('Value')
    plt.ylabel('Density')
    plt.show()


# Function to plot the results, adjusted to handle two-dimensional data
def plot_gmm(gmm, data, reach_data, title=''):
    # Generate a range of values for the main variable based on its observed range
    x = np.linspace(data.min(), data.max(), 1000)
    # Repeat the mean of reach_data for pairing with each x value for conditional modeling
    x_full = np.column_stack((x, np.full_like(x, np.mean(reach_data))))
    # Calculate the log probabilities and corresponding densities
    logprob = gmm.score_samples(x_full)
    pdf = np.exp(logprob)
    
    plt.figure(figsize=(10, 6))
    plt.hist(data, bins=30, density=True, alpha=0.6, color='g', edgecolor='black')
    plt.plot(x, pdf, '-k', lw=2)
    plt.title(title)
    plt.xlabel('Value')
    plt.ylabel('Density')
    plt.show()


# Fit and plot GMM for 'Max Reach Ground'
reach = merged_data['Max Reach Ground'].values.reshape(-1, 1)
gmm_reach = fit_gmm(reach)
plot_gmm_1d(gmm_reach, reach.flatten(), 'Max Reach Ground')

# Conditional GMM for other variables based on 'Reach'
variables = ['Engine Power', 'Boom Length', 'Dipper Length', 'Shovel Length', 'Bucket Capacity', 'Base Mass', 'Track Length', 'Boom Mass', 'Dipper Mass', 'Shovel Mass']
gmm_conditionals = {}

# Fit conditional GMMs: one for each variable conditioned on 'Reach'
for variable in variables:
    data = merged_data[[variable, 'Max Reach Ground']].values
    gmm = GaussianMixture(n_components=3, random_state=0)
    gmm.fit(data)
    gmm_conditionals[variable] = gmm
    # Plot using both the variable data and 'Reach' for correct conditional handling
    plot_gmm(gmm, data[:, 0], data[:, 1], variable)

# Sampling new data points based on the reach
n_samples = 1000
new_reach_samples = gmm_reach.sample(n_samples)[0]


# Generate conditional samples for each variable based on new 'Reach' samples
new_samples = {'Max Reach Ground': new_reach_samples.flatten()}
for variable, gmm in gmm_conditionals.items():
    # Create a 2D array with new 'Reach' samples and a placeholder for the variable
    new_data = np.column_stack((np.zeros_like(new_reach_samples), new_reach_samples))
    # Determine which GMM component each new reach sample is most likely to belong to
    component_assignments = gmm.predict(new_data)
    conditional_samples = []
    for i in range(n_samples):
        component = component_assignments[i]
        mean = gmm.means_[component]
        cov = gmm.covariances_[component]
        # Sample from the conditional distribution
        sample = np.random.multivariate_normal(mean, cov)
        conditional_samples.append(sample[0])  # Assuming the first dimension is the variable of interest
    new_samples[variable] = conditional_samples

# Combine the samples into a DataFrame
new_samples_df = pd.DataFrame(new_samples)
print(new_samples_df.head())
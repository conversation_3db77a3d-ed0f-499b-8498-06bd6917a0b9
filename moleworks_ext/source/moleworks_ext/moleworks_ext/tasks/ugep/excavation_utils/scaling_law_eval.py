import pandas as pd
import scipy.stats as stats
import numpy as np
import os
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
from sklearn.preprocessing import PolynomialFeatures
from sklearn.tree import DecisionTreeRegressor
from sklearn.ensemble import RandomForestRegressor


# Load and preprocess the data
def load_and_preprocess_data(path):
    data_sheet1 = pd.read_excel(path, sheet_name='Sheet1')
    data_sheet2 = pd.read_excel(path, sheet_name='Sheet2')
    merged_data = pd.merge(data_sheet1, data_sheet2, on='Name', how='left')
    merged_data = merged_data.apply(pd.to_numeric, errors='coerce')
    merged_data.fillna(merged_data.mean(), inplace=True)
    return merged_data


# Fit linear regression models
def fit_linear_model(x, y):
    model = LinearRegression()
    if len(x.shape) == 1:
        x = x.reshape(-1, 1)  # Reshape if only one feature is provided
    model.fit(x, y)
    return model


# Evaluate model
def evaluate_model(model, x, y_actual):
    if len(x.shape) == 1:
        x = x.reshape(-1, 1)  # Reshape if only one feature is provided
    y_predicted = model.predict(x)
    r2 = r2_score(y_actual, y_predicted)
    return y_predicted, r2


# Sample from Random Forest model
def sample_from_forest_model(model, data):
    tree_predictions = get_tree_predictions(model, data)
    random_indices = np.random.randint(0, len(model.estimators_), size=data.shape[0])
    return tree_predictions[random_indices, np.arange(data.shape[0])]


# Get predictions from each tree in the Random Forest
def get_tree_predictions(model, data):
    return np.array([tree.predict(data) for tree in model.estimators_])


# Plot predicted vs actual values
def plot_predicted_vs_actual(actual, predicted, title):
    plt.figure(figsize=(10, 6))
    plt.scatter(actual, predicted, edgecolors=(0, 0, 0))
    plt.plot([actual.min(), actual.max()], [actual.min(), actual.max()], 'k--', lw=2)
    plt.xlabel('Actual')
    plt.ylabel('Predicted')
    plt.title(title)
    plt.show()


# Load data
# path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/excavator_info.xlsx'
# merged_data = load_and_preprocess_data(path)
# Load already merged data
file_path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/merged_excavator_info_processed.csv'
merged_data = pd.read_csv(file_path)

reach = merged_data['Max Reach Ground'].values

# Fit and evaluate linear models
engine_power_model = fit_linear_model(reach, merged_data['Engine Power'].values)
boom_length_model = fit_linear_model(reach, merged_data['Boom Length'].values)
dipper_length_model = fit_linear_model(reach, merged_data['Dipper Length'].values)
shovel_length_model = fit_linear_model(reach, merged_data['Shovel Length'].values)
bucket_capacity_model = fit_linear_model(merged_data[['Engine Power', 'Max Reach Ground']].values, merged_data['Bucket Capacity'].values)
base_mass_model = fit_linear_model(reach, merged_data['Base Mass'].values)
track_length_model = fit_linear_model(merged_data[['Max Reach Ground', 'Base Mass']].values, merged_data['Track Length'].values)
boom_mass_model = fit_linear_model(reach, merged_data['Boom Mass'].values)
dipper_mass_model = fit_linear_model(reach, merged_data['Dipper Mass'].values)
shovel_mass_model = fit_linear_model(reach, merged_data['Shovel Mass'].values)

predicted_engine_power, r2_engine_power = evaluate_model(engine_power_model, reach, merged_data['Engine Power'])
predicted_boom_length, r2_boom_length = evaluate_model(boom_length_model, reach, merged_data['Boom Length'])
predicted_dipper_length, r2_dipper_length = evaluate_model(dipper_length_model, reach, merged_data['Dipper Length'])
predicted_shovel_length, r2_shovel_length = evaluate_model(shovel_length_model, reach, merged_data['Shovel Length'])
predicted_bucket_capacity, r2_bucket_capacity = evaluate_model(bucket_capacity_model, merged_data[['Engine Power', 'Max Reach Ground']].values, merged_data['Bucket Capacity'])
predicted_base_mass, r2_base_mass = evaluate_model(base_mass_model, reach, merged_data['Base Mass'])
predicted_track_length, r2_track_length = evaluate_model(track_length_model, merged_data[['Max Reach Ground', 'Base Mass']].values, merged_data['Track Length'])
predicted_boom_mass, r2_boom_mass = evaluate_model(boom_mass_model, reach, merged_data['Boom Mass'])
predicted_dipper_mass, r2_dipper_mass = evaluate_model(dipper_mass_model, reach, merged_data['Dipper Mass'])
predicted_shovel_mass, r2_shovel_mass = evaluate_model(shovel_mass_model, reach, merged_data['Shovel Mass'])

print(f"R-squared for Engine Power: {r2_engine_power}")
print(f"R-squared for Boom Length: {r2_boom_length}")
print(f"R-squared for Dipper Length: {r2_dipper_length}")
print(f"R-squared for Shovel Length: {r2_shovel_length}")
print(f"R-squared for Bucket Capacity: {r2_bucket_capacity}")
print(f"R-squared for Base Mass: {r2_base_mass}")
print(f"R-squared for Track Length: {r2_track_length}")
print(f"R-squared for Boom Mass: {r2_boom_mass}")
print(f"R-squared for Dipper Mass: {r2_dipper_mass}")
print(f"R-squared for Shovel Mass: {r2_shovel_mass}")



# Plot predicted vs actual values
plot_predicted_vs_actual(merged_data['Engine Power'], predicted_engine_power, 'Engine Power')
plot_predicted_vs_actual(merged_data['Boom Length'], predicted_boom_length, 'Boom Length')
plot_predicted_vs_actual(merged_data['Dipper Length'], predicted_dipper_length, 'Dipper Length')
plot_predicted_vs_actual(merged_data['Shovel Length'], predicted_shovel_length, 'Shovel Length')
plot_predicted_vs_actual(merged_data['Bucket Capacity'], predicted_bucket_capacity, 'Bucket Capacity')
plot_predicted_vs_actual(merged_data['Base Mass'], predicted_base_mass, 'Base Mass')
plot_predicted_vs_actual(merged_data['Track Length'], predicted_track_length, 'Track Length')
plot_predicted_vs_actual(merged_data['Boom Mass'], predicted_boom_mass, 'Boom Mass')
plot_predicted_vs_actual(merged_data['Dipper Mass'], predicted_dipper_mass, 'Dipper Mass')
plot_predicted_vs_actual(merged_data['Shovel Mass'], predicted_shovel_mass, 'Shovel Mass')


# Train and evaluate Random Forest models
engine_power_forest_model = RandomForestRegressor(n_estimators=100)
engine_power_forest_model.fit(reach.reshape(-1, 1), merged_data['Engine Power'].values)

boom_length_forest_model = RandomForestRegressor(n_estimators=100)
boom_length_forest_model.fit(reach.reshape(-1, 1), merged_data['Boom Length'].values)

dipper_length_forest_model = RandomForestRegressor(n_estimators=100)
dipper_length_forest_model.fit(reach.reshape(-1, 1), merged_data['Dipper Length'].values)

shovel_length_forest_model = RandomForestRegressor(n_estimators=100)
shovel_length_forest_model.fit(reach.reshape(-1, 1), merged_data['Shovel Length'].values)

bucket_capacity_model = RandomForestRegressor(n_estimators=100)
bucket_capacity_model.fit(merged_data[['Engine Power', 'Max Reach Ground']], merged_data['Bucket Capacity'])

import joblib

base_mass_model = RandomForestRegressor(n_estimators=100)
base_mass_model.fit(reach.reshape(-1, 1), merged_data['Base Mass'])

# Save the model to disk
joblib.dump(base_mass_model, 'base_mass_model.pkl')

# Load the model from disk for inference
base_mass_model = joblib.load('base_mass_model.pkl')

track_length_model = RandomForestRegressor(n_estimators=100)
track_length_model.fit(merged_data[['Max Reach Ground', 'Base Mass']], merged_data['Track Length'])

boom_mass_model = RandomForestRegressor(n_estimators=100)
boom_mass_model.fit(reach.reshape(-1, 1), merged_data['Boom Mass'])

dipper_mass_model = RandomForestRegressor(n_estimators=100)
dipper_mass_model.fit(reach.reshape(-1, 1), merged_data['Dipper Mass'])

shovel_mass_model = RandomForestRegressor(n_estimators=100)
shovel_mass_model.fit(reach.reshape(-1, 1), merged_data['Shovel Mass'])

# Evaluate Random Forest models and print R-squared values
r2_engine_power_forest = r2_score(merged_data['Engine Power'].values, engine_power_forest_model.predict(reach.reshape(-1, 1)))
r2_boom_length_forest = r2_score(merged_data['Boom Length'].values, boom_length_forest_model.predict(reach.reshape(-1, 1)))
r2_dipper_length_forest = r2_score(merged_data['Dipper Length'].values, dipper_length_forest_model.predict(reach.reshape(-1, 1)))
r2_shovel_length_forest = r2_score(merged_data['Shovel Length'].values, shovel_length_forest_model.predict(reach.reshape(-1, 1)))
r2_bucket_capacity_forest = r2_score(merged_data['Bucket Capacity'].values, bucket_capacity_model.predict(merged_data[['Engine Power', 'Max Reach Ground']]))
r2_base_mass_forest = r2_score(merged_data['Base Mass'].values, base_mass_model.predict(reach.reshape(-1, 1)))
r2_track_length_forest = r2_score(merged_data['Track Length'].values, track_length_model.predict(merged_data[['Max Reach Ground', 'Base Mass']]))
r2_boom_mass_forest = r2_score(merged_data['Boom Mass'].values, boom_mass_model.predict(reach.reshape(-1, 1)))
r2_dipper_mass_forest = r2_score(merged_data['Dipper Mass'].values, dipper_mass_model.predict(reach.reshape(-1, 1)))
r2_shovel_mass_forest = r2_score(merged_data['Shovel Mass'].values, shovel_mass_model.predict(reach.reshape(-1, 1)))

print(f"R-squared for Engine Power (Forest): {r2_engine_power_forest}")
print(f"R-squared for Boom Length (Forest): {r2_boom_length_forest}")
print(f"R-squared for Dipper Length (Forest): {r2_dipper_length_forest}")
print(f"R-squared for Shovel Length (Forest): {r2_shovel_length_forest}")
print(f"R-squared for Bucket Capacity (Forest): {r2_bucket_capacity_forest}")
print(f"R-squared for Base Mass (Forest): {r2_base_mass_forest}")
print(f"R-squared for Track Length (Forest): {r2_track_length_forest}")
print(f"R-squared for Boom Mass (Forest): {r2_boom_mass_forest}")
print(f"R-squared for Dipper Mass (Forest): {r2_dipper_mass_forest}")
print(f"R-squared for Shovel Mass (Forest): {r2_shovel_mass_forest}")


# Sample from all models
def sample_all_models(num_samples=10):
    reach_samples = np.random.choice(reach, size=num_samples).reshape(-1, 1)

    # Generate samples for models that require multiple features
    bucket_capacity_samples = np.column_stack((
        np.random.choice(merged_data['Engine Power'].values, size=num_samples),
        reach_samples.flatten()
    ))

    track_length_samples = np.column_stack((
        reach_samples.flatten(),
        np.random.choice(merged_data['Base Mass'].values, size=num_samples)
    ))

    return {
        "Bucket Capacities": sample_from_forest_model(bucket_capacity_model, bucket_capacity_samples),
        "Base Masses": sample_from_forest_model(base_mass_model, reach_samples),
        "Track Lengths": sample_from_forest_model(track_length_model, track_length_samples),
        "Boom Masses": sample_from_forest_model(boom_mass_model, reach_samples),
        "Dipper Masses": sample_from_forest_model(dipper_mass_model, reach_samples),
        "Shovel Masses": sample_from_forest_model(shovel_mass_model, reach_samples)
    }

# Example usage
sampled_values = sample_all_models(num_samples=10)
print("Sampled Values:", sampled_values)
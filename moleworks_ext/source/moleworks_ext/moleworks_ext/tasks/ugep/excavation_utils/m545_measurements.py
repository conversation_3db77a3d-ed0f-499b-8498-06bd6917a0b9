# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
This class measurements buffers of the excavation agent that gets updated every steps/ inter-decim steps.
"""
import numpy as np
import os
import pickle
import re
import torch
import yaml
from tqdm import tqdm

# With these imports
import isaacsim.core.utils.torch as torch_utils
from moleworks_ext.tasks.ugep.excavation_utils.general_excavator import (
    ASSETS_DIR,
    USD_PATHS,
)

import isaaclab.utils.math as math_utils
from isaaclab.utils.math import quat_from_euler_xyz

# For helpers, you'll need to update to the corresponding path in the new structure
# Based on your codebase organization, this is likely:
from moleworks_ext.tasks.ugep.excavation_utils import helpers


class M545Measurements:
    def __init__(self, asset, num_envs, excavation_env):
        # Asset to derive all necessary measuremnts buffers
        self.asset = asset
        self.num_different_assets = len(excavation_env.cfg.scene.robot.spawn.assets_cfg)
        print("num_different_assets: ", self.num_different_assets)
        self.num_envs = num_envs
        self.num_dofs = asset.num_joints
        self.env = excavation_env
        self.cfg = excavation_env.cfg
        self.device = excavation_env.device

        # Make arm joints optional: set to empty if not defined.
        if hasattr(self.cfg, "arm_joints_names") and self.cfg.arm_joints_names:
            self.arm_joints_names = self.cfg.arm_joints_names
            self.num_arm_dofs = len(self.arm_joints_names)
        else:
            self.arm_joints_names = []
            self.num_arm_dofs = 0

        self.gravity = torch.tensor([0, 0, -9.81], device=self.device)
        self.gravity_expanded = self.gravity.view(1, 3)

        self.masses_batched = self.asset.root_physx_view.get_masses().to(self.device)
        # m545:  torch.tensor([10490.0020,  1400.0000,  1222.4600,   526.4620,   993.0000],device=self.device)
        self.base_masses = self.asset.root_physx_view.get_masses()[:, 0].to(self.device)
        # self.masses_expanded = self.masses.view(self.asset.num_bodies, 1)
        # TODO: find spot in the cfg to list all the refs
        self.base_length = torch.zeros(self.num_envs, 1, device=self.device)
        # Inertias
        inertias_batched = self.asset.root_physx_view.get_inertias().to(self.device)
        self.inertias_batched = torch.reshape(
            inertias_batched, (self.num_envs, self.asset.num_bodies, 3, 3)
        )
        # self.inertias = torch.reshape(self.inertias_batched[0], (self.asset.num_bodies, 3, 3))
        # ugep specific
        self.arm_scale = torch.zeros(num_envs, 1, device=self.device)
        self.z_degree = self.env.cfg.reset.z_degree
        self.alpha_degree = self.env.cfg.reset.alpha_degree
        # Fill all the necessary buffers with zeros, cyclical spawning

        # TODO: put in config, tele = 1
        # just to log
        self.base_friction_tau = torch.zeros(num_envs, 3, device=self.device)
        self.base_lift_up_tau = torch.zeros(num_envs, 3, device=self.device)

        # metadata: torque limits and transform
        # TODO: self.files could be removed?
        self.model_coefficients_files = []
        self.torque_limits_files = []
        self.scale_factors_files = []

        self.torque_coeff_degree = 0
        self.load_metadata()
        self.torque_coeffs = torch.zeros(
            self.num_envs, self.torque_coeff_degree + 1, 3, device=self.device
        )

        # TODO: maybe conditioned in an the actual spawning function that is used
        self.last_env_idx = torch.linspace(
            0, num_envs, self.num_different_assets + 1, dtype=torch.int64
        )
        self.load_specs()
        self.read_transforms()
        # Move these up so they exist before init_measurements_buffers (which calls compute_torque_limits)
        self.force_limits = 1000000 * torch.ones(self.num_envs, 3, device=self.device)
        self.torque_factors = torch.zeros(self.num_envs, 3, device=self.device)
        self.torque_limits = torch.zeros(self.num_envs, 3, device=self.device)
        self.init_measurements_buffers()

        # Bucket body (optional)
        if hasattr(self.cfg, "ee_body_name") and self.cfg.ee_body_name:
            self.bucket_body_idx = self.asset.find_bodies(self.cfg.ee_body_name)[0]
        else:
            self.bucket_body_idx = None

    def load_metadata(self):
        # iterate over all subdirectories
        for subdir, dirs, _ in os.walk(ASSETS_DIR):
            model_coefficients_path = os.path.join(
                subdir, self.env.cfg.asset_metadata.model_coefficients_name
            )
            torque_limits_path = os.path.join(
                subdir, self.env.cfg.asset_metadata.torque_limits_name
            )
            scale_factors_path = os.path.join(
                subdir, self.env.cfg.asset_metadata.scale_factors_name
            )

            if os.path.isfile(model_coefficients_path):
                self.model_coefficients_files.append(model_coefficients_path)
            if os.path.isfile(torque_limits_path):
                self.torque_limits_files.append(torque_limits_path)
            if os.path.isfile(scale_factors_path):
                self.scale_factors_files.append(scale_factors_path)

        # Function to extract numbers from the path and convert to integer
        def sort_key(path):
            numbers = re.findall(r"\d+", path)
            return int(numbers[-1]) if numbers else None

        # Sort the lists based on numerical part extracted from filenames
        self.model_coefficients_files.sort(key=sort_key)
        self.torque_limits_files.sort(key=sort_key)
        self.scale_factors_files.sort(key=sort_key)

        # print all the lists
        print(f"Model coefficients files: {self.model_coefficients_files}")
        print(f"Torque limits files: {self.torque_limits_files}")
        print(f"Scale factors files: {self.scale_factors_files}")

        # get the polinomial degree for the torque limits
        torque_limits_path = self.torque_limits_files[0]
        with open(torque_limits_path) as file:
            # Replacing tabs with spaces in the file content before loading
            file_content = file.read().replace("\t", "    ")
            torque_limits_corrected = yaml.safe_load(file_content)

            # Find the maximum length of coefficients
            self.torque_coeff_degree = (
                max(
                    len(limits["coeffs"]) for limits in torque_limits_corrected.values()
                )
                - 1
            )

    def load_specs(self):
        # Batched limits initialization
        self.x_min = torch.zeros(self.num_envs, 1, device=self.device)
        self.x_max = torch.zeros(self.num_envs, 1, device=self.device)
        self.z_min_coeff = torch.zeros(
            self.num_envs, self.z_degree + 1, device=self.device
        )
        self.z_max_coeff = torch.zeros(
            self.num_envs, self.z_degree + 1, device=self.device
        )
        self.alpha_min_coeff = torch.zeros(
            self.num_envs,
            int((self.alpha_degree + 1) * (self.alpha_degree + 2) / 2),
            device=self.device,
        )
        self.alpha_max_coeff = torch.zeros(
            self.num_envs,
            int((self.alpha_degree + 1) * (self.alpha_degree + 2) / 2),
            device=self.device,
        )
        self.force_min = torch.zeros(self.num_envs, 3, device=self.device)
        self.force_max = torch.zeros(self.num_envs, 3, device=self.device)
        self.tau_min = torch.zeros(self.num_envs, self.num_dofs, device=self.device)
        self.tau_max = torch.zeros(self.num_envs, self.num_dofs, device=self.device)
        # r, b, a
        self.bucket_dims = torch.zeros(self.num_envs, 3, device=self.device)
        # only x and z
        self.bucket_com = torch.zeros(self.num_envs, 2, device=self.device)

        for i in tqdm(range(self.num_different_assets), desc="Loading limits"):
            start_idx = self.last_env_idx[i].item()  # Start index for this excavator
            end_idx = self.last_env_idx[i + 1].item()  # End index for this excavator

            # Load machinfillinge specs for this excavator
            model_coefficients_path = self.model_coefficients_files[i]
            model_coefficients = helpers.load_model_coefficients(
                model_coefficients_path
            )  # Ensure the data is assigned correctly with the right shapes and on the intended device
            # Convert numpy arrays to tensors and load them into the class attributes
            self.x_min[start_idx:end_idx] = (
                torch.tensor(model_coefficients["x_min"], device=self.device).float()
                * self.env.cfg.reset.x_min_factor
            )
            self.x_max[start_idx:end_idx] = (
                torch.tensor(model_coefficients["x_max"], device=self.device).float()
                * self.env.cfg.reset.x_max_factor
            )
            self.arm_scale[start_idx:end_idx] = torch.tensor(
                model_coefficients["x_max"]
                / self.env.cfg.reference_values.ref_arm_reach,
                device=self.device,
            ).float()
            self.z_min_coeff[start_idx:end_idx] = torch.tensor(
                model_coefficients["z_min"], device=self.device
            ).float()
            self.z_max_coeff[start_idx:end_idx] = torch.tensor(
                model_coefficients["z_max"], device=self.device
            ).float()
            self.alpha_min_coeff[start_idx:end_idx] = torch.tensor(
                model_coefficients["alpha_min"], device=self.device
            ).float()
            self.alpha_max_coeff[start_idx:end_idx] = torch.tensor(
                model_coefficients["alpha_max"], device=self.device
            ).float()

            # Load torque limits for this excavator
            torque_limits_path = self.torque_limits_files[i]
            with open(torque_limits_path) as file:
                # Replacing tabs with spaces in the file content before loading
                file_content = file.read().replace("\t", "    ")
                torque_limits_corrected = yaml.safe_load(file_content)

                # Find the maximum length of coefficients
                self.torque_coeff_degree = (
                    max(
                        len(limits["coeffs"])
                        for limits in torque_limits_corrected.values()
                    )
                    - 1
                )

                # Initialize an empty list to hold coefficient tensors
                coeffs_tensors = []

                for joint, limits in torque_limits_corrected.items():
                    coeffs = limits["coeffs"]
                    # revert the order
                    coeffs = coeffs[::-1]
                    padded_coeffs = coeffs + [0.0] * (
                        self.torque_coeff_degree + 1 - len(coeffs)
                    )
                    coeffs_tensor = torch.tensor(padded_coeffs, device="cuda")
                    coeffs_tensors.append(coeffs_tensor)
                # Stack the tensors into a single tensor and transpose to match the required shape
                self.torque_coeffs[start_idx:end_idx] = torch.stack(
                    coeffs_tensors
                ).transpose(0, 1)

                # Assuming 'start_idx' and 'end_idx' are defined and represent the range you want to fill
                for j, (joint, limits) in enumerate(torque_limits_corrected.items()):
                    # Create tensors filled with the 'min' and 'max' values for the specified range
                    min_val = torch.full(
                        (end_idx - start_idx,), limits["min"], device="cuda"
                    )
                    max_val = torch.full(
                        (end_idx - start_idx,), limits["max"], device="cuda"
                    )

                    # Use 'j' to index the correct column for assignment
                    self.force_min[start_idx:end_idx, j] = min_val
                    self.force_max[start_idx:end_idx, j] = max_val

            # scaling factors path
            scaling_factors_path = self.scale_factors_files[i]
            print("loading scaling factors from: ", scaling_factors_path)
            with open(scaling_factors_path) as file:
                scaling_factors = yaml.safe_load(file)
                x, y, z = scaling_factors["SHOVEL"]
                self.bucket_dims[start_idx:end_idx, 0] = self.env.cfg.bucket.r * x * z
                self.bucket_dims[start_idx:end_idx, 1] = self.env.cfg.bucket.b * y
                self.bucket_dims[start_idx:end_idx, 2] = self.env.cfg.bucket.a * z
                self.bucket_com[start_idx:end_idx, 0] = self.env.cfg.bucket.com[0] * x
                self.bucket_com[start_idx:end_idx, 1] = self.env.cfg.bucket.com[1] * z
                # these scalings  are currencly not used.
                # x_boom, y_boom, z_boom = scaling_factors["BOOM"]
                # x_dipper, y_dipper, z_dipper = scaling_factors["DIPPER"]
                # Convert the scalar multiplication result into a tensor with the same shape as the ones you're comparing it against
                # Ensure it's on the same device fch
                # or compatibility
                # scalar_result_tensor = (x_boom * x_dipper) * torch.ones(end_idx - start_idx, device=self.device)
                # self.arm_scale[start_idx:end_idx] = torch.max(
                #     scalar_result_tensor, torch.ones(end_idx - start_idx, device=self.device)
                # ).unsqueeze(1)
                self.arm_scale[start_idx:end_idx] = (
                    self.x_max[start_idx:end_idx]
                    / self.env.cfg.reference_values.ref_arm_reach
                )
                print("scaling factors keys ", scaling_factors.keys())
                base_x, base_y, base_z = scaling_factors["BASE"]
                self.base_length[start_idx:end_idx] = (
                    base_x * self.env.cfg.reference_values.ref_base_length
                )

    def read_transforms(self):
        """
        For each excavator, load the transforms present in the same folder as self.excavator[i].cfg.file.
        Separate translational and rotational components.
        """
        total_translations = []
        total_rotations = []
        total_w_P_wb = []
        total_R_wb = []

        for i in range(self.num_different_assets):
            print(
                "loading transformations for excavator from asset path: ", USD_PATHS[i]
            )
            # Load the transformations from the pickle file
            with open(
                os.path.join(
                    os.path.dirname(os.path.dirname(USD_PATHS[i])),
                    "transformations_and_limits.pickle",
                ),
                "rb",
            ) as file:
                transformations = pickle.load(file)
            tensor_transformations = {
                key: torch.tensor(
                    value["xyz"] + value["rpy"], device=self.device
                ).repeat(self.last_env_idx[i + 1] - self.last_env_idx[i], 1)
                for key, value in transformations.items()
            }

            # Extract and stack translational and rotational components
            translations = torch.stack(
                [
                    tensor_transformations[key][:, :3]
                    for key in tensor_transformations
                    if key != "w_P_wb"
                ],
                dim=1,
            )
            rotations = torch.stack(
                [
                    tensor_transformations[key][:, 3:]
                    for key in tensor_transformations
                    if key != "w_P_wb"
                ],
                dim=1,
            )

            # # Create a new vector for
            # hardcoded_w_p_wb = torch.tensor([0.0, 0.0, 0.74])

            # w_P_wb = torch.tensor(bucket_vel_norm
            #     hardcoded_w_p_wb, device=self.device
            # ).repeat(
            #     self.last_env_idx[i + 1] - self.last_env_idx[i], 1
            # )
            w_P_wb = torch.tensor(
                transformations["w_P_wb"]["xyz"], device=self.device
            ).repeat(self.last_env_idx[i + 1] - self.last_env_idx[i], 1)
            R_wb = quat_from_euler_xyz(
                roll=torch.tensor(
                    transformations["w_P_wb"]["rpy"][0], device=self.device
                ).repeat(self.last_env_idx[i + 1] - self.last_env_idx[i]),
                pitch=torch.tensor(
                    transformations["w_P_wb"]["rpy"][1], device=self.device
                ).repeat(self.last_env_idx[i + 1] - self.last_env_idx[i]),
                yaw=torch.tensor(
                    transformations["w_P_wb"]["rpy"][2], device=self.device
                ).repeat(self.last_env_idx[i + 1] - self.last_env_idx[i]),
            )

            total_translations.append(translations)
            total_rotations.append(rotations)
            total_w_P_wb.append(w_P_wb)
            total_R_wb.append(R_wb)

        # Stack transformations across all excavators
        self.links_arm = torch.cat(total_translations, dim=0)[:, 1:, :]
        self.b_P_bbm = torch.cat(total_translations, dim=0)[:, 0, :]
        self.link_rotations = torch.cat(total_rotations, dim=0)[:, :-1, :]
        self.w_P_wb = torch.cat(total_w_P_wb, dim=0)
        self.R_wb = torch.cat(total_R_wb, dim=0)

        # TODO: this is obsolete, Add the end effector contact frame rotation delta
        self.ee_contact_delta_angle = 0.245

    def init_measurements_buffers(self):
        # Arm joints (optional)
        if self.arm_joints_names:
            self.arm_joint_names = self.arm_joints_names
            self.arm_joint_ids = self.asset.find_joints(self.arm_joint_names)[0]
            self.jac_arm_joint_ids = [joint_id + 6 for joint_id in self.arm_joint_ids]
        else:
            self.arm_joint_names = []
            self.arm_joint_ids = []
            self.jac_arm_joint_ids = []

        # Bucket body (optional)
        if hasattr(self.cfg, "ee_body_name") and self.cfg.ee_body_name:
            self.bucket_body_idx = self.asset.find_bodies(self.cfg.ee_body_name)[0]
        else:
            self.bucket_body_idx = None

        # DOF pos and vel
        self.joint_pos = self.asset.data.joint_pos
        self.joint_vel = self.asset.data.joint_vel

        # -- Pitch Joint Kinematics --
        # Position and orientation of the pitch joint body in world frame
        self.j_pitch_quat = self.asset.data.body_quat_w[:, -1, :]
        # j_pitch_map stores the position of the pitch joint in the *map* (world) frame
        self.j_pitch_map = self.asset.data.body_pos_w[:, -1, :]
        # j_pitch_pos stores the position of the pitch joint relative to the *environment origin*
        self.j_pitch_pos = self.j_pitch_map - self.env.scene.env_origins

        # Linear and angular velocity of the pitch joint body in world frame
        self.j_pitch_vel = self.asset.data.body_lin_vel_w[:, -1, :]
        self.j_pitch_ang_vel = self.asset.data.body_ang_vel_w[:, -1, :]
        # ---------------------------

        # Jacobian
        self.jacobian = self.asset.root_physx_view.get_jacobians()
        self.jac_lin = self.jacobian[:, :, 0:3, :]
        self.jac_lin_T = self.jac_lin.transpose(-2, -1)
        self.jac_rot = self.jacobian[:, :, 3:6, :]
        self.jac_rot_T = self.jac_rot.transpose(-2, -1)
        # only bucket jacobians
        self.bucket_jac_lin_T_dof = self.jacobian[
            :, -1, 0:3, -self.num_dofs :
        ].transpose(-2, -1)
        self.bucket_jac_rot_T_dof = self.jacobian[
            :, -1, 3:6, -self.num_dofs :
        ].transpose(-2, -1)

        # contact forces, not as in Gym, TODO: check tensor size
        self.net_contact_forces = self.env.scene.sensors[
            "contact_forces"
        ].data.net_forces_w
        print("Loading bucket collisions")
        self.bucket_collision_f = self.net_contact_forces[:, -1, :]
        print("Loaded bucket collisions")
        self.compute_torque_limits()

        # Linear and angular velocity of the root of the asset in world frame
        self.root_lin_vel_w = self.asset.data.root_lin_vel_w
        self.root_ang_vel_w = self.asset.data.root_ang_vel_w
        self.root_quat_w = self.asset.data.root_quat_w
        self.root_pos_w = self.asset.data.root_pos_w

        # constants
        # const offset vector eef to pitch in eef
        # self.e_r_ep = torch.tensor([-1.3888, 0.0, 0.291139], device=self.device).expand(self.num_envs, -1)
        # const offset vector pitch to eef in pitch frame
        self.p_r_pe = self.links_arm[:, -1, :]
        print("read p_r_pe from link transfroms")
        # cabin: constant cabin pos in base frame (base and cabin frame have same orientation as long as turn joint is 0!!)
        # this is incorrect
        # constant rot pitch to eef
        self.quat_pe = math_utils.quat_from_euler_xyz(
            self.link_rotations[:, -1, 0],
            self.link_rotations[:, -1, 1],
            self.link_rotations[:, -1, 2],
        )
        # derived - reset cache sets these
        self.mm = torch.zeros(
            self.num_envs, self.num_dofs, self.num_dofs, device=self.device
        )
        self.gravity_tau = torch.zeros(self.num_envs, self.num_dofs, device=self.device)

        # Unit z vector
        self.unit_vec_z = torch.tensor([0.0, 0.0, 1.0], device=self.asset.device)
        self.unit_vec_z_expanded = self.unit_vec_z.expand(self.num_envs, -1)

        # Vector from pitch joint to bucket tip in world frame
        self.w_r_pe = torch.zeros([self.num_envs, 3], device=self.device)

        # -- Bucket Tip Kinematics --
        # Bucket Tip Position in World (Map) Frame
        self.bucket_pos_map = torch.zeros([self.num_envs, 3], device=self.device)
        # Bucket Tip Position relative to Environment Origin
        self.bucket_pos_w = torch.zeros([self.num_envs, 3], device=self.device)
        self.prev_bucket_pos_w = torch.zeros([self.num_envs, 3], device=self.device)

        # Bucket Tip Velocity in World Frame
        self.bucket_vel_w = torch.zeros([self.num_envs, 3], device=self.device)
        # Bucket Tip Orientation in World Frame
        self.quat_bucket_w = torch.zeros([self.num_envs, 4], device=self.device)
        # Unit vector along the bucket's primary axis (e.g., pointing backwards from tip) in World Frame
        self.bp_unit_vector_w = torch.zeros([self.num_envs, 3], device=self.device)
        # -------------------------

        # derived - reset cache does not set these
        self.base_pitch_w = torch.zeros([self.num_envs], device=self.device)
        self.bucket_aoa = torch.zeros(self.num_envs, device=self.device)
        self.bucket_ang_gac = torch.zeros([self.num_envs], device=self.device)
        self.bucket_vel_norm = torch.zeros([self.num_envs], device=self.device)

        self.base_z_w = torch.zeros([self.num_envs, 3], device=self.device)

        # Jacobian
        # # Gravity forces
        # self.gravity_forces = self.masses_expanded * self.gravity_expanded
        # self.gravity_forces_batched = self.masses_batched * self.gravity_expanded.unsqueeze(0)
        # Gravity forces
        # self.gravity_forces = self.masses_expanded * self.gravity_expanded
        self.gravity_forces_batched = self.masses_batched.unsqueeze(
            -1
        ) * self.gravity_expanded.unsqueeze(0)
        # In joint space
        gravity_genco_tau_batched = torch.sum(
            torch.matmul(
                self.jac_lin_T[:, -self.num_dofs :, -self.num_dofs :, :],
                self.gravity_forces_batched[:, -self.num_dofs :, :].unsqueeze(-1),
            ),
            dim=1,
        )

        # # Safety check: Compare the first entry of both calculations
        # gravity_genco_tau = torch.sum(
        #     torch.matmul(
        #         self.jac_lin_T[:, -self.num_dofs:, -self.num_dofs:, :],
        #         self.gravity_forces[-self.num_dofs:, :].unsqueeze(-1),
        #     ),
        #     dim=1,
        # )

        # # Check if the results are close
        # assert torch.allclose(gravity_genco_tau_batched[0], gravity_genco_tau[0], atol=1e-6), \
        #     "Mismatch between batched and non-batched gravity_genco_tau calculations"

        # Use the batched version for further calculations
        self.gravity_tau[:] = gravity_genco_tau_batched.squeeze()

        # # Mass Matrix
        # self.mm[:] = torch.sum(
        #     self.jac_lin_T.matmul(self.masses.view(-1, 1, 1) * self.jac_lin)
        #     + self.jac_rot_T.matmul(self.inertias.matmul(self.jac_rot)),
        #     dim=1,
        # )[:, -self.num_dofs :, -self.num_dofs :]

        self.mm = torch.sum(
            self.jac_lin_T.matmul(
                self.masses_batched.unsqueeze(-1).unsqueeze(-1) * self.jac_lin
            )
            + self.jac_rot_T.matmul(self.inertias_batched.matmul(self.jac_rot)),
            dim=1,
        )[:, -self.num_dofs :, -self.num_dofs :]

        # # check the first entry is the same
        # assert torch.allclose(self.mm[0], self.mm_batched[0], atol=1e-6), "Mismatch between mm and mm_batched"

        # Non linear therms
        # self.coriolis_centrifugal_force = self.asset.root_physx_view.get_coriolis_and_centrifugal_forces()
        self.coriolis_centrifugal_force = 0

    def update_measurements(self):
        # -- Update Core Physics Data --
        # Jacobians (assuming self.asset.data is updated before this call)
        self.jacobian[:] = self.asset.root_physx_view.get_jacobians()
        self.jac_lin = self.jacobian[:, :, 0:3, :]
        self.jac_lin_T = self.jac_lin.transpose(-2, -1)
        self.jac_rot = self.jacobian[:, :, 3:6, :]
        self.jac_rot_T = self.jac_rot.transpose(-2, -1)
        # Bucket-specific Jacobians (DOF part)
        self.bucket_jac_lin_T_dof = self.jacobian[
            :, -1, 0:3, -self.num_dofs :
        ].transpose(-2, -1)
        self.bucket_jac_rot_T_dof = self.jacobian[
            :, -1, 3:6, -self.num_dofs :
        ].transpose(-2, -1)

        # Contact Forces
        self.net_contact_forces = self.env.scene.sensors[
            "contact_forces"
        ].data.net_forces_w
        # Ensure bucket_body_idx is valid before indexing
        if self.bucket_body_idx is not None:
            self.bucket_collision_f = self.net_contact_forces[
                :, self.bucket_body_idx, :
            ]
        else:
            # Handle case where bucket body index is not set (e.g., set to zero or raise error)
            self.bucket_collision_f.zero_()  # Example: set to zero if not found

        # -- Update Pitch Joint Kinematics --
        # Orientation
        self.j_pitch_quat[:] = self.asset.data.body_quat_w[:, -1, :]
        # Position in Map (World) Frame
        self.j_pitch_map[:] = self.asset.data.body_pos_w[:, -1, :]
        # Position relative to Environment Origin
        self.j_pitch_pos[:] = self.j_pitch_map - self.env.scene.env_origins
        # Velocities
        self.j_pitch_vel[:] = self.asset.data.body_lin_vel_w[:, -1, :]
        self.j_pitch_ang_vel[:] = self.asset.data.body_ang_vel_w[:, -1, :]
        # -- Update Bucket Tip Kinematics --
        # Store previous position (relative to env origin)
        # Use .clone() to ensure it's a copy, not a reference that gets updated later
        self.prev_bucket_pos_w[:] = self.bucket_pos_w.clone()

        # Vector from pitch joint to bucket tip in world frame
        # self.p_r_pe is the constant vector in pitch frame
        self.w_r_pe = torch_utils.quat_rotate(self.j_pitch_quat, self.p_r_pe)

        # Bucket tip position in Map (World) frame
        self.bucket_pos_map[:] = self.j_pitch_map + self.w_r_pe
        # Bucket tip position relative to Environment origin
        self.bucket_pos_w[:] = self.bucket_pos_map - self.env.scene.env_origins

        # Bucket tip velocity in world frame
        # v_tip = v_pitch + omega_pitch x r_pitch_to_tip (where r is in world frame)
        self.bucket_vel_w[:] = self.j_pitch_vel + torch.cross(
            self.j_pitch_ang_vel, self.w_r_pe, dim=-1
        )

        # Bucket tip orientation in world frame
        # self.quat_pe is the constant rotation from pitch frame to bucket frame
        self.quat_bucket_w[:] = torch_utils.quat_mul(self.j_pitch_quat, self.quat_pe)
        # Unit vector along bucket's z-axis in world frame
        self.bp_unit_vector_w[:] = torch_utils.quat_rotate(
            self.quat_bucket_w, self.unit_vec_z_expanded
        )

        # -- Update Dynamics Terms --
        # Gravity forces
        # Assuming masses don't change per step, gravity_forces_batched is constant
        # self.gravity_forces_batched = self.masses_batched.unsqueeze(-1) * self.gravity_expanded.unsqueeze(0) # Can be computed once if masses are constant

        # Gravity Torques (using updated Jacobians)
        gravity_genco_tau = torch.sum(
            torch.matmul(
                self.jac_lin_T[:, -self.num_dofs :, -self.num_dofs :, :],
                self.gravity_forces_batched[:, -self.num_dofs :, :].unsqueeze(-1),
            ),
            dim=1,
        )
        self.gravity_tau[:] = gravity_genco_tau.squeeze(
            -1
        )  # Use squeeze(-1) for robustness

        # Mass Matrix (using updated Jacobians)
        # Assuming masses and inertias don't change per step, only Jacobians update
        self.mm = torch.sum(
            self.jac_lin_T.matmul(
                self.masses_batched.unsqueeze(-1).unsqueeze(-1) * self.jac_lin
            )
            + self.jac_rot_T.matmul(self.inertias_batched.matmul(self.jac_rot)),
            dim=1,
        )[:, -self.num_dofs :, -self.num_dofs :]

        # Coriolis/Centrifugal forces (if needed)
        # self.coriolis_centrifugal_force[:] = self.asset.root_physx_view.get_coriolis_and_centrifugal_forces()
        self.compute_torque_limits()

    def update_derived_measurements(self, env_ids, dim0):

        self.base_z_w = torch_utils.quat_rotate(
            self.root_quat_w[env_ids],
            self.unit_vec_z.expand(dim0, -1),
        )  # Noneed

        self.base_pitch_w[env_ids] = torch.atan2(
            self.base_z_w[:, 0], self.base_z_w[:, 2]
        )

        vel_ang_w = torch.atan2(
            self.bucket_vel_w[env_ids, 2], self.bucket_vel_w[env_ids, 0]
        )
        # in motion direction, bp unit vec points from tip backwards, -> invert sign here
        bp_ang_w = torch.atan2(
            -self.bp_unit_vector_w[env_ids, 2], -self.bp_unit_vector_w[env_ids, 0]
        )
        aoa = bp_ang_w - vel_ang_w
        # wrap between +/- pi
        self.bucket_aoa[env_ids] = torch.where(
            torch.abs(aoa) > np.pi,
            aoa
            - torch.copysign(2.0 * np.pi * torch.ones(dim0, device=self.device), aoa),
            aoa,
        )
        self.bucket_ang_gac[env_ids] = torch.atan2(
            self.bp_unit_vector_w[env_ids, 2], self.bp_unit_vector_w[env_ids, 0]
        )
        # problem with slicing with two tensors that are not broadcastale, easier to
        # y vel is 0 anyways
        self.bucket_vel_norm[env_ids] = torch.linalg.norm(
            self.bucket_vel_w[env_ids], dim=-1
        )

    def compute_force_limits(self):
        # friction in the x direction
        # sum first dim to get the excavator mass
        excavator_mass = self.masses_batched.sum(dim=1)
        # print("excavator_mass:", excavator_mass)
        friction_coeff = self.env.cfg.scene.plane.spawn.physics_material.static_friction
        # print("friction_coeff:", friction_coeff)
        friction_x = -excavator_mass * 9.81 * friction_coeff * 0.85
        # print("friction_x:", friction_x)
        self.force_limits[:, 0] = friction_x

        # pull up front part for the z direction, for bucket_pos_w_x world is also base
        # the center of mass should be given in the local frame of reference
        # Avoid division by zero by adding a small epsilon where the denominator might be zero
        epsilon = 1e-6
        # Use bucket_pos_map (world frame) for consistency in world frame calculations
        denominator = self.bucket_pos_map[:, 0] + self.base_length.squeeze(-1) / 2
        # print("denominator:", denominator)
        safe_denominator = torch.where(
            denominator == 0, torch.full_like(denominator, epsilon), denominator
        )
        # print("safe_denominator:", safe_denominator)
        # the get_com methods returns the com in the local reference frame of the body
        # the method get_transforms returns the transform wrt the *global* reference world frame, pos + xyzw quat
        # w_ccom  = \sum_i m_i * (w_P_wbi + T_wbi * bi_P_bicom)
        # Ensure using link transforms directly from PhysX view for current state
        link_transforms = (
            self.asset.root_physx_view.get_link_transforms()
        )  # Shape (N, num_bodies, 7)
        # print("link_transforms.shape:", link_transforms.shape)
        w_P_wb = link_transforms[:, :, :3]  # World position of each body's frame origin
        # print("w_P_wb:", w_P_wb)
        R_wb_quat = link_transforms[
            :, :, 3:
        ]  # World orientation (quat xyzw) of each body's frame
        # print("R_wb_quat:", R_wb_quat)

        # this centers of mass seems to be in the frame of the body b_P_bcom
        b_P_bcom = self.asset.root_physx_view.get_coms()[:, :, :3].to(
            self.device
        )  # Shape (N, num_bodies, 3)
        # print("b_P_bcom:", b_P_bcom)

        # Transform the center of mass from the body frame to the world frame using quaternion rotation
        w_P_bcom = math_utils.quat_apply(
            R_wb_quat, b_P_bcom
        )  # Shape (N, num_bodies, 3)
        # print("w_P_bcom:", w_P_bcom)

        # Add the position from the transforms to the rotated coms to translate them into the world frame
        w_P_wcom = w_P_wb + w_P_bcom  # Shape (N, num_bodies, 3)
        # print("w_P_wcom:", w_P_wcom)

        # Calculate the weighted center of mass
        # Ensure masses_batched has shape (N, num_bodies, 1) for broadcasting
        weighted_world_coms = (
            self.masses_batched.unsqueeze(-1) * w_P_wcom
        )  # Shape (N, num_bodies, 3)
        # print("weighted_world_coms:", weighted_world_coms)

        # Sum over the body indices to get the total center of mass for each asset
        # Ensure total mass has shape (N, 1) for broadcasting
        total_mass_per_env = torch.sum(
            self.masses_batched, dim=1, keepdim=True
        )  # Shape (N, 1)
        # print("total_mass_per_env:", total_mass_per_env)
        # Avoid division by zero if total mass is zero for some environment
        safe_total_mass = torch.where(
            total_mass_per_env == 0,
            torch.full_like(total_mass_per_env, epsilon),
            total_mass_per_env,
        )
        # print("safe_total_mass:", safe_total_mass)
        total_world_com = (
            torch.sum(weighted_world_coms, dim=1) / safe_total_mass
        )  # Shape (N, 3)
        # print("total_world_com:", total_world_com)

        # Calculate CoM relative to the base link's origin (assuming base is link 0)
        w_P_base = w_P_wb[:, 0, :]  # Shape (N, 3)
        # print("w_P_base:", w_P_base)
        # Need base orientation R_wb_base to transform world CoM into base frame, but calculation seems simpler in world frame
        # Let's recalculate the required moment arm components in the world frame directly

        # Approximate pivot point x-coordinate in world frame (assuming base link 0 is centered and aligned)
        x_pivot_world = self.root_pos_w[:, 0] - self.base_length.squeeze(-1) / 2
        # print("x_pivot_world:", x_pivot_world)
        x_com_world = total_world_com[:, 0]
        # print("x_com_world:", x_com_world)
        x_bucket_world = self.bucket_pos_map[:, 0]  # Use map frame (world) position
        # print("x_bucket_world:", x_bucket_world)

        arm_com = x_com_world - x_pivot_world
        # print("arm_com:", arm_com)
        arm_bucket = x_bucket_world - x_pivot_world
        # print("arm_bucket:", arm_bucket)
        safe_arm_bucket = torch.where(
            arm_bucket == 0, torch.full_like(arm_bucket, epsilon), arm_bucket
        )
        # print("safe_arm_bucket:", safe_arm_bucket)

        # Calculate max upward force (negative sign as Fz is upward lift, opposing gravity)
        force_z = -(excavator_mass * 9.81 * arm_com) / safe_arm_bucket
        # print("force_z:", force_z)

        # Assign the result to force_limits
        self.force_limits[:, 2] = force_z
        # print("force_limits before nan_to_num:", self.force_limits)
        # Check for NaNs/Infs in the force limits and clamp or set to default
        self.force_limits = torch.nan_to_num(
            self.force_limits, nan=0.0, posinf=1e6, neginf=-1e6
        )  # Set NaNs to 0, clamp Infs
        # print("force_limits after nan_to_num:", self.force_limits)

    def compute_torque_limits(self):
        """
        Compute the torque limits for the joints using the vector self.torque_coeffs of size (N, M, 3) and the joint positions self.joint_pos of size (N, 3).
        M is the number of coefficients of the polynomial. Since we assume the torque limits of a joint to be independent of other joints positions,
        the degree of the polynomial m = size(M) - 1.

        Uses the internal attribute self.joint_pos for joint positions. Computes for all environments.
        """
        target_coeffs = self.torque_coeffs
        target_force_max = self.force_max
        target_force_min = self.force_min
        # Assuming self.joint_pos holds the current joint positions of shape (N, 3)
        joint_pos = self.joint_pos

        # Compute polynomial factor based on joint positions
        torque_factor = helpers.compute_indipendent_poly(
            target_coeffs, joint_pos, self.torque_coeff_degree
        )
        self.torque_factors[:] = torque_factor

        # Create a mask for positive torque factors
        positive_mask = torque_factor >= 0

        # Compute tau_max and tau_min based on the sign of torque_factor
        # When torque_factor is positive: tau_max = force_max * factor, tau_min = force_min * factor
        # When torque_factor is negative: tau_max = force_min * factor, tau_min = force_max * factor (signs flip)
        self.tau_max[:] = torch.where(
            positive_mask,
            target_force_max * torque_factor,
            target_force_min * torque_factor,
        )
        self.tau_min[:] = torch.where(
            positive_mask,
            target_force_min * torque_factor,
            target_force_max * torque_factor,
        )


# Example instantiation (replace with your actual setup)
# cfg_mock = ... # Your configuration object mock/instance
# asset_mock = ... # Your asset object mock/instance
# env_mock = ... # Your environment object mock/instance
# num_envs_mock = ... # Number of environments
# measurements = M545Measurements(asset_mock, num_envs_mock, env_mock)
# measurements.update_measurements() # Call the updated method

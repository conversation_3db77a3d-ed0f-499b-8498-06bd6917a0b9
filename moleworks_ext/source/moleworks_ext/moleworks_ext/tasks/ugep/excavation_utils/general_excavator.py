# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""This script demonstrates how to spawn multiple objects in multiple environments.

.. code-block:: bash

    # Usage
    ./orbit.sh -p source/standalone/demos/multi_object.py --num_envs 2048

"""
from __future__ import annotations

"""Launch Isaac Sim Simulator first."""

import argparse

# add argparse argum

"""Rest everything follows."""
import os
import random
import re
from dataclasses import MISSING

import isaacsim.core.utils.prims as prim_utils
import omni.usd
# m545 specifics
from moleworks_ext import MOLEWORKS_RSC_DIR
from pxr import Gf, Sdf, Semantics, Usd, UsdGeom, Vt

import isaaclab.sim as sim_utils
from isaaclab.assets import ArticulationCfg, AssetBaseCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.utils import configclass
from moleworks_ext.common.assets.articulation_ext_force_global_cfg import Articulation_ext_force_globalCfg
# Define default values
DEFAULT_ASSETS_DIR = f"{MOLEWORKS_RSC_DIR}/Robots/Gravis/cat323_visual"
DEFAULT_ASSET_FILE_PATTERN = "cat323_rl_merged_modified.usd"

# Define default values
# DEFAULT_ASSETS_DIR = f"{MOLEWORKS_RSC_DIR}/Robots/Gravis/failing_assets"
# DEFAULT_ASSET_FILE_PATTERN = "sampled.usd"

# DEFAULT_ASSETS_DIR = f"{MOLEWORKS_RSC_DIR}/Robots/Gravis/cat_and_m545_visual"
# DEFAULT_ASSET_FILE_PATTERN = "same.usd"

DEFAULT_ASSETS_DIR = f"{MOLEWORKS_RSC_DIR}/ugep/m545_visual_calibrated"
DEFAULT_ASSET_FILE_PATTERN = "m545_fixed_tele_narrow_merged_modified.usd"

# DEFAULT_ASSETS_DIR = f"{MOLEWORKS_RSC_DIR}/Robots/Gravis/sampled_usds_100_filtered_w_m545"
# DEFAULT_ASSET_FILE_PATTERN = "sampled.usd"

# DEFAULT_ASSETS_DIR = f"{MOLEWORKS_RSC_DIR}/Robots/Gravis/sampled_usds_100_v2"
# DEFAULT_ASSET_FILE_PATTERN = "sampled.usd"

# DEFAULT_ASSETS_DIR = f"{MOLEWORKS_RSC_DIR}/Robots/Gravis/sampled_usds_1500_filtered"
# DEFAULT_ASSET_FILE_PATTERN = "sampled.usd"

# DEFAULT_ASSETS_DIR = f"{MOLEWORKS_RSC_DIR}/Robots/Gravis/real_machines"
# DEFAULT_ASSET_FILE_PATTERN = "same.usd"

ASSETS_DIR = DEFAULT_ASSETS_DIR
ASSET_FILE_PATTERN = DEFAULT_ASSET_FILE_PATTERN


def get_default_asset_paths():
    return DEFAULT_ASSETS_DIR, DEFAULT_ASSET_FILE_PATTERN


def set_asset_paths(asset_dir=None, asset_name=None):
    global ASSETS_DIR, ASSET_FILE_PATTERN, USD_PATHS
    ASSETS_DIR = os.path.join(MOLEWORKS_RSC_DIR, asset_dir) if asset_dir is not None else DEFAULT_ASSETS_DIR
    ASSET_FILE_PATTERN = asset_name if asset_name is not None else DEFAULT_ASSET_FILE_PATTERN
    USD_PATHS = find_usd_files(ASSETS_DIR, ASSET_FILE_PATTERN)
    print("USD Paths: ", USD_PATHS)


def find_usd_files(directory, filename_pattern):
    """
    Recursively finds all USD files matching the exact filename_pattern within the given directory,
    and sorts them based on the folder name or file name if no numeric subfolder exists.

    Args:
    directory (str): The directory to search in.
    filename_pattern (str): The exact filename to match.

    Returns:
    list: A sorted list of paths to the matching USD files.
    """
    matching_files = []
    for root, _, files in os.walk(directory):
        for filename in files:
            if filename == filename_pattern:  # Check for exact filename match
                matching_files.append(os.path.join(root, filename))

    def sort_key(path):
        # Extract the part of the path immediately under the given directory
        relative_path = os.path.relpath(path, directory)
        first_part = relative_path.split(os.sep)[0]
        
        # Try to convert to int if possible, otherwise use the string
        try:
            return int(first_part)
        except ValueError:
            return first_part

    # Sort the list based on the extracted key
    matching_files.sort(key=sort_key)
    return matching_files


USD_PATHS = find_usd_files(ASSETS_DIR, ASSET_FILE_PATTERN)
print("USD Paths:  ", USD_PATHS)

import fnmatch

from isaaclab.actuators import ImplicitActuatorCfg
from isaaclab.sim.spawners.from_files.from_files_cfg import GroundPlaneCfg, UsdFileCfg


def spawn_multi_object_randomly(
    prim_path: str,
    cfg: MultiAssetCfg,
    translation: tuple[float, float, float] | None = None,
    orientation: tuple[float, float, float, float] | None = None,
) -> Usd.Prim:
    # resolve: {SPAWN_NS}/AssetName
    # note: this assumes that the spawn namespace already exists in the stage
    root_path, asset_path = prim_path.rsplit("/", 1)
    # check if input is a regex expression
    # note: a valid prim path can only contain alphanumeric characters, underscores, and forward slashes
    is_regex_expression = re.match(r"^[a-zA-Z0-9/_]+$", root_path) is None

    # resolve matching prims for source prim path expression
    if is_regex_expression and root_path != "":
        source_prim_paths = sim_utils.find_matching_prim_paths(root_path)
        # if no matching prims are found, raise an error
        if len(source_prim_paths) == 0:
            raise RuntimeError(
                f"Unable to find source prim path: '{root_path}'. Please create the prim before spawning."
            )
    else:
        source_prim_paths = [root_path]

    # resolve prim paths for spawning and cloning
    prim_paths = [f"{source_prim_path}/{asset_path}" for source_prim_path in source_prim_paths]
    # manually clone prims if the source prim path is a regex expression
    for prim_path in prim_paths:
        # randomly select an asset configuration
        asset_cfg = random.choice(cfg.assets_cfg)
        # spawn single instance
        prim = asset_cfg.func(prim_path, asset_cfg, translation, orientation)
        # set the prim visibility
        if hasattr(asset_cfg, "visible"):
            imageable = UsdGeom.Imageable(prim)
            if asset_cfg.visible:
                imageable.MakeVisible()
            else:
                imageable.MakeInvisible()
        # set the semantic annotations
        if hasattr(asset_cfg, "semantic_tags") and asset_cfg.semantic_tags is not None:
            # note: taken from replicator scripts.utils.utils.py
            for semantic_type, semantic_value in asset_cfg.semantic_tags:
                # deal with spaces by replacing them with underscores
                semantic_type_sanitized = semantic_type.replace(" ", "_")
                semantic_value_sanitized = semantic_value.replace(" ", "_")
                # set the semantic API for the instance
                instance_name = f"{semantic_type_sanitized}_{semantic_value_sanitized}"
                sem = Semantics.SemanticsAPI.Apply(prim, instance_name)
                # create semantic type and data attributes
                sem.CreateSemanticTypeAttr()
                sem.CreateSemanticDataAttr()
                sem.GetSemanticTypeAttr().Set(semantic_type)
                sem.GetSemanticDataAttr().Set(semantic_value)
        # activate rigid body contact sensors
        if hasattr(asset_cfg, "activate_contact_sensors") and asset_cfg.activate_contact_sensors:
            sim_utils.activate_contact_sensors(prim_path, asset_cfg.activate_contact_sensors)

    # return the prim
    return prim


def spawn_multi_object_randomly_sdf(
    prim_path: str,
    cfg: MultiAssetCfg,
    translation: tuple[float, float, float] | None = None,
    orientation: tuple[float, float, float, float] | None = None,
) -> Usd.Prim:
    # resolve: {SPAWN_NS}/AssetName
    # note: this assumes that the spawn namespace already exists in the stage
    root_path, asset_path = prim_path.rsplit("/", 1)
    # check if input is a regex expression
    # note: a valid prim path can only contain alphanumeric characters, underscores, and forward slashes
    is_regex_expression = re.match(r"^[a-zA-Z0-9/_]+$", root_path) is None

    # resolve matching prims for source prim path expression
    if is_regex_expression and root_path != "":
        source_prim_paths = sim_utils.find_matching_prim_paths(root_path)
        # if no matching prims are found, raise an error
        if len(source_prim_paths) == 0:
            raise RuntimeError(
                f"Unable to find source prim path: '{root_path}'. Please create the prim before spawning."
            )
    else:
        source_prim_paths = [root_path]

    # spawn everything first in a "Dataset" prim
    prim_utils.create_prim("/World/Dataset", "Scope")
    proto_prim_paths = list()
    for index, asset_cfg in enumerate(cfg.assets_cfg):
        # spawn single instance
        proto_prim_path = f"/World/Dataset/Object_{index:02d}"
        prim = asset_cfg.func(proto_prim_path, asset_cfg)
        # save the proto prim path
        proto_prim_paths.append(proto_prim_path)
        # set the prim visibility
        if hasattr(asset_cfg, "visible"):
            imageable = UsdGeom.Imageable(prim)
            if asset_cfg.visible:
                imageable.MakeVisible()
            else:
                imageable.MakeInvisible()
        # set the semantic annotations
        if hasattr(asset_cfg, "semantic_tags") and asset_cfg.semantic_tags is not None:
            # note: taken from replicator scripts.utils.utils.py
            for semantic_type, semantic_value in asset_cfg.semantic_tags:
                # deal with spaces by replacing them with underscores
                semantic_type_sanitized = semantic_type.replace(" ", "_")
                semantic_value_sanitized = semantic_value.replace(" ", "_")
                # set the semantic API for the instance
                instance_name = f"{semantic_type_sanitized}_{semantic_value_sanitized}"
                sem = Semantics.SemanticsAPI.Apply(prim, instance_name)
                # create semantic type and data attributes
                sem.CreateSemanticTypeAttr()
                sem.CreateSemanticDataAttr()
                sem.GetSemanticTypeAttr().Set(semantic_type)
                sem.GetSemanticDataAttr().Set(semantic_value)
        # activate rigid body contact sensors
        if hasattr(asset_cfg, "activate_contact_sensors") and asset_cfg.activate_contact_sensors:
            sim_utils.activate_contact_sensors(proto_prim_path, asset_cfg.activate_contact_sensors)

    # resolve prim paths for spawning and cloning
    prim_paths = [f"{source_prim_path}/{asset_path}" for source_prim_path in source_prim_paths]
    # acquire stage
    stage = omni.usd.get_context().get_stage()
    # convert orientation ordering (wxyz to xyzw)
    orientation = (orientation[1], orientation[2], orientation[3], orientation[0])
    # manually clone prims if the source prim path is a regex expression
    with Sdf.ChangeBlock():
        for i, prim_path in enumerate(prim_paths):
            # spawn single instance
            env_spec = Sdf.CreatePrimInLayer(stage.GetRootLayer(), prim_path)
            # randomly select an asset configuration
            proto_path = random.choice(proto_prim_paths)
            # inherit the proto prim
            # env_spec.inheritPathList.Prepend(Sdf.Path(proto_path))
            Sdf.CopySpec(env_spec.layer, Sdf.Path(proto_path), env_spec.layer, Sdf.Path(prim_path))
            # set the translation and orientation
            _ = UsdGeom.Xform(stage.GetPrimAtPath(proto_path)).GetPrim().GetPrimStack()

            translate_spec = env_spec.GetAttributeAtPath(prim_path + ".xformOp:translate")
            if translate_spec is None:
                translate_spec = Sdf.AttributeSpec(env_spec, "xformOp:translate", Sdf.ValueTypeNames.Double3)
            translate_spec.default = Gf.Vec3d(*translation)

            orient_spec = env_spec.GetAttributeAtPath(prim_path + ".xformOp:orient")
            if orient_spec is None:
                orient_spec = Sdf.AttributeSpec(env_spec, "xformOp:orient", Sdf.ValueTypeNames.Quatd)
            orient_spec.default = Gf.Quatd(*orientation)

            scale_spec = env_spec.GetAttributeAtPath(prim_path + ".xformOp:scale")
            if scale_spec is None:
                scale_spec = Sdf.AttributeSpec(env_spec, "xformOp:scale", Sdf.ValueTypeNames.Double3)
            scale_spec.default = Gf.Vec3d(1.0, 1.0, 1.0)

            op_order_spec = env_spec.GetAttributeAtPath(prim_path + ".xformOpOrder")
            if op_order_spec is None:
                op_order_spec = Sdf.AttributeSpec(env_spec, UsdGeom.Tokens.xformOpOrder, Sdf.ValueTypeNames.TokenArray)
            op_order_spec.default = Vt.TokenArray(["xformOp:translate", "xformOp:orient", "xformOp:scale"])

            # DO YOUR OWN OTHER KIND OF RANDOMIZATION HERE!
            # Note: Just need to acquire the right attribute about the property you want to set
            # Here is an example on setting color randomly
            # color_spec = env_spec.GetAttributeAtPath(prim_path + "/geometry/material/Shader.inputs:diffuseColor")
            # color_spec.default = Gf.Vec3f(random.random(), random.random(), random.random())

    # delete the dataset prim after spawning
    prim_utils.delete_prim("/World/Dataset")

    # return the prim
    return prim_utils.get_prim_at_path(prim_paths[0])


def spawn_multi_object_cyclically_sdf(
    prim_path: str,
    cfg: MultiAssetCfg,
    translation: tuple[float, float, float] | None = None,
    orientation: tuple[float, float, float, float] | None = None,
) -> Usd.Prim:
    # Initialize a static index for cycling through configurations
    if not hasattr(spawn_multi_object_cyclically_sdf, "index"):
        spawn_multi_object_cyclically_sdf.index = 0

    # Check if the prim path contains regex; if it does, resolve the matching prims
    root_path, asset_path = prim_path.rsplit("/", 1)
    is_regex_expression = re.match(r"^[a-zA-Z0-9/_]+$", root_path) is None
    if is_regex_expression and root_path != "":
        source_prim_paths = sim_utils.find_matching_prim_paths(root_path)
        if not source_prim_paths:
            raise RuntimeError(
                f"Unable to find source prim path: '{root_path}'. Please create the prim before spawning."
            )
    else:
        source_prim_paths = [root_path]

    # Spawn everything first in a "Dataset" prim for managing cloning operations
    prim_utils.create_prim("/World/Dataset", "Scope")
    proto_prim_paths = list()

    # Create prototypes in Dataset
    for index, asset_cfg in enumerate(cfg.assets_cfg):
        proto_prim_path = f"/World/Dataset/Object_{index:02d}"
        prim = asset_cfg.func(proto_prim_path, asset_cfg)
        proto_prim_paths.append(proto_prim_path)
        # Apply visibility settings
        if hasattr(asset_cfg, "visible"):
            imageable = UsdGeom.Imageable(prim)
            imageable.MakeVisible() if asset_cfg.visible else imageable.MakeInvisible()
        # Apply semantic annotations
        if hasattr(asset_cfg, "semantic_tags") and asset_cfg.semantic_tags:
            for semantic_type, semantic_value in asset_cfg.semantic_tags:
                semantic_type_sanitized = semantic_type.replace(" ", "_")
                semantic_value_sanitized = semantic_value.replace(" ", "_")
                instance_name = f"{semantic_type_sanitized}_{semantic_value_sanitized}"
                sem = Semantics.SemanticsAPI.Apply(prim, instance_name)
                sem.CreateSemanticTypeAttr()
                sem.CreateSemanticDataAttr()
                sem.GetSemanticTypeAttr().Set(semantic_type)
                sem.GetSemanticDataAttr().Set(semantic_value)
        # Activate contact sensors if applicable
        if hasattr(asset_cfg, "activate_contact_sensors") and asset_cfg.activate_contact_sensors:
            sim_utils.activate_contact_sensors(proto_prim_path, asset_cfg.activate_contact_sensors)

    # Cycle through the asset configurations and clone to the actual spawn locations
    stage = omni.usd.get_context().get_stage()
    with Sdf.ChangeBlock():
        for source_prim_path in source_prim_paths:
            prim_path = f"{source_prim_path}/{asset_path}"
            env_spec = Sdf.CreatePrimInLayer(stage.GetRootLayer(), prim_path)
            proto_path = proto_prim_paths[spawn_multi_object_cyclically_sdf.index]
            Sdf.CopySpec(env_spec.layer, Sdf.Path(proto_path), env_spec.layer, Sdf.Path(prim_path))
            spawn_multi_object_cyclically_sdf.index = (spawn_multi_object_cyclically_sdf.index + 1) % len(
                cfg.assets_cfg
            )

            # Set transformation: translate, orient, scale
            translate_spec = Sdf.AttributeSpec(env_spec, prim_path + ".xformOp:translate", Sdf.ValueTypeNames.Double3)
            translate_spec.default = translation or (0.0, 0.0, 0.0)
            orient_spec = Sdf.AttributeSpec(env_spec, prim_path + ".xformOp:orient", Sdf.ValueTypeNames.Quatd)
            orient_spec.default = orientation or (0, 0, 0, 1)
            scale_spec = Sdf.AttributeSpec(env_spec, prim_path + ".xformOp:scale", Sdf.ValueTypeNames.Double3)
            scale_spec.default = (1.0, 1.0, 1.0)
            op_order_spec = Sdf.AttributeSpec(env_spec, prim_path + ".xformOpOrder", Sdf.ValueTypeNames.TokenArray)
            op_order_spec.default = Vt.TokenArray(["xformOp:translate", "xformOp:orient", "xformOp:scale"])

    # Delete the dataset prim after spawning
    prim_utils.delete_prim("/World/Dataset")

    # Return the prim at the path of the last spawned object
    return prim_utils.get_prim_at_path(prim_path[0])


def spawn_multi_object_repeatedly_sdf(
    prim_path: str,
    cfg: MultiAssetCfg,
    translation: tuple[float, float, float] | None = None,
    orientation: tuple[float, float, float, float] | None = None,
) -> Usd.Prim:
    print("calling spawn_multi_object_repeatedly_sdf with usd paths: ", USD_PATHS)
    # Initialize a static index for cycling through configurations
    if not hasattr(spawn_multi_object_repeatedly_sdf, "index"):
        spawn_multi_object_repeatedly_sdf.index = 0
        spawn_multi_object_repeatedly_sdf.count = 0  # Additional counter to track repetitions of each asset


    # Extract the root path and asset path from the prim path
    root_path, asset_path = prim_path.rsplit("/", 1)
    is_regex_expression = re.match(r"^[a-zA-Z0-9/_]+$", root_path) is None
    if is_regex_expression and root_path != "":
        source_prim_paths = sim_utils.find_matching_prim_paths(root_path)
        if not source_prim_paths:
            raise RuntimeError(
                f"Unable to find source prim path: '{root_path}'. Please create the prim before spawning."
            )
    else:
        source_prim_paths = [root_path]

    num_envs = len(source_prim_paths)
    num_assets = len(cfg.assets_cfg)
    if num_envs % num_assets != 0:
        proposed_num_envs = num_assets * (num_envs // num_assets + 1)
        raise ValueError(f"Number of environments ({num_envs}) must be a multiple of the number of asset types ({num_assets}). Consider using {proposed_num_envs} environments instead.")
    repetitions = num_envs // num_assets  # Ensuring even distribution

    # Create a Dataset prim if it does not exist for managing cloning operations
    dataset_prim_path = "/World/Dataset"
    prim_utils.create_prim(dataset_prim_path, "Scope")
    proto_prim_paths = []

    # Create prototypes in the Dataset
    for index, asset_cfg in enumerate(cfg.assets_cfg):
        proto_prim_path = f"{dataset_prim_path}/Object_{index:02d}"
        prim = asset_cfg.func(proto_prim_path, asset_cfg)
        proto_prim_paths.append(proto_prim_path)
        # Apply visibility, semantics, and activate sensors as per your original logic

    # Cycle through the asset configurations and clone to the actual spawn locations
    stage = omni.usd.get_context().get_stage()
    with Sdf.ChangeBlock():
        for source_prim_path in source_prim_paths:
            prim_path = f"{source_prim_path}/{asset_path}"
            print(f"Prim path: {prim_path}")
            env_spec = Sdf.CreatePrimInLayer(stage.GetRootLayer(), prim_path)
            proto_path = proto_prim_paths[spawn_multi_object_repeatedly_sdf.index]
            Sdf.CopySpec(env_spec.layer, Sdf.Path(proto_path), env_spec.layer, Sdf.Path(prim_path))
            print(f"Proto path: {proto_path}")

            # Increment the count and check if it meets the required repetitions
            spawn_multi_object_repeatedly_sdf.count += 1
            if spawn_multi_object_repeatedly_sdf.count == repetitions:
                spawn_multi_object_repeatedly_sdf.count = 0
                spawn_multi_object_repeatedly_sdf.index = (spawn_multi_object_repeatedly_sdf.index + 1) % num_assets

    # Delete the dataset prim after spawning
    prim_utils.delete_prim("/World/Dataset")

    # Return the last created prim
    return prim_utils.get_prim_at_path(prim_path)


def spawn_multi_object_cyclically(
    prim_path: str,
    cfg: MultiAssetCfg,
    translation: tuple[float, float, float] | None = None,
    orientation: tuple[float, float, float, float] | None = None,
) -> Usd.Prim:
    # Initialize 1 index on the function if it doesn't exist
    if not hasattr(spawn_multi_object_cyclically, "index"):
        spawn_multi_object_cyclically.index = 0

    # resolve: {SPAWN_NS}/AssetName
    root_path, asset_path = prim_path.rsplit("/", 1)
    is_regex_expression = re.match(r"^[a-zA-Z0-9/_]+$", root_path) is None

    if is_regex_expression and root_path != "":
        source_prim_paths = sim_utils.find_matching_prim_paths(root_path)
        if len(source_prim_paths) == 0:
            raise RuntimeError(
                f"Unable to find source prim path: '{root_path}'. Please create the prim before spawning."
            )
    else:
        source_prim_paths = [root_path]

    prim_paths = [f"{source_prim_path}/{asset_path}" for source_prim_path in source_prim_paths]

    for prim_path in prim_paths:
        # Cycle through the asset configurations
        asset_cfg = cfg.assets_cfg[spawn_multi_object_cyclically.index]
        print(f"Asset config: {asset_cfg}")
        spawn_multi_object_cyclically.index = (spawn_multi_object_cyclically.index + 1) % len(cfg.assets_cfg)

        # spawn single instance
        prim = asset_cfg.func(prim_path, asset_cfg, translation, orientation)

        if hasattr(asset_cfg, "visible"):
            imageable = UsdGeom.Imageable(prim)
            if asset_cfg.visible:
                imageable.MakeVisible()
            else:
                imageable.MakeInvisible()

        if hasattr(asset_cfg, "semantic_tags") and asset_cfg.semantic_tags is not None:
            for semantic_type, semantic_value in asset_cfg.semantic_tags:
                semantic_type_sanitized = semantic_type.replace(" ", "_")
                semantic_value_sanitized = semantic_value.replace(" ", "_")
                instance_name = f"{semantic_type_sanitized}_{semantic_value_sanitized}"
                sem = Semantics.SemanticsAPI.Apply(prim, instance_name)
                sem.CreateSemanticTypeAttr()
                sem.CreateSemanticDataAttr()
                sem.GetSemanticTypeAttr().Set(semantic_type)
                sem.GetSemanticDataAttr().Set(semantic_value)

        if hasattr(asset_cfg, "activate_contact_sensors") and asset_cfg.activate_contact_sensors:
            sim_utils.activate_contact_sensors(prim_path, asset_cfg.activate_contact_sensors)

    return prim


@configclass
class MultiAssetCfg(sim_utils.SpawnerCfg):
    """Configuration parameters for loading multiple assets randomly."""

    # Uncomment this one: 45 seconds for 2048 envs
    # func: sim_utils.SpawnerCfg.func = spawn_multi_object_cyclically
    # Uncomment this one: 2.15 seconds for 2048 envs
    func: sim_utils.SpawnerCfg.func = spawn_multi_object_repeatedly_sdf

    assets_cfg: list[sim_utils.SpawnerCfg] = MISSING
    """List of asset configurations to spawn."""


GENERAL_EX_CFG = Articulation_ext_force_globalCfg(
    prim_path="/World/envs/env_.*/Objects",
    spawn=MultiAssetCfg(
        assets_cfg=[
            sim_utils.UsdFileCfg(
                usd_path=usd_path,
                activate_contact_sensors=True,
                rigid_props=sim_utils.RigidBodyPropertiesCfg(
                    disable_gravity=False,
                    retain_accelerations=False,
                    linear_damping=0.0,
                    angular_damping=0.0,
                    max_linear_velocity=1000.0,
                    max_angular_velocity=1000.0,
                    max_depenetration_velocity=0.1,
                    sleep_threshold=0.0,
                    stabilization_threshold=0.0,
                ),
                articulation_props=sim_utils.ArticulationRootPropertiesCfg(
                    enabled_self_collisions=False,
                    solver_position_iteration_count=16,
                    solver_velocity_iteration_count=12,
                    sleep_threshold=0.0,
                    stabilization_threshold=0.0,
                ),
                collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.008, rest_offset=0.0),
            )
            for usd_path in USD_PATHS
        ]
    ),
    # same init for all: for the moment ok because base height is not modified and robot does not drop
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(
            0.0,
            0.0,
            0.0,
        ),  #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        # pos=( 0.0136, -0.0689,   0.521),
        joint_pos={
            # arm
            "J_BOOM": (
                -0.8792816
            ),  # -0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
            "J_STICK": 2.029131,  # 1.57, # 2.029131, #
            # "J_TELE": 1.1033065, #0.009, #1.1033065, #
            # tool (end effector)
            "J_EE_PITCH": 0.48247415,  # 0.48247415,#
        },
    ),  # -0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_EE_PITCH"],
            effort_limit=1e10,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)


class GeneralExcavatorReferenceValues:
    """
    Reference values for the general excavator.
    """
    ref_base_length: float = 0
    ref_arm_reach: float = 0
    boom_length: float = 0
    dipper_length: float = 0
    shovel_length: float = 0
    shovel_height: float = 0


class M545ReferenceValues(GeneralExcavatorReferenceValues):
    """
    Reference values for the m545 excavator.
    """
    ref_base_length = 6.0
    ref_arm_reach = 7.5
    
    boom_height = 1.5
    boom_length = 2.84
    dipper_length = 3.52
    shovel_length = 1.5  # this is bucket edge length (y)
    shovel_height = 1.79
    
    class Bucket:
        a = 0.525  # bottom plate length
        da = 0.0105  # discretization step of bottom plate for finding intersection with soil
        b = 1.4  # bucket with
        r = 0.375  # bucket radius
        edge_half_angle = 0.1  # for penetration model
        edge_top_width = 0.015  # for penetration model
        # TODO: this should change!
        com = [0.527271, 0.75594]

    bucket = Bucket()


class Cat323ReferenceValues(GeneralExcavatorReferenceValues):
    ref_base_length = 8.0
    ref_arm_reach = 9.5
    
    boom_height = 1.8
    boom_length = 5.67
    dipper_length = 2.92
    shovel_length = 1.5  # this is bucket edge length (y), actually not sure
    shovel_height = 1.79
    
    # Add these new attributes to match the joint mapping keys
    J_BOOM_z = boom_height  # Using boom_height as the reference value
    J_BOOM_Length = boom_length
    J_DIPPER_Length = dipper_length
    Shovel_Width = 1.5
    Shovel_x_offset = 1.79
    Shovel_z_offset = shovel_height
    Shovel_pitch_offset = -0.34  # Add appropriate value
    
    class Bucket:
        a = 0.525  # bottom plate length
        da = 0.0105  # discretization step of bottom plate for finding intersection with soil
        b = 1.5  # bucket with
        r = 0.375  # bucket radius
        edge_half_angle = 0.1  # for penetration model
        edge_top_width = 0.015  # for penetration model
        # TODO: this should change!
        com = [0.527271, 0.75594]

    bucket = Bucket()
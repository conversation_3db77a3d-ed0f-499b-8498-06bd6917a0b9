import pandas as pd
import scipy.stats as stats
import numpy as np
import os
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression

# Load the data from the Excel file
path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/excavator_info.xlsx'
data = pd.read_excel(os.path.abspath(path))
# Convert all columns to numeric, forcing errors to NaN
data = data.apply(pd.to_numeric, errors='coerce')
# Handle missing values (e.g., fill with mean or median)
data.fillna(data.mean(), inplace=True)

# Fit linear regression models conditioned on reach
def fit_linear_model(x, y):
    model = LinearRegression()
    model.fit(x.reshape(-1, 1), y)
    return model

reach = data['Max Reach Ground'].values

engine_power_model = fit_linear_model(reach, data['Engine Power'].values)
boom_length_model = fit_linear_model(reach, data['Boom Length'].values)
dipper_length_model = fit_linear_model(reach, data['Dipper Length'].values)
shovel_length_model = fit_linear_model(reach, data['Shovel Length'].values)

# Predict values using the models
predicted_engine_power = engine_power_model.predict(reach.reshape(-1, 1))
predicted_boom_length = boom_length_model.predict(reach.reshape(-1, 1))
predicted_dipper_length = dipper_length_model.predict(reach.reshape(-1, 1))
predicted_shovel_length = shovel_length_model.predict(reach.reshape(-1, 1))

# Calculate residuals
residuals_engine_power = data['Engine Power'] - predicted_engine_power
residuals_boom_length = data['Boom Length'] - predicted_boom_length
residuals_dipper_length = data['Dipper Length'] - predicted_dipper_length
residuals_shovel_length = data['Shovel Length'] - predicted_shovel_length

# Fit distributions to residuals
engine_power_residual_dist = stats.norm.fit(residuals_engine_power)
boom_length_residual_dist = stats.norm.fit(residuals_boom_length)
dipper_length_residual_dist = stats.norm.fit(residuals_dipper_length)
shovel_length_residual_dist = stats.norm.fit(residuals_shovel_length)

# Function to plot histogram and fitted distribution
def plot_distribution(data, dist_params, title):
    mu, std = dist_params
    plt.figure(figsize=(10, 6))
    plt.hist(data, bins=30, density=True, alpha=0.6, color='g', label='Data histogram')
    xmin, xmax = plt.xlim()
    x = np.linspace(xmin, xmax, 100)
    p = stats.norm.pdf(x, mu, std)
    plt.plot(x, p, 'k', linewidth=2, label='Fitted normal distribution')
    plt.title(title)
    plt.xlabel('Value')
    plt.ylabel('Density')
    plt.legend()
    plt.show()

# Plot residual distributions
plot_distribution(residuals_engine_power, engine_power_residual_dist, 'Engine Power Residual Distribution')
plot_distribution(residuals_boom_length, boom_length_residual_dist, 'Boom Length Residual Distribution')
plot_distribution(residuals_dipper_length, dipper_length_residual_dist, 'Dipper Length Residual Distribution')
plot_distribution(residuals_shovel_length, shovel_length_residual_dist, 'Shovel Length Residual Distribution')

# Sample reach from [R_min, R_max]
R_min, R_max = data['Max Reach Ground'].min(), data['Max Reach Ground'].max()
# Calculate mean and standard deviation for 'Max Reach Ground'
reach_mean = data['Max Reach Ground'].mean()
reach_std = data['Max Reach Ground'].std()

# Sample reach from a normal distribution centered around the mean with calculated standard deviation
sampled_reach = np.random.normal(reach_mean, reach_std)
# Sample residuals
sampled_engine_power_residual = stats.norm.rvs(*engine_power_residual_dist)
sampled_boom_length_residual = stats.norm.rvs(*boom_length_residual_dist)
sampled_dipper_length_residual = stats.norm.rvs(*dipper_length_residual_dist)
sampled_shovel_length_residual = stats.norm.rvs(*shovel_length_residual_dist)

# Calculate sampled values by adding residuals to predicted values
sampled_engine_power = engine_power_model.predict(np.array([[sampled_reach]])) + sampled_engine_power_residual
sampled_boom_length = boom_length_model.predict(np.array([[sampled_reach]])) + sampled_boom_length_residual
sampled_dipper_length = dipper_length_model.predict(np.array([[sampled_reach]])) + sampled_dipper_length_residual
sampled_shovel_length = shovel_length_model.predict(np.array([[sampled_reach]])) + sampled_shovel_length_residual

# Calculate bucket and stick force based on sampled engine power and reach
# Assuming 'data' has the necessary columns
X = data[['Engine Power', 'Max Reach Ground']]
# Interaction term
X['Interaction'] = X['Engine Power'] * X['Max Reach Ground']

model = LinearRegression()
model.fit(X, data['Bucket Force'])  # Assuming 'Bucket Force' is a column in your data

# Now use this model to predict
sampled_values = np.array([[sampled_engine_power, sampled_reach, sampled_engine_power * sampled_reach]])
predicted_force = model.predict(sampled_values)

# Ensure constraints
bucket_force_min, bucket_force_max = data['Bucket Force'].min(), data['Bucket Force'].max()
stick_force_min, stick_force_max = data['Stick Force'].min(), data['Stick Force'].max()

sampled_bucket_force = np.clip(predicted_force, bucket_force_min, bucket_force_max)
sampled_stick_force = np.clip(predicted_force, stick_force_min, stick_force_max)

# Print sampled values and their bounds
print(f"Sampled Reach: {sampled_reach} (Min: {R_min}, Max: {R_max})")
print(f"Sampled Engine Power: {sampled_engine_power} (Mean: {engine_power_residual_dist[0]}, Std Dev: {engine_power_residual_dist[1]})")
print(f"Sampled Boom Length: {sampled_boom_length} (Mean: {boom_length_residual_dist[0]}, Std Dev: {boom_length_residual_dist[1]})")
print(f"Sampled Dipper Length: {sampled_dipper_length} (Mean: {dipper_length_residual_dist[0]}, Std Dev: {dipper_length_residual_dist[1]})")
print(f"Sampled Shovel Length: {sampled_shovel_length} (Mean: {shovel_length_residual_dist[0]}, Std Dev: {shovel_length_residual_dist[1]})")
print(f"Sampled Bucket Force: {sampled_bucket_force} (Min: {bucket_force_min}, Max: {bucket_force_max})")
print(f"Sampled Stick Force: {sampled_stick_force} (Min: {stick_force_min}, Max: {stick_force_max})")

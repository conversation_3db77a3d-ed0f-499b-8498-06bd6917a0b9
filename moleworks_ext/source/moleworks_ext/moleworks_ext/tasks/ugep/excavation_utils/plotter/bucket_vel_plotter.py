# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Script designed to plot data saved from the Logger to CSV

import matplotlib.pyplot as plt
import numpy as np

import pandas as pd

# Set global parameters for all plots
plt.rcParams["axes.labelsize"] = 14  # For x and y labels
plt.rcParams["axes.titlesize"] = 16  # For title
plt.rcParams["xtick.labelsize"] = 12  # For x tick labels
plt.rcParams["ytick.labelsize"] = 12  # For y tick labels

# Load the data


# Timestep
dt = 0.04
decimation = 4
# Number of joints (assuming you know this or can derive it from the DataFrame)
num_joints = 4


# ----------------- Plot
# Load the data
csv_file_name = "/home/<USER>/Desktop/Data/isaac_sim_peak_904.csv"
df = pd.read_csv(csv_file_name)

# Remove '.csv' from the file name to use in the title
csv_title = csv_file_name[:-4]

# bucket

"""plt.figure()  # Initialize a new figure for each plot
plt.plot(df['j_pitch_lin_vel_w_z'], label='j_pitch_lin_vel_w_z')
plt.plot(df['j_pitch_ang_vel_x']*df['w_r_pe_y']-df['j_pitch_ang_vel_y']*df['w_r_pe_x'], label='Cross product z')
plt.plot(df['bucket_vel_z'], label='bucket_vel_z')

#plt.plot(df['j_pitch_vel_manual_z'], label='j_pitch_vel_manual_z', linestyle='--')
# Include the CSV title in the plot title
plt.title(f'{csv_title} Bucket  vel z contrib')
#plt.xlim([93,96])
plt.xlabel('Timestep')
plt.ylabel('Velocity')
plt.legend()"""

plot_sim = True

if plot_sim:
    for i in range(num_joints):
        plt.figure()  # Initialize a new figure for each plot
        plt.plot(df[f"dof_vel_{i}"], label=f"dof_vel_{i}")
        plt.plot(df[f"action_cipped_scaled{i}"], label=f"action_cipped_scaled{i}")
        # plt.xlim([91,96])
        # Include the CSV title in the plot title
        plt.title(f"{csv_title} - Joint {i} Velocity Comparison")
        plt.xlabel("Timestep")
        plt.ylabel("Velocity")
        plt.legend()

    """for i in range(num_joints):
        plt.figure()  # Initialize a new figure for each plot
        plt.plot(df[f'tau{i}'], label=f'tau{i}')
        plt.xlim([91,96])
        # Include the CSV title in the plot title
        plt.title(f'{csv_title} -tau{i}')
        plt.xlabel('Timestep')
        plt.ylabel('torque')
        plt.legend()"""

    for i in range(num_joints):
        plt.figure()  # Initialize a new figure for each plot
        plt.plot(df[f"tau_{i}"], label=f"tau{i}")
        plt.plot(df[f"inertial_tau{i}"], label=f"inertial_tau{i}")
        plt.plot(df[f"gravity_tau{i}"], label=f"gravity_tau{i}")
        plt.plot(df[f"soil_tau{i}"], label=f"soil_tau{i}")

        # plt.xlim([85,105])
        # Include the CSV title in the plot title
        plt.title(f"{csv_title} -tau{i}")
        plt.xlabel("Timestep")
        plt.ylabel("torque")
        plt.legend()
    plt.show()

# Load the data
csv_file_name = "/home/<USER>/Desktop/Data/isaac_gym.csv"
df = pd.read_csv(csv_file_name)

# Remove '.csv' from the file name to use in the title
csv_title = csv_file_name[:-4]

"""plt.figure()  # Initialize a new figure for each plot
plt.plot(df['j_pitch_lin_vel_w_z'], label='j_pitch_lin_vel_w_z')
plt.plot(df['j_pitch_ang_vel_x']*df['w_r_pe_y']-df['j_pitch_ang_vel_y']*df['w_r_pe_x'], label='Cross product z')
plt.plot(df['bucket_vel_z'], label='bucket_vel_z')

#plt.plot(df['j_pitch_vel_manual_z'], label='j_pitch_vel_manual_z', linestyle='--')
# Include the CSV title in the plot title
plt.title(f'{csv_title} Bucket  vel z contrib')
#plt.xlim([91,93])
plt.xlabel('Timestep')
plt.ylabel('Velocity')
plt.legend()"""


for i in range(num_joints):
    plt.figure()  # Initialize a new figure for each plot
    plt.plot((df[f"dof_vel_{i}"]), label=f"dof_vel_{i}")
    plt.plot((df[f"action_cipped_scaled{i}"]), label=f"action_cipped_scaled{i}")
    # plt.xlim([91,96])
    # Include the CSV title in the plot title
    plt.title(f"{csv_title} - Joint {i} Velocity Comparison")
    plt.xlabel("Timestep")
    plt.ylabel("Velocity")
    plt.legend()

plt.show()


"""
for i in range(num_joints):
    plt.figure()  # Initialize a new figure for each plot
    plt.plot(df[f'dof_vel_{i}'], label=f'dof_vel_{i}')
    plt.plot(df[f'action_cipped_scaled{i}'], label=f'action_cipped_scaled{i}')
    #plt.xlim([90,95])
    # Include the CSV title in the plot title
    plt.title(f'{csv_title} - Joint {i} Velocity Comparison')
    plt.xlabel('Timestep')
    plt.ylabel('Velocity')
    plt.legend()
plt.show()
"""

"""for i in range(num_joints):
    plt.figure()  # Initialize a new figure for each plot
    plt.plot(df[f'tau_{i}'], label=f'tau{i}')
    plt.plot(df[f'inertial_tau{i}'], label=f'inertial_tau{i}')
    plt.plot(df[f'gravity_tau{i}'], label=f'gravity_tau{i}')
    plt.plot(df[f'soil_tau{i}'], label=f'soil_tau{i}')

    #plt.xlim([85,105])
    # Include the CSV title in the plot title
    plt.title(f'{csv_title} -tau{i}')
    plt.xlabel('Timestep')
    plt.ylabel('torque')
    plt.legend()

plt.show()"""

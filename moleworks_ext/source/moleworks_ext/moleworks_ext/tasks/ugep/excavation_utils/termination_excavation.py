# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
As the termination are handled differently then in Orbit, a custom manager for the excavation environment is implemented.
The main reason is that the termination are separated for postive and negative. Additionaly a reward is computed for
positive termination. The main manager could be changed to handle this.
"""

from __future__ import annotations

import torch
from collections.abc import Sequence
from copy import deepcopy
from typing import TYPE_CHECKING

from isaaclab.utils import class_to_dict

if TYPE_CHECKING:
    from moleworks_ext.tasks.ugep.excavation_env import ExcavationEnv


class Terminations_Excavation:
    def __init__(self, env: ExcavationEnv):
        self.env = env
        self.cfg = env.cfg.terminations_excavation
        self.num_envs = env.num_envs   
        # it can be helpful to understand if failure cases/terminations happen for specific assets
        self.num_different_assets = len(env.cfg.scene.robot.spawn.assets_cfg)
        # Extract asset names (numerical values) from USD paths
        self.asset_names = []
        for asset_cfg in env.cfg.scene.robot.spawn.assets_cfg:
            # Split path and get the numerical folder name
            path_parts = asset_cfg.usd_path.split('/')
            # Find the numerical value before 'urdf/sampled.usd'
            for i, part in enumerate(path_parts):
                if (part == 'urdf' or part == 'usd') and i > 0:
                    self.asset_names.append(path_parts[i-1])
                    break
    
        self.last_env_idx = torch.linspace(0, env.num_envs, self.num_different_assets + 1, dtype=torch.int64)
        self.env_idx_to_asset_idx = torch.zeros(env.num_envs, device=env.device, dtype=torch.int64)
        for i in range(self.num_different_assets):
            self.env_idx_to_asset_idx[self.last_env_idx[i] : self.last_env_idx[i + 1]] = i
        
        self.device = env.device

        # Buffers
        self.pos_terms_cfg = class_to_dict(self.cfg.positive_terminations)
        self.neg_terms_cfg = class_to_dict(self.cfg.negative_terminations)
        self.reset_buf = torch.zeros(self.env.num_envs, device=self.device, dtype=torch.bool)
        self.pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.full_pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.last_full_pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.partial_pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.last_partial_pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.last_close_pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.full_term_mean = torch.zeros(1, device=self.device)
        self.full_term_mean_d = torch.zeros(1, device=self.device)
        self.close_term_mean = torch.zeros(1, device=self.device)

        self.last_neg_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.last_timeout_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.close_pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)

        self.pos_term_rew_buf = torch.zeros(self.num_envs, device=self.device)
        self.neg_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)

        self.time_out_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.time_out_count = torch.zeros(self.num_envs, device=self.device)

        # Buffers of terminated env not due to time out
        self.terminated = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.stuck_count = torch.zeros(self.num_envs, device=self.device, dtype=torch.int)

        # keep track of the lowest bucket pos_w
        self.ep_min_bucket_height = torch.zeros(self.num_envs, device=self.device)
        self.ep_min_bucket_height[:] = float("nan")

        # Util
        self.false_vec = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)

        # create a file where we store some values such as normalized torques, frictions and so when the env terminates
        self.base_termination_data = {
            "normalized_tau": [],
            "friction_force": [],
            "limit_force": [],
            "base_vel": [],
        }

        # negative terminations
        self.neg_term_funs = []
        self.neg_term_names = []
        self.neg_term_enable = []
        for name, enable in self.neg_terms_cfg.items():
            self.neg_term_names.append(name)
            self.neg_term_enable.append(enable)
            name = "_negative_termination_" + name
            self.neg_term_funs.append(getattr(self, name))

        # negative termination episode counts
        self.episode_neg_term_counts = {
            name: torch.zeros(
                self.num_envs,
                device=self.device,
            )
            for name in self.neg_terms_cfg.keys()
        }

        self.last_episode_neg_term = {
            name: torch.zeros(self.num_envs, device=self.device, dtype=bool) for name in self.neg_terms_cfg.keys()
        }
        self.episode_neg_term_buf = deepcopy(self.last_episode_neg_term)

        # positive terminations
        self.pos_term_funs = []
        self.pos_term_names = []
        for name, rew in self.pos_terms_cfg.items():
            self.pos_term_names.append(name)
            name = "_positive_termination_" + name
            self.pos_term_funs.append(getattr(self, name))

        # positive termination episode counts
        self.episode_pos_term_counts = {
            name: torch.zeros(
                self.num_envs,
                device=self.device,
            )
            for name in self.pos_terms_cfg.keys()
        }

        # asset specific dicts
                # Calculate the number of environments per asset
        self.envs_per_asset = self.num_envs // self.num_different_assets

        # Initialize dictionaries to track terminations by asset
        self.episode_neg_term_counts_by_asset = {
            asset: {name: torch.zeros(self.envs_per_asset, device=self.device)
                    for name in self.neg_terms_cfg.keys()}
            for asset in self.asset_names
        }

        self.episode_neg_term_buf_by_asset = {
            asset: {name: torch.zeros(self.envs_per_asset, device=self.device, dtype=torch.bool)
                    for name in self.neg_terms_cfg.keys()}
            for asset in self.asset_names
        }

        # Initialize dictionaries to track positive terminations by asset
        self.episode_pos_term_counts_by_asset = {
            asset: {
                "desired_full": torch.zeros(self.envs_per_asset, device=self.device),
                "desired_close": torch.zeros(self.envs_per_asset, device=self.device),
                "desired_partial": torch.zeros(self.envs_per_asset, device=self.device)
            }
            for asset in self.asset_names
        }

        # Initialize dictionaries to track timeouts by asset
        self.episode_timeout_counts_by_asset = {
            asset: torch.zeros(self.envs_per_asset, device=self.device)
            for asset in self.asset_names
        }

    def reset(self, idxs=...):
        self.stuck_count[idxs] = 0


    def check_termination_old(self):
        # Initialize buffers for this step
        self.reset_buf[:] = False
        self.neg_term_buf[:] = False
        self.pos_term_buf[:] = False
        self.full_pos_term_buf[:] = False
        self.close_pos_term_buf[:] = False
        self.partial_pos_term_buf[:] = False
        self.terminated[:] = False

        for name in self.neg_term_names:
            self.episode_neg_term_buf[name][:] = False

        # Check all negative conditions with priority
        if not self.cfg.disable_negative_termination:
            for i in range(len(self.neg_term_funs)):
                name = self.neg_term_names[i]
                term_mask = self.neg_term_funs[i]() * self.neg_term_enable[i]

                # Update general negative termination buffers
                update_mask = ~self.neg_term_buf & term_mask
                self.neg_term_buf |= update_mask
                self.episode_neg_term_counts[name] += update_mask
                self.episode_neg_term_buf[name][:] = update_mask

                # Update asset-specific buffers
                for asset in range(self.num_different_assets):
                    asset_mask = self.env_idx_to_asset_idx == asset
                    asset_indices = torch.where(asset_mask)[0] % self.envs_per_asset
                    asset_specific_update = update_mask[asset_mask]

                    self.episode_neg_term_counts_by_asset[asset][name][asset_indices] += asset_specific_update
                    self.episode_neg_term_buf_by_asset[asset][name][asset_indices] = asset_specific_update

        # Check all positive conditions
        for i in range(len(self.pos_term_funs)):
            name = self.pos_term_names[i]
            term_mask = self.pos_term_funs[i]()

            self.pos_term_buf |= term_mask
            if name == "desired_full":
                self.full_pos_term_buf[:] = term_mask

            if name == "desired_close":
                self.close_pos_term_buf[:] = term_mask

            if name == "desired_partial":
                self.partial_pos_term_buf[:] = term_mask

        # Existing code to handle positive terminations and timeouts...
        self.reset_buf |= self.neg_term_buf
        self.reset_buf |= self.pos_term_buf
        self.terminated |= self.reset_buf

        # Check timeout conditions
        if self.env.cfg.send_timeouts:
            self.time_out_buf[:] = self.env.episode_length_buf > self.env.max_episode_length
            self.time_out_count += self.time_out_buf
            self.reset_buf |= self.time_out_buf

        # Lowest point in world code...
        self.ep_min_bucket_height = torch.where(
            self.ep_min_bucket_height < self.env.m545_measurements.bucket_pos_w[:, 2],
            self.ep_min_bucket_height,
            self.env.m545_measurements.bucket_pos_w[:, 2]
        )
        self.ep_min_bucket_height[self.reset_buf] = float("nan")

        return self.reset_buf



    def check_termination(self):
        """Check if environments need to be reset.
        Computes the indices to be reset.
        Computes the type of reset: positive, negative, timeout.
        Does not perform the reset here.
        """
        # Initialize buffers for this step
        self.reset_buf[:] = False
        self.neg_term_buf[:] = False
        self.pos_term_buf[:] = False
        self.full_pos_term_buf[:] = False
        self.close_pos_term_buf[:] = False
        self.partial_pos_term_buf[:] = False
        self.terminated[:] = False

        for name in self.neg_term_names:
            self.episode_neg_term_buf[name][:] = False

        # Check all negative conditions with priority
        if not self.cfg.disable_negative_termination:
            for i in range(len(self.neg_term_funs)):
                name = self.neg_term_names[i]
                term_mask = self.neg_term_funs[i]() * self.neg_term_enable[i]
                update_mask = ~self.neg_term_buf & term_mask
                self.neg_term_buf |= update_mask
                self.episode_neg_term_counts[name] += update_mask
                self.episode_neg_term_buf[name][:] = update_mask

                # Update asset-specific buffers
                for asset_name in self.asset_names:
                    asset_mask = self.env_idx_to_asset_idx == self.asset_names.index(asset_name)
                    asset_indices = torch.where(asset_mask)[0] % self.envs_per_asset
                    asset_specific_update = update_mask[asset_mask]

                    self.episode_neg_term_counts_by_asset[asset_name][name][asset_indices] += asset_specific_update
                    self.episode_neg_term_buf_by_asset[asset_name][name][asset_indices] = asset_specific_update

            self.reset_buf |= self.neg_term_buf

        # Check all positive conditions
        for i in range(len(self.pos_term_funs)):
            name = self.pos_term_names[i]
            term_mask = self.pos_term_funs[i]()

            if name == "desired_full":
                self.full_pos_term_buf[:] = term_mask
            elif name == "desired_close":
                # Initialize close buffer first, then mask out full terminations
                self.close_pos_term_buf[:] = term_mask
                self.close_pos_term_buf &= ~self.full_pos_term_buf  # Use logical AND instead of direct indexing
            elif name == "desired_partial":
                self.partial_pos_term_buf[:] = term_mask

        # # Ensure that partial includes close terminations
        # self.partial_pos_term_buf |= self.close_pos_term_buf

        # Update the general positive termination buffer
        self.pos_term_buf |= self.full_pos_term_buf | self.close_pos_term_buf | self.partial_pos_term_buf

        self.reset_buf |= self.pos_term_buf

        # Update asset-specific buffers for positive terminations
        for asset_name in self.asset_names:
            asset_mask = self.env_idx_to_asset_idx == self.asset_names.index(asset_name)
            asset_indices = torch.where(asset_mask)[0] % self.envs_per_asset

            # Full terminations remain unchanged
            self.episode_pos_term_counts_by_asset[asset_name]["desired_full"][asset_indices] += self.full_pos_term_buf[asset_mask]
            # Close terminations exclude full terminations
            self.episode_pos_term_counts_by_asset[asset_name]["desired_close"][asset_indices] += (
                self.close_pos_term_buf[asset_mask] & ~self.full_pos_term_buf[asset_mask]
            )
            # Partial terminations exclude both full and close terminations
            self.episode_pos_term_counts_by_asset[asset_name]["desired_partial"][asset_indices] += (
                self.partial_pos_term_buf[asset_mask] 
                & ~self.full_pos_term_buf[asset_mask] 
                & ~self.close_pos_term_buf[asset_mask]
            )
        # This buffers saves reset before it gets treated by the timeouts
        self.terminated |= self.reset_buf

        # Check timeout
        if self.env.cfg.send_timeouts:
            self.time_out_buf[:] = self.env.episode_length_buf > self.env.max_episode_length
            self.time_out_count += self.time_out_buf
            self.reset_buf |= self.time_out_buf

            # Update asset-specific buffers for timeouts
            for asset_name in self.asset_names:
                asset_mask = self.env_idx_to_asset_idx == self.asset_names.index(asset_name)
                asset_indices = torch.where(asset_mask)[0] % self.envs_per_asset
                self.episode_timeout_counts_by_asset[asset_name][asset_indices] += self.time_out_buf[asset_mask]

        # LOWEST POINT IN WORLD
        self.ep_min_bucket_height = torch.where(
            self.ep_min_bucket_height < self.env.m545_measurements.bucket_pos_w[:, 2],
            self.ep_min_bucket_height,
            self.env.m545_measurements.bucket_pos_w[:, 2],
        )
        self.ep_min_bucket_height[self.reset_buf] = float("nan")

        return self.reset_buf

    def compute_reward(self):
        # Negative termination reward only: 1) if no timeout
        self.neg_term_buf *= ~self.time_out_buf
        neg_term_rew = self.neg_term_buf * self.cfg.neg_term_rew

        # Positive termination reward only: 1) if no timeout and 2) no negative termination
        pos_term_mask = (self.pos_term_buf * ~self.neg_term_buf) * ~self.time_out_buf

        # Update terminations with hierarchy: full > close > partial
        # Full terminations take precedence
        self.full_pos_term_buf *= pos_term_mask
        self.episode_pos_term_counts["desired_full"] += self.full_pos_term_buf

        # Close terminations (excluding full terminations)
        self.close_pos_term_buf *= pos_term_mask & ~self.full_pos_term_buf
        self.episode_pos_term_counts["desired_close"] += self.close_pos_term_buf

        # Partial terminations (excluding both full and close terminations)
        self.partial_pos_term_buf *= pos_term_mask & ~(self.full_pos_term_buf | self.close_pos_term_buf)
        self.episode_pos_term_counts["desired_partial"] += self.partial_pos_term_buf

        # Compute positive termination rewards
        pos_term_rew = (
            self.full_pos_term_buf * self.cfg.positive_terminations.desired_full
            + self.partial_pos_term_buf * self.cfg.positive_terminations.desired_partial
            + self.close_pos_term_buf * self.cfg.positive_terminations.desired_close * (
                1 + torch.min(
                    self.env.soil.fill_ratio.squeeze() / self.env.curriculum_excavation.curr_term_fill_ratio, 
                    torch.tensor(1.0, device=self.device)
                )
            )
        )

        # Logging and curriculum updates remain unchanged
        term_ids = self.reset_buf.nonzero(as_tuple=False).flatten()
        self.last_full_pos_term_buf[term_ids] = self.full_pos_term_buf[term_ids]
        self.last_close_pos_term_buf[term_ids] = self.close_pos_term_buf[term_ids]
        self.last_partial_pos_term_buf[term_ids] = self.partial_pos_term_buf[term_ids]
        self.full_term_mean_d[:] = torch.mean(self.last_full_pos_term_buf.to(torch.float32)) - self.full_term_mean
        self.full_term_mean[:] = torch.mean(self.last_full_pos_term_buf.to(torch.float32))
        self.close_term_mean[:] = torch.mean(self.last_close_pos_term_buf.to(torch.float32))

        self.last_timeout_term_buf[term_ids] = self.time_out_buf[term_ids]
        self.last_neg_term_buf[term_ids] = self.neg_term_buf[term_ids]

        for name in self.neg_term_names:
            self.episode_neg_term_buf[name][term_ids] *= ~self.time_out_buf[term_ids]
            self.last_episode_neg_term[name][term_ids] = self.episode_neg_term_buf[name][term_ids]

        return neg_term_rew + pos_term_rew

    """
    Termination Conditions
    """
    def _negative_termination_torque_limits(self):
        tau_normalized = (self.env.torques - self.env.m545_measurements.tau_min) / (self.env.m545_measurements.tau_max - self.env.m545_measurements.tau_min)
        mask = torch.any((tau_normalized > 1.0) | (tau_normalized < -1.0), dim=-1) # this should be 0!!!!
        return mask

    def _negative_termination_bucket_vel(self):
        # todo: add arm scale to obs
        # return torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        # mask = self.env.m545_measurements.bucket_vel_norm > (self.cfg.max_bucket_vel * self.env.m545_measurements.arm_scale.squeeze())
        mask = self.env.m545_measurements.bucket_vel_norm > self.cfg.termination_bucket_vel
        if mask.any():
            violating_velocities = self.env.m545_measurements.bucket_vel_norm[mask]
        #     print("max vel allowed ", self.cfg.max_bucket_vel * self.env.m545_measurements.arm_scale[0])
            # print('_negative_termination_bucket_vel Violations:', violating_velocities)
        #     # # print the joint values for the violating velocities
        # print('_negative_termination_bucket_vel Violating Joint Values:', self.env.scene.articulations['robot'].data.joint_pos[mask])
        return mask

    def _negative_termination_bucket_aoa(self):
        bad_aoa = self.env.m545_measurements.bucket_aoa < -0.00
        fast_enough = self.env.m545_measurements.bucket_vel_norm > self.cfg.bucket_vel_aoa_threshold  # 0.075
        in_soil = (self.env.soil.bucket_depth > 0.05).squeeze()
        # print percentage of in_soil
        # print("Percentage of in_soil: ", torch.mean(in_soil.to(torch.float32)))
        mask = torch.logical_and(bad_aoa, torch.logical_and(fast_enough, in_soil))
        # set all mask to true
        # mask = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        # print('_negative_termination_bucket_aoa', mask)
        # if mask.any():
        #     violating_aoas = self.env.m545_measurements.bucket_aoa[mask]
        #     bucket_depths = self.env.soil.bucket_depth[mask]\
        #     print('_negative_termination_bucket_aoa Violations:', violating_aoas, 'Bucket Depths:', bucket_depths)
        # set all the mask to false for all the envs
        # mask = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        return mask

    def _negative_termination_base_vel(self):  # Might be Always true
        # return torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        base_vel = self.env.scene.articulations["robot"].data.root_lin_vel_w
        base_vel_excluding_y = torch.stack((base_vel[:, 0], base_vel[:, 2]), dim=-1)
        mask = torch.linalg.norm(base_vel_excluding_y, dim=-1) > self.cfg.max_base_vel  # 0.1
        # print the values with a positive mask 
        if mask.any():
            violating_velocities = base_vel[mask]
            print('_negative_termination_base_vel Violations:', violating_velocities)
        # print('_negative_termination_base_vel', mask, 'Base_vel :', torch.linalg.norm(base_vel, dim=-1))
        # print out the values of the observations: base_friction_tau, base_lift_up_tau

        # friction_tau = base_friction_torque_limits(self.env)
        # lift_up_tau = base_pullup_torque_limits(self.env)
        # if mask.any():
        #     print("Failure Base friction tau: ", friction_tau[mask])
        #     print("Failure Base lift up tau: ", lift_up_tau[mask])
        #     tau_normalized = (self.env.torques[mask] - self.env.m545_measurements.tau_min[mask]) / (self.env.m545_measurements.tau_max[mask] - self.env.m545_measurements.tau_min[mask])
        #     print("Failure Base friction tau normalized: ", tau_normalized)
        return mask

    def _negative_termination_limit_forces(self):
        # - because of reaction force 
        soil_forces_w = self.env.soil.forces.RF_w
        soil_forces_normalized = torch.zeros_like(soil_forces_w)
        soil_forces_normalized[:, 0] = torch.abs(- soil_forces_w[:, 0] / self.env.m545_measurements.force_limits[:, 0])
        soil_forces_normalized[:, 2] = torch.abs(- soil_forces_w[:, 2] / self.env.m545_measurements.force_limits[:, 2])
        mask_x = soil_forces_normalized[:, 0] > 1.0
        mask_z = soil_forces_normalized[:, 2] > 1.0
        mask = torch.logical_or(mask_x, mask_z)
        return mask

    def _negative_termination_joint_vel(self):
        mask = torch.abs(self.env.scene.articulations["robot"].data.joint_vel).any(dim=-1) > self.cfg.max_joint_vel
        # print('_negative_termination_joint_vel', mask)
        return mask

    def _negative_termination_bucket_height(self):  # Might be Always true
        # return torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        cond_a = self.env.soil.fill_ratio <= self.cfg.term_above_soil_fill_ratio
        cond_b = -self.env.soil.bucket_depth > self.cfg.max_bucket_height_above_soil
        # print("bucket depth above soil", -self.env.soil.bucket_depth)
        mask = torch.logical_and(cond_a, cond_b).squeeze()
        # print('_negative_termination_bucket_height', mask)
        # print('_negative_termination_bucket_height conda a', cond_a)
        # print('_negative_termination_bucket_height conda b', cond_b)
        return mask

    def _negative_termination_invalid_soil_model(self):
        # already checked at the end of inter decimation loop
        # return torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        mask = self.env.inter_decimation_soil_model_invalid.squeeze()
        # print('_negative_termination_invalid_soil_model', mask)
        return mask

    def _negative_termination_self_collision(self):
        # only bucket with base/boom can collide
        # return torch.linalg.norm(self.env.m545_asset.bucket_collision_f, dim=-1) > 1.0
        mask = self.env.inter_decimation_self_collision.squeeze()
        # print('_negative_termination_self_collision', mask)
        return mask

    def _negative_termination_max_depth(self):
        mask = (
            self.env.m545_measurements.bucket_pos_w[:, 2].unsqueeze(-1)
            < (
                self.env.soil.get_max_depth_height_at_pos(self.env.m545_measurements.bucket_pos_w[:, 0].unsqueeze(-1))
                - self.cfg.max_depth_overshoot
            )
        ).squeeze()
        # print('_negative_termination_max_depth', mask)
        return mask

    def _negative_termination_pullup(self):
        # return  torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        # mask = (self.env.scene.env_origins[:, 0]-self.env.m545_measurements.bucket_pos_w[:, 0] < self.env.pullup_dist).squeeze() # Or rotate
        mask = (torch.abs(self.env.m545_measurements.bucket_pos_w[:, 0]) < self.env.pullup_dist).squeeze()  # Or rotate

        # print('_negative_termination_pullup', mask)
        return mask

    def _negative_termination_spilling_soil(self):
        # return  torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        cond_a = (self.env.soil.fill_ratio < self.env.last_fill_ratio).squeeze()
        cond_b = (self.env.m545_measurements.bucket_vel_norm > self.cfg.bucket_vel_spillig_threshold).squeeze()
        mask = torch.logical_and(cond_a, cond_b)
        # print('_negative_termination_spilling_soil', mask)
        return mask

    def _negative_termination_stuck(self):
        """
        Terminates the episode when the bucket's velocity is too slow for a consecutive number of steps,
        indicating that the bucket is stuck.
        """
        too_slow = self.env.m545_measurements.bucket_vel_norm < 0.05
        self.stuck_count += too_slow
        # if not too slow, reset
        self.stuck_count *= too_slow
        in_soil = (self.env.soil.bucket_depth > 0.0).squeeze()
        mask = self.stuck_count > 3
        mask &= in_soil
        return mask

    def _positive_termination_desired_close(self):
        # return  torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        # need to provide pullup band, otherwise it can always pullup and not care about filling the shovel if it is possible

        # not needed, negative condition checked first!
        # cannot be closer than pullup_dist if terminal condition is active!
        # if not active, it is ithe intended behavior to come as close as it likes!
        # (not needed) close &= self.env.m545_asset.bucket_pos_gac[:, 0] > self.env.pullup_dist

        close = (
            torch.abs(self.env.m545_measurements.bucket_pos_w[:, 0])
            < self.env.pullup_dist + self.env.curriculum_excavation.curr_pullup_band
        )

        high = torch.where(
            torch.isnan(self.ep_min_bucket_height),
            self.false_vec,
            (self.env.m545_measurements.bucket_pos_w[:, 2] - self.ep_min_bucket_height)
            > self.env.curriculum_excavation.curr_term_height_above_soil,  # dist to max depth
        ).squeeze()

        curl = (self.env.m545_measurements.bucket_ang_gac < self.cfg.max_curl_ang_gac).squeeze()
        curl &= (self.env.m545_measurements.bucket_ang_gac > self.cfg.min_curl_ang_gac).squeeze()

        # filling
        a_bit_full = (self.env.soil.fill_ratio > self.cfg.term_above_soil_fill_ratio).squeeze()

        mask = close
        mask &= high
        mask &= curl
        mask &= a_bit_full

        return mask

    def _positive_termination_desired_full(self):
        # return  torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        full = (self.env.soil.fill_ratio > self.env.curriculum_excavation.curr_term_fill_ratio).squeeze()

        # negative depth = out of soil
        # high = (-self.env.soil.get_bucket_depth() > self.env.curr_manager.curr_term_height_above_soil).squeeze()
        high = torch.where(
            torch.isnan(self.ep_min_bucket_height),
            self.false_vec,
            (self.env.m545_measurements.bucket_pos_w[:, 2] - self.ep_min_bucket_height)
            > self.env.curriculum_excavation.curr_term_height_above_soil,  # dist to max depth
        ).squeeze()

        curl = (self.env.m545_measurements.bucket_ang_gac < self.cfg.max_curl_ang_gac).squeeze()
        curl &= (self.env.m545_measurements.bucket_ang_gac > self.cfg.min_curl_ang_gac).squeeze()

        mask = full
        mask &= high
        mask &= curl
        return mask
    
    def _positive_termination_desired_partial(self):
        """Similar to full termination but with partial filling of the bucket."""
        # Check if bucket is partially filled (above minimum threshold but below full threshold)
        a_bit_full = (self.env.soil.fill_ratio > self.cfg.term_above_soil_fill_ratio).squeeze()
        
        # Check height condition (same as full termination)
        high = torch.where(
            torch.isnan(self.ep_min_bucket_height),
            self.false_vec,
            (self.env.m545_measurements.bucket_pos_w[:, 2] - self.ep_min_bucket_height)
            > self.env.curriculum_excavation.curr_term_height_above_soil,
        ).squeeze()

        # Check curl angle (same as full termination)
        curl = (self.env.m545_measurements.bucket_ang_gac < self.cfg.max_curl_ang_gac).squeeze()
        curl &= (self.env.m545_measurements.bucket_ang_gac > self.cfg.min_curl_ang_gac).squeeze()

        mask = a_bit_full
        mask &= high
        mask &= curl
        return mask

    def reset_excavation(self, env_ids: Sequence[int] | None = None) -> dict[str, torch.Tensor]:
        """Returns the episodic counts of episode_neg_term_counts_by_assetindividual termination terms.
            Args:
            env_ids: The environment ids. Defaults to None, in which case
                all environments are considered.

        Returns:
            Dictionary of episodic sum of individual reward terms.
        """
        # resolve environment ids
        if env_ids is None:
            env_ids = slice(None)

        extras = {}

        forword = "Episode Termination/"
        extras[forword + "last_timeout"] = torch.mean(self.last_timeout_term_buf.to(torch.float32)).item()
        extras[forword + "last_total_negative"] = torch.mean(self.last_neg_term_buf.to(torch.float32)).item()

        for name in self.neg_term_names:
            extras[forword + "last_negative_" + name] = torch.mean(
                self.last_episode_neg_term[name].to(torch.float32)
            ).item()

        extras[forword + "last_full"] = self.full_term_mean.item()
        extras[forword + "last_close"] = self.close_term_mean.item()
        # Add last_partial
        extras[forword + "last_partial"] = torch.mean(self.last_partial_pos_term_buf.to(torch.float32)).item()

        # Epidode lengths
        forword = "Episode length/"
        ids = (self.full_pos_term_buf > 0).nonzero(as_tuple=False)
        mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
        extras[forword + "full"] = mean.item() if torch.isfinite(mean) else 0.0

        ids = (self.close_pos_term_buf > 0).nonzero(as_tuple=False)
        mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
        extras[forword + "close"] = mean.item() if torch.isfinite(mean) else 0.0

        # Add partial termination length
        ids = (self.partial_pos_term_buf > 0).nonzero(as_tuple=False)
        mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
        extras[forword + "partial"] = mean.item() if torch.isfinite(mean) else 0.0

        ids = (self.time_out_buf > 0).nonzero(as_tuple=False)
        mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
        extras[forword + "timeout"] = mean.item() if torch.isfinite(mean) else 0.0

        ids = (self.neg_term_buf > 0).nonzero(as_tuple=False)
        mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
        extras[forword + "tot_negative"] = mean.item() if torch.isfinite(mean) else 0.0

        for name in self.neg_term_names:
            ids = (self.episode_neg_term_buf[name] > 0).nonzero(as_tuple=False)
            mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
            extras[forword + "neg_" + name] = mean.item() if torch.isfinite(mean) else 0.0

        # # Asset-specific episode lengths for positive terminations and timeouts
        # if self.cfg.log_asset_specific_lengths:
        #     for asset in range(self.num_different_assets):
        #         asset_mask = self.env_idx_to_asset_idx == asset
        #         asset_indices = torch.where(asset_mask)[0] % self.envs_per_asset

        #         # Full positive termination lengths
        #         ids = (self.episode_pos_term_counts_by_asset[asset]["desired_full"] > 0).nonzero(as_tuple=False)
        #         mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
        #         extras[forword + "asset_" + str(asset) + "_full"] = mean.item() if torch.isfinite(mean) else 0.0

        #         # Close positive termination lengths
        #         ids = (self.episode_pos_term_counts_by_asset[asset]["desired_close"] > 0).nonzero(as_tuple=False)
        #         mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
        #         extras[forword + "asset_" + str(asset) + "_close"] = mean.item() if torch.isfinite(mean) else 0.0

        #         # Timeout lengths
        #         ids = (self.episode_timeout_counts_by_asset[asset] > 0).nonzero(as_tuple=False)
        #         mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
        #         extras[forword + "asset_" + str(asset) + "_timeout"] = mean.item() if torch.isfinite(mean) else 0.0

        return extras
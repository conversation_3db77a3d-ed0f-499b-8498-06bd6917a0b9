import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.mixture import GaussianMixture

# Load the data
file_path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/merged_excavator_info_processed.csv'
merged_data = pd.read_csv(file_path)

# Function to fit a GMM and plot the results
def fit_gmm_and_plot(data, n_components=2, title=''):
    gmm = GaussianMixture(n_components=n_components, random_state=0)
    gmm.fit(data.reshape(-1, 1))

    x = np.linspace(data.min(), data.max(), 1000).reshape(-1, 1)
    logprob = gmm.score_samples(x)
    pdf = np.exp(logprob)

    plt.figure(figsize=(10, 6))
    plt.hist(data, bins=30, density=True, alpha=0.6, color='g', edgecolor='black')
    plt.plot(x, pdf, '-k', lw=2)
    plt.title(title)
    plt.xlabel('Value')
    plt.ylabel('Density')
    plt.show()

    return gmm

# Fitting GMMs to each parameter
reach = merged_data['Max Reach Ground'].values
engine_power = merged_data['Engine Power'].values
boom_length = merged_data['Boom Length'].values
dipper_length = merged_data['Dippcder Length'].values
shovel_length = merged_data['Shovel Length'].values
bucket_capacity = merged_data['Bucket Capacity'].values
base_mass = merged_data['Overall Base Mass'].values
track_length = merged_data['Track Length'].values
boom_mass = merged_data['Boom Mass'].values
dipper_mass = merged_data['Dipper Mass'].values
shovel_mass = merged_data['Shovel Mass'].values

gmm_reach = fit_gmm_and_plot(reach, n_components=3, title='Max Reach Ground')
gmm_engine_power = fit_gmm_and_plot(engine_power, n_components=3, title='Engine Power')
gmm_boom_length = fit_gmm_and_plot(boom_length, n_components=3, title='Boom Length')
gmm_dipper_length = fit_gmm_and_plot(dipper_length, n_components=3, title='Dipper Length')
gmm_shovel_length = fit_gmm_and_plot(shovel_length, n_components=3, title='Shovel Length')
gmm_bucket_capacity = fit_gmm_and_plot(bucket_capacity, n_components=3, title='Bucket Capacity')
gmm_base_mass = fit_gmm_and_plot(base_mass, n_components=3, title='Base Mass')
gmm_track_length = fit_gmm_and_plot(track_length, n_components=3, title='Track Length')
gmm_boom_mass = fit_gmm_and_plot(boom_mass, n_components=3, title='Boom Mass')
gmm_dipper_mass = fit_gmm_and_plot(dipper_mass, n_components=3, title='Dipper Mass')
gmm_shovel_mass = fit_gmm_and_plot(shovel_mass, n_components=3, title='Shovel Mass')

# Example of sampling new data points
n_samples = 1000
new_reach_samples = gmm_reach.sample(n_samples)[0]
new_engine_power_samples = gmm_engine_power.sample(n_samples)[0]
new_boom_length_samples = gmm_boom_length.sample(n_samples)[0]
new_dipper_length_samples = gmm_dipper_length.sample(n_samples)[0]
new_shovel_length_samples = gmm_shovel_length.sample(n_samples)[0]
new_bucket_capacity_samples = gmm_bucket_capacity.sample(n_samples)[0]
new_base_mass_samples = gmm_base_mass.sample(n_samples)[0]
new_track_length_samples = gmm_track_length.sample(n_samples)[0]
new_boom_mass_samples = gmm_boom_mass.sample(n_samples)[0]
new_dipper_mass_samples = gmm_dipper_mass.sample(n_samples)[0]
new_shovel_mass_samples = gmm_shovel_mass.sample(n_samples)[0]

# Combine the samples into a DataFrame
new_samples_df = pd.DataFrame({
    'Max Reach Ground': new_reach_samples.flatten(),
    'Engine Power': new_engine_power_samples.flatten(),
    'Boom Length': new_boom_length_samples.flatten(),
    'Dipper Length': new_dipper_length_samples.flatten(),
    'Shovel Length': new_shovel_length_samples.flatten(),
    'Bucket Capacity': new_bucket_capacity_samples.flatten(),
    'Base Mass': new_base_mass_samples.flatten(),
    'Track Length': new_track_length_samples.flatten(),
    'Boom Mass': new_boom_mass_samples.flatten(),
    'Dipper Mass': new_dipper_mass_samples.flatten(),
    'Shovel Mass': new_shovel_mass_samples.flatten()
})

print(new_samples_df.head())
import pandas as pd
import scipy.stats as stats
import numpy as np
import os
import matplotlib.pyplot as plt
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
from sklearn.preprocessing import PolynomialFeatures
from sklearn.tree import DecisionTreeRegressor
from sklearn.ensemble import RandomForestRegressor


# Fit linear regression models
def fit_linear_model(x, y):
    model = LinearRegression()
    if len(x.shape) == 1:
        x = x.reshape(-1, 1)  # Reshape if only one feature is provided
    model.fit(x, y)
    return model


# Evaluate model
def evaluate_model(model, x, y_actual):
    if len(x.shape) == 1:
        x = x.reshape(-1, 1)  # Reshape if only one feature is provided
    y_predicted = model.predict(x)
    r2 = r2_score(y_actual, y_predicted)
    return y_predicted, r2


# Plot predicted vs actual values
def plot_predicted_vs_actual(actual, predicted, title):
    plt.figure(figsize=(10, 6))
    plt.scatter(actual, predicted, edgecolors=(0, 0, 0))
    plt.plot([actual.min(), actual.max()], [actual.min(), actual.max()], 'k--', lw=2)
    plt.xlabel('Actual')
    plt.ylabel('Predicted')
    plt.title(title)
    plt.show()

# Load already merged data
file_path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/merged_excavator_info_processed.csv'
merged_data = pd.read_csv(file_path)

reach = merged_data['Max Reach Ground'].values

# Fit and evaluate linear models
engine_power_model = fit_linear_model(reach, merged_data['Engine Power'].values)
boom_length_model = fit_linear_model(reach, merged_data['Boom Length'].values)
dipper_length_model = fit_linear_model(reach, merged_data['Dipper Length'].values)
shovel_length_model = fit_linear_model(reach, merged_data['Shovel Length'].values)
bucket_capacity_model = fit_linear_model(merged_data[['Engine Power', 'Max Reach Ground']].values, merged_data['Bucket Capacity'].values)
base_mass_model = fit_linear_model(reach, merged_data['Base Mass'].values)
track_length_model = fit_linear_model(merged_data[['Max Reach Ground', 'Base Mass']].values, merged_data['Track Length'].values)
boom_mass_model = fit_linear_model(reach, merged_data['Boom Mass'].values)
dipper_mass_model = fit_linear_model(reach, merged_data['Dipper Mass'].values)
shovel_mass_model = fit_linear_model(reach, merged_data['Shovel Mass'].values)

predicted_engine_power, r2_engine_power = evaluate_model(engine_power_model, reach, merged_data['Engine Power'])
predicted_boom_length, r2_boom_length = evaluate_model(boom_length_model, reach, merged_data['Boom Length'])
predicted_dipper_length, r2_dipper_length = evaluate_model(dipper_length_model, reach, merged_data['Dipper Length'])
predicted_shovel_length, r2_shovel_length = evaluate_model(shovel_length_model, reach, merged_data['Shovel Length'])
predicted_bucket_capacity, r2_bucket_capacity = evaluate_model(bucket_capacity_model, merged_data[['Engine Power', 'Max Reach Ground']].values, merged_data['Bucket Capacity'])
predicted_base_mass, r2_base_mass = evaluate_model(base_mass_model, reach, merged_data['Base Mass'])
predicted_track_length, r2_track_length = evaluate_model(track_length_model, merged_data[['Max Reach Ground', 'Base Mass']].values, merged_data['Track Length'])
predicted_boom_mass, r2_boom_mass = evaluate_model(boom_mass_model, reach, merged_data['Boom Mass'])
predicted_dipper_mass, r2_dipper_mass = evaluate_model(dipper_mass_model, reach, merged_data['Dipper Mass'])
predicted_shovel_mass, r2_shovel_mass = evaluate_model(shovel_mass_model, reach, merged_data['Shovel Mass'])

print(f"R-squared for Engine Power: {r2_engine_power}")
print(f"R-squared for Boom Length: {r2_boom_length}")
print(f"R-squared for Dipper Length: {r2_dipper_length}")
print(f"R-squared for Shovel Length: {r2_shovel_length}")
print(f"R-squared for Bucket Capacity: {r2_bucket_capacity}")
print(f"R-squared for Base Mass: {r2_base_mass}")
print(f"R-squared for Track Length: {r2_track_length}")
print(f"R-squared for Boom Mass: {r2_boom_mass}")
print(f"R-squared for Dipper Mass: {r2_dipper_mass}")
print(f"R-squared for Shovel Mass: {r2_shovel_mass}")



# Plot predicted vs actual values
plot_predicted_vs_actual(merged_data['Engine Power'], predicted_engine_power, 'Engine Power')
plot_predicted_vs_actual(merged_data['Boom Length'], predicted_boom_length, 'Boom Length')
plot_predicted_vs_actual(merged_data['Dipper Length'], predicted_dipper_length, 'Dipper Length')
plot_predicted_vs_actual(merged_data['Shovel Length'], predicted_shovel_length, 'Shovel Length')
plot_predicted_vs_actual(merged_data['Bucket Capacity'], predicted_bucket_capacity, 'Bucket Capacity')
plot_predicted_vs_actual(merged_data['Base Mass'], predicted_base_mass, 'Base Mass')
plot_predicted_vs_actual(merged_data['Track Length'], predicted_track_length, 'Track Length')
plot_predicted_vs_actual(merged_data['Boom Mass'], predicted_boom_mass, 'Boom Mass')
plot_predicted_vs_actual(merged_data['Dipper Mass'], predicted_dipper_mass, 'Dipper Mass')
plot_predicted_vs_actual(merged_data['Shovel Mass'], predicted_shovel_mass, 'Shovel Mass')
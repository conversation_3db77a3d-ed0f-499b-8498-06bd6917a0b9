import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from sklearn.mixture import GaussianMixture

# Load the data
file_path = '/home/<USER>/git/orbit/source/extensions/omni.isaac.orbit_tasks/omni/isaac/orbit_tasks/ugep/excavation_utils/data/merged_excavator_info_processed.csv'
merged_data = pd.read_csv(file_path)

# Dictionary specifying the n_components for each variable
n_components_dict = {
    'Engine Power': 3,
    'Boom Length': 2,
    'Dipper Length': 2,
    'Shovel Length': 2,
    'Bucket Capacity': 3,
    'Base Mass': 2,
    'Track Length': 2,
    'Boom Mass': 2,
    'Dipper Mass': 2,
    'Shovel Mass': 2
}


# Function to fit a GMM
def fit_gmm(data, n_components=2):
    gmm = GaussianMixture(n_components=n_components, random_state=0)
    gmm.fit(data)
    return gmm


# Function to plot 1D GMM
def plot_gmm_1d(gmm, data, title=''):
    x = np.linspace(data.min(), data.max(), 1000).reshape(-1, 1)
    logprob = gmm.score_samples(x)
    pdf = np.exp(logprob)
    plt.figure(figsize=(10, 6))
    plt.hist(data, bins=30, density=True, alpha=0.6, color='g', edgecolor='black')
    plt.plot(x, pdf, '-k', lw=2)
    plt.title(title)
    plt.xlabel('Value')
    plt.ylabel('Density')
    plt.show()


# Function to plot 2D GMM
def plot_gmm_2d(gmm, data, title=''):
    x = np.linspace(data[:, 0].min(), data[:, 0].max(), 100)
    y = np.linspace(data[:, 1].min(), data[:, 1].max(), 100)
    X, Y = np.meshgrid(x, y)
    XX = np.array([X.ravel(), Y.ravel()]).T
    Z = -gmm.score_samples(XX)
    Z = Z.reshape(X.shape)
    plt.figure(figsize=(10, 6))
    plt.contour(X, Y, np.exp(-Z), levels=14, linewidths=1.5)
    plt.scatter(data[:, 0], data[:, 1], s=3)
    plt.title(title)
    plt.xlabel('Reach')
    plt.ylabel('Variable')
    plt.colorbar()
    plt.show()

# Function to remove extreme outliers
def remove_outliers(data, z_thresh=3):
    mean = np.mean(data, axis=0)
    std = np.std(data, axis=0)
    z_scores = np.abs((data - mean) / std)
    return data[(z_scores < z_thresh).all(axis=1)]

# Fit and plot GMM for 'Max Reach Ground'
reach = merged_data['Max Reach Ground'].values.reshape(-1, 1)
reach = remove_outliers(reach)
gmm_reach = fit_gmm(reach)
plot_gmm_1d(gmm_reach, reach.flatten(), 'Max Reach Ground')

# Conditional GMMs and 2D visualizations
variables = ['Engine Power', 'Boom Length', 'Dipper Length', 'Shovel Length', 'Bucket Capacity', 'Base Mass', 'Track Length', 'Boom Mass', 'Dipper Mass', 'Shovel Mass']
gmm_conditionals = {}

plt.scatter(merged_data['Engine Power'], merged_data['Max Reach Ground'], s=3)
plt.xlabel('Engine Power')
plt.ylabel('Max Reach Ground')
plt.title('Scatter Plot of Engine Power vs Max Reach Ground')
plt.show()

# Fit and plot 2D GMMs for each variable conditioned on 'Reach'
for variable in variables:
    data = merged_data[[variable, 'Max Reach Ground']].values
    data = remove_outliers(data)
    n_components = n_components_dict.get(variable, 2)
    gmm = fit_gmm(data, n_components=n_components)
    gmm_conditionals[variable] = gmm
    plot_gmm_2d(gmm, data, f'Joint Distribution of Reach and {variable}')

# Sampling new data points based on the reach
n_samples = 1000
new_reach_samples = gmm_reach.sample(n_samples)[0]

# Generate conditional samples for each variable based on new 'Reach' samples
new_samples = {'Max Reach Ground': new_reach_samples.flatten()}
for variable, gmm in gmm_conditionals.items():
    new_data = np.column_stack((np.zeros_like(new_reach_samples), new_reach_samples))
    component_assignments = gmm.predict(new_data)
    conditional_samples = []
    for i in range(n_samples):
        component = component_assignments[i]
        mean = gmm.means_[component]
        cov = gmm.covariances_[component]
        mean_cond = mean[0]
        cov_cond = cov[0, 0]
        conditional_samples.append(np.random.normal(mean_cond, np.sqrt(cov_cond)))
    new_samples[variable] = conditional_samples

# Combine the samples into a DataFrame
new_samples_df = pd.DataFrame(new_samples)
print(new_samples_df.head())
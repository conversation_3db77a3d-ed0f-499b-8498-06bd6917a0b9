import matplotlib.pyplot as plt
import numpy as np
import torch
from matplotlib.backends.backend_pdf import PdfPages
from typing import Tuple

import sympy as sp
# from legged_gym.utils.config_utils import class_to_dict
import math
import numpy as np # For comparing angles robustly

class PID:
    """Vectorized PID controller"""

    def __init__(self, dt, n_envs, cfg_class, device):
        self.dt = dt
        self.n_envs = n_envs
        joint_names = ["boom", "dipper", "pitch"]
        # dof = len(cfg_class)
        # if dof != len(joint_names):
        #     raise ValueError("[PID] wrong number of PID parameters")

        dof = 3
        self.kp = torch.zeros(dof, device=device)
        self.kd = torch.zeros(dof, device=device)
        self.ki = torch.zeros(dof, device=device)
        self.max_effort = torch.zeros(dof, device=device)
        self.min_effort = torch.zeros(dof, device=device)
        self.max_i = torch.zeros(dof, device=device)
        self.min_i = torch.zeros(dof, device=device)

        self.integral = torch.zeros(n_envs, dof, device=device)
        self.prevErr = torch.zeros(n_envs, dof, device=device)

        # as of python 3.6 dicts remember order!!
        for i, name in enumerate(joint_names):
            cfg = class_to_dict(eval(f"{cfg_class=}".split("=")[0] + "." + name))
            self.kp[i] = cfg["p"]
            self.ki[i] = cfg["i"]
            self.kd[i] = cfg["d"]
            self.max_effort[i] = cfg["eff_max"]
            self.min_effort[i] = cfg["eff_min"]
            self.max_i[i] = cfg["i_max"]
            self.min_i[i] = cfg["i_min"]

    def reset(self, idxs=...):
        self.integral[idxs] = 0.0
        self.prevErr[idxs] = 0.0

    def advance(self, des, meas):
        self.err = des - meas
        self.integral += self.err * self.dt
        self.integral = torch.clip(self.integral, self.min_i, self.max_i)
        derivative = (self.err - self.prevErr) / self.dt
        self.prevErr = self.err
        out = self.kp * self.err + self.ki * self.integral + self.kd * derivative

        return torch.clip(out, self.min_effort, self.max_effort)

    def setGains(self, kp, ki, kd, idx):
        self.kp[idx] = kp
        self.ki[idx] = ki
        self.kd[idx] = kd
        print("New gains: ", kp.item(), " / ", ki.item(), " / ", kd.item())


def u_rand(size, min, max, device):
    return min + (max - min) * torch.rand(size, device=device)


def deg2rad(deg):
    return deg / 180.0 * np.pi


def get_log_dict(dictionary, num_samples, dim=None):
    """
    Returns a dictionary with the same keys as the input dictionary, but with
    corresponding numpy arrays of shape (num_samples, dim) filled with NaNs.

    Args:
        dictionary (dict): Input dictionary to be copied.
        num_samples (int): The number of rows for each value array.
        dim (int or None): The number of columns for each value array.
            If None, the last dimension of the input value array will be used.

    Returns:
        A new dictionary with the same keys as the input dictionary,
        but with corresponding numpy arrays of shape (num_samples, dim)
        filled with NaNs.
    """
    result_dict = {
        k: np.full((num_samples, dim if dim else v.shape[-1]), np.nan)
        for k, v in dictionary.items()
    }
    return result_dict


def multipage(filename, figs=None, dpi=200):
    pp = PdfPages(filename)
    if figs is None:
        figs = [plt.figure(n) for n in plt.get_fignums()]
    for fig in figs:
        fig.savefig(pp, format="pdf", dpi=dpi, bbox_inches="tight")
    pp.close()


class NaNValueError(Exception):
    def __init__(self, message):
        self.message = message
        super().__init__(self.message)


import torch
from typing import Tuple

import torch
import sympy as sp
from typing import Tuple


def inverse_kinematics_analytic_fixed_links_with_offsets(
    x: torch.Tensor,
    z: torch.Tensor,
    alpha: torch.Tensor,
    rel_positions: torch.Tensor, # Input: [J1->J2, J2->EE, EE->Tip]
    joint_offsets: torch.Tensor # Input: [OffsetJ1, OffsetJ2, OffsetJ3]
) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
    """
    Compute the inverse kinematics analytically, accounting for offsets.
    Based on user feedback, assumes rel_positions[0] is J1->J2, [1] is J2->EE, [2] is EE->Tip.
    Calculates controllable angles q1, q2, q3 for joints J1, J2, J3.
    """
    x = x.view(-1, 1)
    z = z.view(-1, 1)
    alpha = alpha.view(-1, 1)
    batch_size = x.shape[0]
    num_joints = joint_offsets.shape[1]

    if rel_positions.shape[1] < 3:
         raise ValueError(f"rel_positions needs at least 3 entries for [J1->J2, J2->EE, EE->Tip]. Got shape {rel_positions.shape}")
    if num_joints != 3:
         raise ValueError(f"This specific analytic IK implementation requires exactly 3 joints, got {num_joints}")

    offset1 = joint_offsets[:, 0, 1:2] # Offset for joint 1
    offset2 = joint_offsets[:, 1, 1:2] # Offset for joint 2
    offset3 = joint_offsets[:, 2, 1:2] # Offset for joint 3

    l0 = torch.norm(rel_positions[:, 0, [0, 2]], dim=1, keepdim=True) # Length(J1->J2)
    l1 = torch.norm(rel_positions[:, 1, [0, 2]], dim=1, keepdim=True) # Length(J2->EE)
    l2 = torch.norm(rel_positions[:, 2, [0, 2]], dim=1, keepdim=True) # Length(EE->Tip)

    l0 = torch.where(l0 == 0, torch.full_like(l0, 1e-9), l0)
    l1 = torch.where(l1 == 0, torch.full_like(l1, 1e-9), l1)

    theta_2 = torch.atan2(rel_positions[:, 1, 2:3], rel_positions[:, 1, 0:1])
    theta_end = torch.atan2(rel_positions[:, 2, 2:3], rel_positions[:, 2, 0:1])

    alpha_end = alpha - theta_end
    p2x = x - l2 * torch.cos(alpha_end)
    p2z = z + l2 * torch.sin(alpha_end) # Original sign convention

    cos_q2_arg = (p2x**2 + p2z**2 - l0**2 - l1**2) / (2 * l0 * l1 + 1e-9)
    cos_q2_arg = torch.clamp(cos_q2_arg, -1.0, 1.0)
    q2_total_geom = torch.acos(cos_q2_arg) # Geometric angle related to J2

    q1_total = -torch.atan2(p2z, p2x) - torch.atan2(l1 * torch.sin(q2_total_geom), l0 + l1 * torch.cos(q2_total_geom))
    q2_total = q2_total_geom + theta_2
    q3_total = alpha - q1_total - q2_total

    q1_ctrl = q1_total - offset1
    q2_ctrl = q2_total - offset2
    q3_ctrl = q3_total - offset3

    q1_ctrl = torch.remainder(q1_ctrl + math.pi, 2 * math.pi) - math.pi
    q2_ctrl = torch.remainder(q2_ctrl + math.pi, 2 * math.pi) - math.pi
    q3_ctrl = torch.remainder(q3_ctrl + math.pi, 2 * math.pi) - math.pi

    return q1_ctrl, q2_ctrl, q3_ctrl


def forward_kinematics_2D_batch_with_offsets(
    rel_positions: torch.Tensor,    # Input: [J1->J2, J2->EE, EE->Tip]
    joint_angles_ctrl: torch.Tensor,# Input: [q1_ctrl, q2_ctrl, q3_ctrl]
    joint_offsets: torch.Tensor     # Input: [OffsetJ1, OffsetJ2, OffsetJ3]
) -> Tuple[torch.Tensor, torch.Tensor]:
    """
    Perform forward kinematics, accounting for offsets.
    Assumes rel_positions[i] is rotated by (joint_angles_ctrl[i] + offset[i]).
    """
    batch_size = rel_positions.shape[0]
    num_segments = rel_positions.shape[1]
    num_angles = joint_angles_ctrl.shape[1]

    if num_segments != num_angles:
        raise ValueError(f"Mismatch between rel_positions segments ({num_segments}) and joint angles ({num_angles})")
    if joint_offsets.shape[1] != num_angles:
        raise ValueError(f"Mismatch between joint_offsets ({joint_offsets.shape[1]}) and joint angles ({num_angles})")

    num_joints = num_angles
    joint_positions = torch.zeros(batch_size, num_joints + 1, 2, device=rel_positions.device, dtype=rel_positions.dtype)
    alpha_cumulative = torch.zeros((batch_size, 1), device=rel_positions.device, dtype=rel_positions.dtype)
    x = torch.zeros((batch_size, 1), device=rel_positions.device, dtype=rel_positions.dtype)
    z = torch.zeros((batch_size, 1), device=rel_positions.device, dtype=rel_positions.dtype)

    for i in range(num_joints):
        q_ctrl = joint_angles_ctrl[:, i:i+1]
        offset = joint_offsets[:, i, 1:2]
        q_total = q_ctrl + offset
        alpha_cumulative += q_total

        dx = rel_positions[:, i, 0:1]
        dz = rel_positions[:, i, 2:3]
        # Original rotation convention
        new_dx = dx * torch.cos(alpha_cumulative) + dz * torch.sin(alpha_cumulative)
        new_dz = -dx * torch.sin(alpha_cumulative) + dz * torch.cos(alpha_cumulative)
        x += new_dx
        z += new_dz
        joint_positions[:, i+1, 0] = x.squeeze(1)
        joint_positions[:, i+1, 1] = z.squeeze(1)

    alpha_cumulative = torch.remainder(alpha_cumulative + math.pi, 2 * math.pi) - math.pi
    end_effector = torch.cat([x, z, alpha_cumulative], dim=1) # This is the TIP pose

    return joint_positions, end_effector


# --- Jacobian Function WITH OFFSET HANDLING ---

def compute_jacobian_torch_with_offsets(
    rel_positions: torch.Tensor, # Input: [J1->J2, J2->EE, EE->Tip]
    q_ctrl: torch.Tensor,        # Input: [q1_ctrl, q2_ctrl, q3_ctrl]
    joint_offsets: torch.Tensor  # Input: [OffsetJ1, OffsetJ2, OffsetJ3]
) -> torch.Tensor:
    """
    Compute numerical Jacobian d(EndEffectorPose)/d(q_ctrl), accounting for offsets.
    Assumes rel_positions[i] is rotated by (q_ctrl[i] + offset[i]).
    """
    batch_size = rel_positions.shape[0]
    num_joints = q_ctrl.shape[1] # Should be 3

    if rel_positions.shape[1] < num_joints:
         raise ValueError(f"rel_positions needs at least {num_joints} entries. Got shape {rel_positions.shape}")
    if joint_offsets.shape[1] < num_joints:
         raise ValueError(f"joint_offsets needs at least {num_joints} entries.")
    if num_joints != 3:
         raise ValueError(f"This Jacobian implementation requires exactly 3 joints, got {num_joints}")

    dx = rel_positions[:, :num_joints, 0] # Shape (N, 3)
    dz = rel_positions[:, :num_joints, 2] # Shape (N, 3)
    offsets = joint_offsets[:, :num_joints, 1] # Shape (N, 3)
    q_total = q_ctrl + offsets # Shape (N, 3)
    alpha_cumulative = torch.cumsum(q_total, dim=1) # Shape (N, 3)

    cos_alpha = torch.cos(alpha_cumulative) # Shape (N, 3)
    sin_alpha = torch.sin(alpha_cumulative) # Shape (N, 3)

    cos_a0 = cos_alpha[:, 0]
    sin_a0 = sin_alpha[:, 0]
    cos_a1 = cos_alpha[:, 1]
    sin_a1 = sin_alpha[:, 1]
    cos_a2 = cos_alpha[:, 2]
    sin_a2 = sin_alpha[:, 2]

    dx0, dx1, dx2 = dx[:, 0], dx[:, 1], dx[:, 2]
    dz0, dz1, dz2 = dz[:, 0], dz[:, 1], dz[:, 2]

    jacobian = torch.zeros((batch_size, 3, num_joints), dtype=dx.dtype, device=dx.device)

    jacobian[:, 0, 0] = (-dx0 * sin_a0 + dz0 * cos_a0) + (-dx1 * sin_a1 + dz1 * cos_a1) + (-dx2 * sin_a2 + dz2 * cos_a2)
    jacobian[:, 1, 0] = (-dx0 * cos_a0 - dz0 * sin_a0) + (-dx1 * cos_a1 - dz1 * sin_a1) + (-dx2 * cos_a2 - dz2 * sin_a2)
    jacobian[:, 2, 0] = 1.0

    jacobian[:, 0, 1] = (-dx1 * sin_a1 + dz1 * cos_a1) + (-dx2 * sin_a2 + dz2 * cos_a2)
    jacobian[:, 1, 1] = (-dx1 * cos_a1 - dz1 * sin_a1) + (-dx2 * cos_a2 - dz2 * sin_a2)
    jacobian[:, 2, 1] = 1.0

    jacobian[:, 0, 2] = (-dx2 * sin_a2 + dz2 * cos_a2)
    jacobian[:, 1, 2] = (-dx2 * cos_a2 - dz2 * sin_a2)
    jacobian[:, 2, 2] = 1.0

    return jacobian


def compute_inverse_jacobian_torch_with_offsets(
    rel_positions: torch.Tensor, # Input: [J1->J2, J2->EE, EE->Tip]
    q_ctrl: torch.Tensor,        # Input: [q1_ctrl, q2_ctrl, q3_ctrl]
    joint_offsets: torch.Tensor  # Input: [OffsetJ1, OffsetJ2, OffsetJ3]
) -> torch.Tensor:
    """
    Compute the inverse Jacobian d(q_ctrl)/d(EndEffectorPose), accounting for offsets.
    """
    jacobian = compute_jacobian_torch_with_offsets(rel_positions, q_ctrl, joint_offsets)
    return torch.inverse(jacobian)


def sample_within_limits(min_val: torch.Tensor, max_val: torch.Tensor, num_samples: int, device: str = "cuda") -> torch.Tensor:
    """
    Sample random values within specified limits.

    Parameters:
        min_val: Minimum value tensor
        max_val: Maximum value tensor
        num_samples: Number of samples to generate
        device: Device to generate samples on ("cpu", "cuda")

    Returns:
        A tensor containing random samples within the specified limits.
    """
    # print("sampling between ", min_val, " and ", max_val)
    if min_val.dim() == 0 and max_val.dim() == 0:
        return torch.rand(num_samples, device=device) * (max_val - min_val) + min_val
    
    elif min_val.dim() == 1 and max_val.dim() == 1:
        if min_val.shape[0] != num_samples or max_val.shape[0] != num_samples:
            raise ValueError("Batch size mismatch")
        return (torch.rand(num_samples, 1, device=device) * (max_val.unsqueeze(1) - min_val.unsqueeze(1)) + min_val.unsqueeze(1))
    
    elif min_val.dim() == 2 and max_val.dim() == 2:
        if min_val.shape[0] != num_samples or max_val.shape[0] != num_samples:
            raise ValueError("Batch size mismatch")
        return torch.rand(num_samples, 1, device=device) * (max_val - min_val) + min_val
    
    else:
        raise ValueError("Unsupported dimensions for min_val and max_val")


def sample_within_limits_skewed(min_val: torch.Tensor, max_val: torch.Tensor, num_samples: int, alpha: float, beta: float, device: str = "cuda") -> torch.Tensor:
    """
    Sample random values within specified limits using a skewed distribution.
    
    Parameters:
        min_val: Minimum value tensor
        max_val: Maximum value tensor
        num_samples: Number of samples to generate
        alpha: Alpha parameter for the Beta distribution
        beta: Beta parameter for the Beta distribution
        device: Device to generate samples on ("cpu", "cuda")

    Returns:
        A tensor containing random samples within the specified limits.
    """
    # Generate samples from Beta distribution
    beta_samples = torch.distributions.beta.Beta(alpha, beta).rsample([num_samples]).to(device)
    
    if min_val.dim() == 0 and max_val.dim() == 0:
        return beta_samples * (max_val - min_val) + min_val
    
    elif min_val.dim() == 1 and max_val.dim() == 1:
        if min_val.shape[0] != num_samples or max_val.shape[0] != num_samples:
            raise ValueError("Batch size mismatch")
        return (beta_samples.view(-1, 1) * (max_val.unsqueeze(1) - min_val.unsqueeze(1)) + min_val.unsqueeze(1))
    
    elif min_val.dim() == 2 and max_val.dim() == 2:
        if min_val.shape[0] != num_samples or max_val.shape[0] != num_samples:
            raise ValueError("Batch size mismatch")
        return beta_samples.view(-1, 1) * (max_val - min_val) + min_val
    
    else:
        raise ValueError("Unsupported dimensions for min_val and max_val")


# Correcting the function to generate the output with shape (batch_size, num_columns)
def sample_params_within_limits_skewed(min_val: torch.Tensor, max_val: torch.Tensor, num_samples: int, alpha: float, beta: float, device: str = "cuda") -> torch.Tensor:
    """
    Sample random values within specified limits using a skewed distribution.
    """
    # Validate the shapes of min_val and max_val
    if min_val.shape != max_val.shape:
        raise ValueError("Shape mismatch between min_val and max_val")
    
    if min_val.shape[0] not in [1, num_samples]:
        raise ValueError("The 0-dimension of min_val and max_val should either be 1 or num_samples")
    
    # Generate samples from Beta distribution
    beta_samples = torch.distributions.beta.Beta(alpha, beta).rsample([num_samples, *min_val.shape[1:]]).to(device)
    
    # Compute the skewed samples
    return beta_samples * (max_val - min_val) + min_val


import os
import xml.etree.ElementTree as ET


def scale_link_dimensions(root, link_name, scale_factor):
    """Scale the dimensions of the specified link."""
    for link in root.findall('link'):
        if link.get('name') == link_name:
            for visual in link.findall('visual'):
                geometry = visual.find('geometry')
                if geometry is not None:
                    for dimension in geometry.iter():
                        if 'size' in dimension.tag:
                            size_values = [float(v) * scale_factor for v in dimension.text.split()]
                            dimension.text = ' '.join(map(str, size_values))


def scale_inertia(root, scale_factor):
    """Scale the inertia of all links."""
    for link in root.findall('link'):
        inertia = link.find('inertia')
        if inertia is not None:
            for param in inertia:
                param_value = float(param.text) * scale_factor
                param.text = str(param_value)


def adjust_urdf(file_path, scale_factors):
    """Adjust the URDF file based on specified scale factors.
    
    Usage:
    # URDF file path
    urdf_file = '/mnt/data/m545_fixed_tele_narrow.urdf'

    # Scale factors for specified links
    scale_factors = {
        'BOOM': 1.2,
        'TELE': 1.2,
        'DIPPER': 1.2,
        'ROTO_BASE': 1.1,
        'ROTO': 1.1,
        'SHOVEL': 1.3,
        'ENDEFFECTOR': 1.3
    }

    # Modify and save the URDF file
    modified_urdf_path = adjust_urdf(urdf_file, scale_factors)
    """
    tree = ET.parse(file_path)
    root = tree.getroot()

    # Scale specified links
    for link, scale_factor in scale_factors.items():
        scale_link_dimensions(root, link, scale_factor)

    # Scale inertia proportionally to the highest scale factor
    max_scale = max(scale_factors.values())
    scale_inertia(root, max_scale)

    # Save the modified URDF
    new_file_path = os.path.splitext(file_path)[0] + '_modified.urdf'
    tree.write(new_file_path)
    return new_file_path


def save_model_coefficients(model_coefficients, filename):
    """
    Save the model coefficients to a JSON file.
    
    Parameters:
    - model_coefficients: A dictionary where keys are robot identifiers and
      values are numpy arrays of coefficients.
    - filename: Path to the JSON file where coefficients will be saved.
    """
    # Convert numpy arrays to lists for JSON serialization
    model_coefficients_serializable = {k: v.tolist() for k, v in model_coefficients.items()}
    
    with open(filename, 'w') as f:
        json.dump(model_coefficients_serializable, f)

# Example usage:
# coefficients = {'robot1': np.array([...]), 'robot2': np.array([...])}
# save_model_coefficients(coefficients, 'model_coefficients.json')
import json


def load_model_coefficients(filename):
    """
    Load model coefficients from a JSON file.
    
    Parameters:
    - filename: Path to the JSON file from which coefficients will be loaded.
    
    Returns:
    A dictionary with the same structure as the one saved, but numpy arrays are
    numpy arrays again.
    """
    with open(filename, 'r') as f:
        model_coefficients_serializable = json.load(f)
    
    return {k: np.array(v) for k, v in model_coefficients_serializable.items()}

# Example usage:
# coefficients = load_model_coefficients('model_coefficients.json')

# Re-import torch and define the necessary functions again after code execution state reset
import torch
from itertools import combinations_with_replacement


def polynomial_features_torch(X, degree):
    n_samples, n_features = X.shape
    combinations_list = [combinations_with_replacement(range(n_features), i) for i in range(0, degree + 1)]
    index_combinations = [item for sublist in combinations_list for item in sublist]
    
    n_output_features = len(index_combinations)
    X_poly = torch.ones((n_samples, n_output_features), device=X.device)
    
    for i, indices in enumerate(index_combinations):
        if len(indices) > 0:
            X_poly[:, i] = torch.prod(X[:, list(indices)], dim=1)
    
    return X_poly


def compute_poly(coefficients, X, degree=1):
    X_poly = polynomial_features_torch(X, degree)
    poses = torch.sum(X_poly * coefficients, dim=1)
    return poses


def polynomial_indipendent_features(X, degree):
    N, D = X.shape
    result = torch.empty((N, degree + 1, D), dtype=X.dtype, device=X.device)
    for i in range(degree + 1):
        result[:, i, :] = X ** i
    return result


def compute_indipendent_poly(coefficient, X, degree=1):
    X_poly = polynomial_indipendent_features(X, degree)
    # Use Einstein notation to contract along the second dimension
    poses = torch.einsum('ijk,ijk->ik', coefficient, X_poly)
    return poses


if __name__ == '__main__':
    J = compute_jacobian()
    print(J)
    J_inv = J.inv()
    print(J_inv)
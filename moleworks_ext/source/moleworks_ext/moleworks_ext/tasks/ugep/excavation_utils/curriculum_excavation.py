# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
As the curriculum is handled differently then in Orbit, a custom manager for the excavation environment is implemented.
The main reason is that the curriculum has to update its own buffers over iterations.
"""

from __future__ import annotations

import torch
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from moleworks_ext.tasks.ugep.excavation_env import ExcavationEnv


class Curriculum_Excavation:
    def __init__(self, env: ExcavationEnv):
        self.env = env
        self.device = self.env.device

        # params
        self.exp_f = torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.exp_f

        self.start_term_fill_ratio = (
            torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.start_term_fill_ratio
        )
        self.end_term_fill_ratio = torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.end_term_fill_ratio

        self.start_height_above_soil = (
            torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.start_height_above_soil
        )
        self.end_height_above_soil = (
            torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.end_height_above_soil
        )

        self.start_curl_fill_ratio = (
            torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.start_curl_fill_ratio
        )
        self.end_curl_fill_ratio = torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.end_curl_fill_ratio

        self.start_pullup_band = torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.start_pullup_band
        self.end_pullup_band = torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.end_pullup_band

        self.start_spilling_depth_margin = (
            torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.start_spilling_depth_margin
        )
        self.end_spilling_depth_margin = (
            torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.end_spilling_depth_margin
        )

        self.curr_term_fill_ratio = self.start_term_fill_ratio.clone()
        self.curr_term_height_above_soil = self.start_height_above_soil.clone()
        self.curr_curl_ratio = self.start_curl_fill_ratio.clone()
        self.curr_pullup_band = self.start_pullup_band.clone()
        self.curr_spilling_depth_margin = self.start_spilling_depth_margin.clone()

        self.algo_iter = 0
        self.level = torch.zeros(1, device=self.device)
        self.full_term_mean = torch.zeros(1, device=self.device)
        self.close_term_mean = torch.zeros(1, device=self.device)
        self.timeout_term_mean = torch.zeros(1, device=self.device)
        self.theta = torch.zeros(1, device=self.device)
        self.completed = False

    def update_curriculum_excavation(self):
        # performance_better = (-self.last_full_term_mean + self.full_term_mean) > 0.0

        # self.level += performance_better * (self.full_term_mean > 0.5)
        # this is criterion for levelling up

        # this works nicely 
        # if (self.env.term_manager.full_term_mean + self.env.term_manager.close_term_mean) > 0.1 and self.env.term_manager.full_term_mean > 0.001 or self.env.term_manager.full_term_mean > 0.02:
        #     self.level += 2.0

        if (self.env.termination_excavation.full_term_mean > 0.0001 and self.env.termination_excavation.full_term_mean_d > 0):
            self.level += torch.tensor(5.0, device=self.device, dtype=torch.float32)
        # self.level = torch.tensor(750, device=self.device)
        # linear 0-500
        # self.level = torch.tensor([100], device=self.device)
        self.level1 = torch.minimum(self.level, torch.tensor(100.0, device=self.device))
        if self.level1 == 100:
            self.completed = True
        # self.level2 = torch.minimum(self.level, torch.tensor(500.0, device=self.device))
        # self.level3 = torch.minimum(self.level, torch.tensor(750.0, device=self.device))
        # used to control the covariance of the terrain distribution
        self.cf = self.level1 / 100.0
        # self.cf2 = self.level2 / 500.0
        # self.cf3 = self.level3 / 750.0

        # exponential
        # self.cf = 1.0 - torch.exp(-self.exp_f * self.level)
        self.curr_curl_ratio[:] = self.start_curl_fill_ratio + self.cf * (
                self.end_curl_fill_ratio - self.start_curl_fill_ratio
        )

        self.curr_term_fill_ratio[:] = torch.clip(self.start_term_fill_ratio + self.cf * (
                self.end_term_fill_ratio - self.start_term_fill_ratio
        ) + (1 - self.cf) * torch.rand(1, device=self.device) / 20.0, max=self.end_term_fill_ratio, min=self.curr_curl_ratio)

        # self.cf = 1

        self.curr_term_height_above_soil[:] = self.start_height_above_soil + self.cf * (
                self.end_height_above_soil - self.start_height_above_soil
        )

        self.curr_pullup_band[:] = self.start_pullup_band + self.cf * (self.end_pullup_band - self.start_pullup_band)

        self.curr_spilling_depth_margin[:] = self.start_spilling_depth_margin + self.cf * (
                self.end_spilling_depth_margin - self.start_spilling_depth_margin
        )

    def update_curriculum_excavation_old(self):
        # performance_better = (-self.last_full_term_mean + self.full_term_mean) > 0.0

        # self.level += performance_better * (self.full_term_mean > 0.5)
        self.level += (
            self.env.termination_excavation.full_term_mean + self.env.termination_excavation.close_term_mean > 0.5
        )

        # linear 0-500
        self.level1 = torch.minimum(self.level, torch.tensor(250.0, device=self.device))
        self.level2 = torch.minimum(self.level, torch.tensor(500.0, device=self.device))
        self.level3 = torch.minimum(self.level, torch.tensor(750.0, device=self.device))

        self.cf = self.level1 / 250.0
        self.cf2 = self.level2 / 500.0
        self.cf3 = self.level3 / 750.0

        # exponential
        # self.cf = 1.0 - torch.exp(-self.exp_f * self.level)

        self.curr_term_fill_ratio[:] = self.start_term_fill_ratio + self.cf * (
                self.end_term_fill_ratio - self.start_term_fill_ratio
            )

        self.curr_curl_ratio[:] = self.start_curl_fill_ratio + self.cf * (
            self.end_curl_fill_ratio - self.start_curl_fill_ratio
        )

        self.curr_term_height_above_soil[:] = self.start_height_above_soil + self.cf * (
            self.end_height_above_soil - self.start_height_above_soil
        )
        self.curr_pullup_band[:] = self.start_pullup_band + self.cf * (self.end_pullup_band - self.start_pullup_band)

        self.curr_spilling_depth_margin[:] = self.start_spilling_depth_margin + self.cf * (
            self.end_spilling_depth_margin - self.start_spilling_depth_margin
        )
        # reduce go down and go up rewards with curriculum
        # go_down_idx = self.env.reward_manager.reward_names.index("bucket_edge_down")
        # self.env.reward_manager.reward_scales[go_down_idx] = (
        #     1.0 - self.cf
        # ) * self.env.cfg.rewards.scales.bucket_edge_down

        # curl_idx = self.env.reward_manager.reward_names.index("bucket_curl")
        # self.env.reward_manager.reward_scales[curl_idx] = (1.0 - self.cf) * self.env.cfg.rewards.scales.bucket_curl

        # go_up_idx = self.env.reward_manager.reward_names.index("pitch_up")
        # self.env.reward_manager.reward_scales[go_up_idx] = (1.0 - self.cf) * self.env.cfg.rewards.scales.pitch_up

        if self.env.cfg.curriculum_utils.rbf_theta_cf == 1:
            self.theta = self.cf / 2.0

            if self.cf < 1.0:
                self.env.soil.soil_height_rbf.compute_norm_transform(theta=self.theta)
                self.env.soil.max_depth_height_rbf.compute_norm_transform(theta=self.theta)

        if self.env.cfg.curriculum_utils.rbf_theta_cf == 2:
            self.theta = self.cf2 / 2.0

            if self.cf2 < 1.0:
                self.env.soil.soil_height_rbf.compute_norm_transform(theta=self.theta)
                self.env.soil.max_depth_height_rbf.compute_norm_transform(theta=self.theta)

        if self.env.cfg.curriculum_utils.rbf_theta_cf == 3:
            self.theta = self.cf3 / 2.0

            if self.cf3 < 1.0:
                self.env.soil.soil_height_rbf.compute_norm_transform(theta=self.theta)
                self.env.soil.max_depth_height_rbf.compute_norm_transform(theta=self.theta)

    def reset_excavation(self):
        extras = {}
        extras["Curriculum/level"] = self.level1.item()
        # extras["Curriculum/level2"] = self.level2.item()
        # extras["Curriculum/level3"] = self.level3.item()
        return extras

    def set_level_and_update(self, level):
        self.level = level
        self.update_curriculum_excavation()
        print("updating curriculum level!")

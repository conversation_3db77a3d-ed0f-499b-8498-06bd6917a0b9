# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
This script provides function to easily override cfg with fixed aguments defined here before playing, benchmarking
or training.
"""

import argparse


def get_play_args() -> argparse.Namespace:
    """Defines custom command-line arguments and parses them.

    Returns:
        argparse.Namespace: Parsed CLI arguments.
    """
    custom_parameters = [
        {
            "name": "--record_frames",
            "type": bool,
            "default": False,
            "help": "record frames or not",
        },
        {
            "name": "--max_ep_length",
            "type": float,
            "default": 20.0,
            "help": "maximum episode length",
        },
        {
            "name": "--slope_ang",
            "type": float,
            "default": 30,
            "help": "slope angle in deg",
        },
        {
            "name": "--rbf_theta",
            "type": float,
            "default": 0.5,
            "help": "rbf theta",
        },
        {
            "name": "--max_depth_offset_height",
            "type": float,
            "default": 0.0,
            "help": "max depth offset",
        },
        {
            "name": "--pullup_range_min",
            "type": float,
            "default": 2.0,
            "help": "pullup dist",
        },
        {
            "name": "--soil_type",
            "type": str,
            "default": "random",
            "help": "Soil Type",
        },
        {
            "name": "--config",
            "default": False,
            "type": str,
            "help": "Excavator Configuration [False, close, medium, far]",
        },
        {
            "name": "--disable_neg_terms",
            "action": "store_true",
            "default": False,
            "help": "Excavator Configuration [False, close, medium, far]",
        },
        {
            "name": "--time",
            "type": int,
            "default": 20,
            "help": "Test Time [s]",
        },
        {
            "name": "--task",
            "type": str,
            "default": "Isaac-m545-v0",
            "help": "Resume training or start testing from a checkpoint. Overrides config file if provided.",
        },
        {
            "name": "--run_name",
            "type": str,
            "default": "training name",
            "help": "Name of the experiment to run or load. Overrides config file if provided.",
        },
        {
            "name": "--experiment_name",
            "type": str,
            "default": "random_soil",
            "help": "Name of the experiment to run or load. Overrides config file if provided.",
        },
        {
            "name": "--checkpoint",
            "type": int,
            "default": -1,
            "help": (
                "Saved model checkpoint number. If -1: will load the last checkpoint. Overrides config file if"
                " provided."
            ),
        },
        {
            "name": "--headless",
            "action": "store_true",
            "default": False,
            "help": "Force display off at all times",
        },
        {
            "name": "--seed",
            "type": int,
            "default": 2,
            "help": "Random seed. Overrides config file if provided.",
        },
        {
            "name": "--log_env_nr",
            "type": int,
            "default": None,
            "help": "Env number to log, if None, logger is not used",
        },
        {
            "name": "--infinite_torque",
            "type": bool,
            "default": False,
            "help": "Do not enforce torque limits.",
        },
        {
            "name": "--send_timeouts",
            "type": bool,
            "default": True,
            "help": "Do not enforce torque limits.",
        },
        {
            "name": "--reset_only_above_soil",
            "type": bool,
            "default": False,
            "help": "Do not enforce torque limits.",
        },
        {
            "name": "--reset_sample_soil",
            "type": bool,
            "default": True,
            "help": "Do not enforce torque limits.",
        },
    ]

    parser = argparse.ArgumentParser(description="Play args")
    for argument in custom_parameters:
        if ("name" in argument) and ("type" in argument or "action" in argument):
            help_str = ""
        if "help" in argument:
            help_str = argument["help"]

        if "type" in argument:
            if "default" in argument:
                parser.add_argument(argument["name"], type=argument["type"], default=argument["default"], help=help_str)
            else:
                parser.add_argument(argument["name"], type=argument["type"], help=help_str)
        elif "action" in argument:
            parser.add_argument(argument["name"], action=argument["action"], help=help_str)

    parser.add_argument("--soil_height_type", nargs=2)
    parser.add_argument("--max_depth_type", nargs=2)
    args = parser.parse_args()

    return args


def override_env_cfg_with_args(env_cfg, args):
    # not the playa args!
    # env_cfg.seed = args.seed

    if hasattr(args, "max_ep_length"):
        env_cfg.episode_length_s = args.max_ep_length
    else:
        print("[WARNING] 'max_ep_length' not found in args. Skipping episode_length_s override.")
    env_cfg.max_depth_height.clip_margin = args.max_depth_height.clip_margin

    # env_cfg.rewards_excavation.scales.action_rate = args.rewards_excavation.scales.action_rate
    # env_cfg.rewards_excavation.scales.bucket_edge_down = args.rewards_excavation.scales.bucket_edge_down
    # env_cfg.rewards_excavation.scales.bucket_filling = args.rewards_excavation.scales.bucket_filling
    # env_cfg.rewards_excavation.scales.power = args.rewards_excavation.scales.power
    # env_cfg.rewards_excavation.scales.pitch_up = args.rewards_excavation.scales.pitch_up
    # env_cfg.rewards_excavation.scales.bucket_curl = args.rewards_excavation.scales.bucket_curl
    # env_cfg.rewards_excavation.scales.max_depth_tracking = args.rewards_excavation.scales.max_depth_tracking
    env_cfg.rewards_excavation.max_depth_tracking_offset = args.rewards_excavation.max_depth_tracking_offset

    env_cfg.terminations_excavation.positive_terminations.desired_close = (
        args.terminations_excavation.positive_terminations.desired_close
    )
    env_cfg.terminations_excavation.positive_terminations.desired_full = (
        args.terminations_excavation.positive_terminations.desired_full
    )
    env_cfg.terminations_excavation.neg_term_rew = args.terminations_excavation.neg_term_rew
    env_cfg.terminations_excavation.max_depth_overshoot = args.terminations_excavation.max_depth_overshoot
    env_cfg.terminations_excavation.max_bucket_vel = args.terminations_excavation.max_bucket_vel

    env_cfg.curriculum_utils.start_term_fill_ratio = args.curriculum_utils.start_term_fill_ratio
    env_cfg.curriculum_utils.start_curl_fill_ratio = args.curriculum_utils.start_curl_fill_ratio
    env_cfg.curriculum_utils.end_height_above_soil = args.curriculum_utils.end_height_above_soil

    env_cfg.reset.max_soil_force_and_moment = args.reset.max_soil_force_and_moment
    env_cfg.soil_parameters.type = args.soil_type  #
    env_cfg.reset.pullup_dist_range = args.reset.pullup_dist_range
    env_cfg.curriculum_utils.start_pullup_band = args.curriculum_utils.start_pullup_band
    env_cfg.curriculum_utils.start_spilling_depth_margin = args.curriculum_utils.start_spilling_depth_margin
    env_cfg.curriculum_utils.end_spilling_depth_margin = args.curriculum_utils.end_spilling_depth_margin
    env_cfg.soil_height.z_max = args.soil_height.z_max
    env_cfg.soil_height.z_min = args.soil_height.z_min
    env_cfg.soil_height.slope_ang = args.slope_ang  #
    env_cfg.max_depth_height.slope_ang = args.slope_ang  #
    env_cfg.curriculum_utils.rbf_theta_cf = args.curriculum_utils.rbf_theta_cf

    env_cfg.observations_excavation.num_soil_height_futures = args.observations_excavation.num_soil_height_futures
    env_cfg.observations_excavation.soil_height_futures_spacing = (
        args.observations_excavation.soil_height_futures_spacing
    )
    env_cfg.observations_excavation.num_max_depth_futures = args.observations_excavation.num_max_depth_futures
    env_cfg.observations_excavation.max_depth_futures_spacing = args.observations_excavation.max_depth_futures_spacing
    env_cfg.observations_excavation.enable.soil_parameters = args.observations_excavation.enable.soil_parameters

    env_cfg.decimation = args.decimation

    return env_cfg

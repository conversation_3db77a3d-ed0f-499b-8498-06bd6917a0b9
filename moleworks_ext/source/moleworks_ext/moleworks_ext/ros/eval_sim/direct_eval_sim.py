# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import signal
from collections import deque
from time import perf_counter, sleep
from typing import Optional

import rclpy
import torch
from isaaclab.envs import ManagerBasedEnv
import carb

from moleworks_ext.ros.eval_sim.eval_sim_cfg import BaseEvalSimCfg
from moleworks_ext.ros.ros_manager import RosManager
from moleworks_ext.ros.utils import log_error, log_info


class DirectEvalSim:
    """A simplified version of EvalSim that takes pre-initialized environment and ROS manager."""

    def __init__(
        self, cfg: BaseEvalSimCfg, env: ManagerBasedEnv, ros_manager: RosManager
    ) -> None:
        """Initialize DirectEvalSim with configuration, environment, and ROS manager.

        Args:
            cfg: Configuration for the evaluation simulation
            env: Pre-initialized Isaac Lab environment
            ros_manager: Pre-initialized ROS manager for communication
        """
        self.cfg = cfg
        self.env = env
        self.ros_manager = ros_manager

        # Initialize runtime variables
        self.step_counter = 0
        self.obs = {}
        self.wallclock_dt_buffer = deque(maxlen=200)
        self.t_prev = perf_counter()

        # Register signal handler for graceful shutdown
        self.exit_requested = False
        signal.signal(signal.SIGINT, self.sigint_handler)
        log_info("DirectEvalSim initialized")

    def sigint_handler(self, signum, frame):
        """Handle SIGINT (Ctrl+C) for graceful shutdown."""
        log_info("Ctrl+C received, running DirectEvalSim cleanup code")
        rclpy.shutdown()
        self.exit_requested = True

    def step(self):
        """Perform a simulation step."""
        with torch.inference_mode():
            step_start_time = perf_counter()

            # Publish observations from previous step
            self.ros_manager.publish(self.obs)

            # Get latest action commands from ROS
            action = self.ros_manager.get_latest_messages()

            # Handle rendering if configured
            if (
                self.cfg.render_substeps
                and self.step_counter % self.cfg.render_substeps == 0
            ):
                pass  # Rendering logic would go here

            # Execute environment step
            self.obs, extras = self.env.step(action=action)

            step_end_time = perf_counter()
            carb.log_info(
                f"Completed step {self.step_counter} in {step_end_time - step_start_time:.4f}s, "
                f"sim_time={self.step_counter * self.physics_dt * self.cfg.control_substeps:.4f}"
            )

        self._handle_timing()

    def _handle_timing(self):
        """Handle timing and real-time synchronization."""
        self.log_wallclock_time()
        sim_wallclock_dt = self.wallclock_dt_buffer[-1]
        target_step_time = self.physics_dt

        if self.cfg.sync_to_real_time and sim_wallclock_dt < target_step_time:
            sleep_time = target_step_time - sim_wallclock_dt
            carb.log_info(
                f"Step completed faster than real-time, sleeping for {sleep_time:.4f}s"
            )
            sleep(sleep_time)

        self.step_counter += 1

    def reset(self):
        """Reset the environment and controller."""
        if self.env is not None:
            self.env.reset()

        if self.ros_manager is not None:
            self.ros_manager.reset_controller()

    def close(self):
        """Close active DirectEvalSim components."""
        if self.env is not None:
            try:
                self.env.close()
                self.env = None
            except Exception as e:
                log_error(f"Environment couldn't be closed. Exception: {e}")

        if self.ros_manager is not None:
            try:
                self.ros_manager.close()
                self.ros_manager = None
            except Exception as e:
                log_error(f"RosManager couldn't be shutdown. Exception: {e}")

    def log_wallclock_time(self):
        """Log wallclock time for profiling."""
        t_curr = perf_counter()
        dt = t_curr - self.t_prev
        self.wallclock_dt_buffer.append(dt)
        self.dt_avg_ms = (
            1000 * sum(self.wallclock_dt_buffer) / len(self.wallclock_dt_buffer)
        )
        self.t_prev = t_curr

    def reset_timeline_variables(self):
        """Reset timeline variables."""
        if self.env is not None:
            self.env.sim._timeline_timer_callback_fn(None)
        self.wallclock_dt_buffer.clear()

    @property
    def physics_dt(self):
        """Get the environment's physics timestep."""
        if self.env is None:
            raise ValueError("Environment must exist for accessing physics_dt.")
        return self.env.physics_dt


class ExcavationEvalSim(DirectEvalSim):
    """Specialized DirectEvalSim for excavation tasks with additional safety mechanisms."""

    def __init__(
        self, cfg: BaseEvalSimCfg, env: ManagerBasedEnv, ros_manager: RosManager
    ) -> None:
        super().__init__(cfg, env, ros_manager)

    def step(self):
        """Perform a simulation step with excavation-specific logic."""
        with torch.inference_mode():
            step_start_time = perf_counter()

            # Publish observations if available
            if self.obs:
                self.ros_manager.publish(self.obs)

            # Get latest action commands with timestamp
            actions_tensor, oldest_msg_timestamp_sec = (
                self.ros_manager.get_latest_messages()
            )

            # Apply safety mechanism for stale commands
            actions_tensor = self._apply_staleness_check(
                actions_tensor, oldest_msg_timestamp_sec
            )

            # Extract arm action with safety checks
            arm_action = self._extract_arm_action(actions_tensor)

            # Handle rendering if configured
            if (
                self.cfg.render_substeps
                and self.step_counter % self.cfg.render_substeps == 0
            ):
                pass  # Rendering logic would go here

            # Execute environment step
            self.obs, rew, reset_terminated, reset_timeout, extras = self.env.step(
                action=arm_action
            )

            # Handle environment resets
            self._handle_reset_conditions(reset_timeout)

            step_end_time = perf_counter()
            carb.log_info(
                f"Completed step {self.step_counter} in {step_end_time - step_start_time:.4f}s, "
                f"sim_time={self.step_counter * self.physics_dt * self.cfg.control_substeps:.4f}"
            )

        self._handle_timing()

    def _apply_staleness_check(
        self, actions_tensor: torch.Tensor, oldest_msg_timestamp_sec: float
    ) -> torch.Tensor:
        """Apply safety mechanism to check for stale commands."""
        current_ros_time_sec = (
            self.ros_manager._node.get_clock().now().nanoseconds / 1e9
        )
        staleness_threshold_sec = 0.2

        if (
            oldest_msg_timestamp_sec <= 0
            or (current_ros_time_sec - oldest_msg_timestamp_sec)
            > staleness_threshold_sec
        ):
            log_info(
                f"Stale command detected or no valid timestamp "
                f"(age: {current_ros_time_sec - oldest_msg_timestamp_sec:.4f}s > {staleness_threshold_sec}s). "
                f"Using zero action."
            )
            return torch.zeros_like(actions_tensor)

        return actions_tensor

    def _extract_arm_action(self, actions_tensor: torch.Tensor) -> torch.Tensor:
        """Extract arm action from actions tensor with error handling."""
        if actions_tensor.numel() == 0:
            log_error(
                "Received empty actions_tensor. Cannot extract arm_action. Check subscriber setup."
            )
            # Return a safe zero action - this should be handled upstream, but provides fallback
            return actions_tensor

        return actions_tensor[:, self.env.m545_measurements.arm_joint_ids]

    def _handle_reset_conditions(self, reset_timeout):
        """Handle environment reset conditions."""
        should_reset = False

        if isinstance(reset_timeout, torch.Tensor):
            should_reset = reset_timeout.item()
        else:
            should_reset = reset_timeout

        if should_reset:
            self.reset()

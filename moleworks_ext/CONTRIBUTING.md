# Contributing to Moleworks Extension

Thank you for your interest in contributing to the Moleworks Extension for IsaacLab! This guide will help you get started with development and ensure your contributions meet our quality standards.

## 🚀 Quick Start

### Development Environment Setup

#### Docker Container Development (Recommended)
Most development happens inside Docker containers where Isaac Lab is pre-installed.

1. **Install development tools:**
   ```bash
   # Inside your Docker container
   /workspace/isaaclab/_isaac_sim/python.sh -m pip install ruff black mypy bandit
   ```

2. **Install VS Code Extensions:**
   - Open Extensions panel: `Ctrl+Shift+X`
   - Search and install: **"Ruff"** by Astral Software
   - Reload VS Code: `Ctrl+Shift+P` → "Developer: Reload Window"

3. **Verify setup:**
   - Open any Python file
   - Save it (`Ctrl+S`) - should auto-format
   - Or manually format: `Shift+Alt+F`

#### Native Development
If developing outside containers:

```bash
make install-dev
make check
```

## 📝 Code Standards

### Python Style Guide
- **Line length**: 120 characters
- **Python version**: 3.10+
- **Import sorting**: Automatic with Ruff
- **Type hints**: Required for public APIs
- **Docstrings**: Google style for public functions and classes

### Code Quality Tools
We use modern Python tools for consistent, high-quality code:

- **Ruff**: Fast linting and formatting (replaces flake8, isort, pyupgrade)
- **Black**: Code formatting (via Ruff)
- **MyPy**: Static type checking
- **Bandit**: Security vulnerability scanning
- **Vulture**: Dead code detection

### Formatting Example
```python
"""Example module demonstrating code style."""

from typing import Optional

import numpy as np
import torch

from isaaclab.envs import ManagerBasedRLEnv
from moleworks_ext.common.utils import validate_config


class ExampleClass:
    """Example class following our coding standards.
    
    Args:
        config: Configuration dictionary with required parameters.
        device: PyTorch device for tensor operations.
        verbose: Whether to enable verbose logging.
    """
    
    def __init__(
        self, 
        config: dict, 
        device: torch.device = torch.device("cpu"),
        verbose: bool = False
    ) -> None:
        self.config = validate_config(config)
        self.device = device
        self.verbose = verbose
    
    def process_data(self, data: np.ndarray) -> Optional[torch.Tensor]:
        """Process input data and return processed tensor.
        
        Args:
            data: Input numpy array to process.
            
        Returns:
            Processed tensor or None if data is invalid.
        """
        if data.size == 0:
            return None
            
        tensor = torch.from_numpy(data).to(self.device)
        return tensor.float()
```

## 🔧 Development Workflow

### 1. Create Feature Branch
```bash
git checkout -b feature/your-feature-name
```

### 2. Development Commands
```bash
# Format code
make format

# Run linting
make lint

# Run all quality checks
make check

# Run tests
make test

# Run pre-commit hooks
make pre-commit
```

### 3. VS Code Integration
The project includes comprehensive VS Code configuration:

**Key Features:**
- Automatic formatting on save
- Real-time linting with Ruff
- Type checking with MyPy
- Integrated testing with pytest
- Custom tasks for development operations

**Available Tasks** (`Ctrl+Shift+P` → "Tasks: Run Task"):
- `lint`: Run Ruff linting
- `format`: Format code
- `type-check`: Run MyPy type checking
- `test`: Run pytest
- `pre-commit`: Run all pre-commit hooks
- `check-all`: Run comprehensive quality checks

### 4. Testing
```bash
# Run all tests
make test

# Run only fast tests
make test-fast

# Run integration tests
make test-integration

# Run with coverage
pytest tests/ --cov=source/moleworks_ext --cov-report=html
```

### 5. Pre-commit Hooks
All commits are automatically checked for:
- Code formatting (Ruff)
- Import sorting (Ruff)
- Type checking (MyPy)
- Security scanning (Bandit)
- Dead code detection (Vulture)
- Documentation formatting
- Spell checking
- Docker linting

## 🏗️ Project Structure

### Adding New Tasks
1. Create task directory: `source/moleworks_ext/moleworks_ext/tasks/your_task/`
2. Define environment: `your_task_env.py`
3. Configure agents: `agents/rsl_rl_cfg.py`
4. Set up MDP components: `mdp/observations.py`, `mdp/rewards.py`, `mdp/events.py`
5. Create environment config: `env_cfg/your_task_env_cfg.py`

### Common Components
- **Actions**: `source/moleworks_ext/moleworks_ext/common/actions/`
- **Sensors**: `source/moleworks_ext/moleworks_ext/common/sensors/`
- **Controllers**: `source/moleworks_ext/moleworks_ext/common/controllers/`
- **Utilities**: `source/moleworks_ext/moleworks_ext/common/utils/`

## 🔐 Security Guidelines

### Code Security
- **No hardcoded secrets**: Use environment variables or config files
- **Safe imports**: Avoid dynamic imports from user input
- **Input validation**: Validate all external inputs
- **Resource cleanup**: Always clean up resources in tests and long-running processes

### Example Security Best Practices
```python
import os
from pathlib import Path

# ✅ Good: Use environment variables for secrets
api_key = os.getenv("API_KEY")
if not api_key:
    raise ValueError("API_KEY environment variable is required")

# ✅ Good: Validate file paths
def load_config(config_path: str) -> dict:
    path = Path(config_path).resolve()
    if not path.exists() or not path.is_file():
        raise FileNotFoundError(f"Config file not found: {path}")
    # ... load config

# ❌ Bad: Hardcoded secrets
api_key = "sk-1234567890abcdef"  # Never do this!

# ❌ Bad: Unsafe file operations
with open(user_input_path) as f:  # Could access any file!
    data = f.read()
```

## 🧪 Testing Guidelines

### Test Structure
```
tests/
├── unit/           # Fast unit tests
├── integration/    # Integration tests
└── fixtures/       # Test data and fixtures
```

### Writing Tests
```python
import pytest
import torch

from moleworks_ext.tasks.excavation.excavation_env import ExcavationEnv


class TestExcavationEnv:
    """Test suite for excavation environment."""
    
    @pytest.fixture
    def env_config(self):
        """Provide test environment configuration."""
        return {
            "num_envs": 1,
            "robot_model": "m545",
            "simulation_dt": 0.01,
        }
    
    def test_environment_initialization(self, env_config):
        """Test environment initializes correctly."""
        env = ExcavationEnv(env_config)
        assert env.num_envs == 1
        assert env.robot_model == "m545"
    
    @pytest.mark.slow
    def test_environment_reset(self, env_config):
        """Test environment reset functionality."""
        env = ExcavationEnv(env_config)
        observations = env.reset()
        assert observations is not None
        assert isinstance(observations, torch.Tensor)
```

### Test Markers
- `@pytest.mark.unit`: Fast unit tests
- `@pytest.mark.integration`: Integration tests
- `@pytest.mark.slow`: Slow tests (skipped in fast runs)

## 📋 Pull Request Process

### Before Creating PR
1. **Run quality checks**: `make check`
2. **Run tests**: `make test`
3. **Update documentation** if needed
4. **Test in Docker container** to ensure compatibility

### PR Checklist
- [ ] Code follows style guidelines
- [ ] All tests pass
- [ ] Documentation updated (if applicable)
- [ ] No merge conflicts
- [ ] Descriptive commit messages
- [ ] PR description explains changes

### PR Description Template
```markdown
## Summary
Brief description of changes and motivation.

## Changes
- List specific changes made
- Include any breaking changes
- Mention new dependencies

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Documentation
- [ ] Code comments updated
- [ ] README updated (if applicable)
- [ ] API documentation updated (if applicable)
```

## 🤝 Code Review Guidelines

### For Authors
- Keep PRs focused and reasonably sized
- Write clear commit messages
- Respond promptly to review feedback
- Test thoroughly before requesting review

### For Reviewers
- Review for correctness, not just style
- Consider security implications
- Check test coverage
- Suggest improvements constructively

## 🐛 Reporting Issues

### Bug Reports
Include:
- Environment details (Docker image, Isaac Lab version)
- Steps to reproduce
- Expected vs actual behavior
- Error messages and logs
- Minimal code example

### Feature Requests
Include:
- Use case description
- Proposed solution (if any)
- Alternative solutions considered
- Impact on existing functionality

## 📚 Resources

- [Isaac Lab Documentation](https://isaac-sim.github.io/IsaacLab)
- [Isaac Sim Documentation](https://docs.omniverse.nvidia.com/isaacsim/latest/overview.html)
- [Ruff Documentation](https://docs.astral.sh/ruff/)
- [Python Type Hints](https://docs.python.org/3/library/typing.html)
- [Pytest Documentation](https://docs.pytest.org/)

## ❓ Getting Help

- **Documentation**: Check README.md and code comments
- **Issues**: Search existing issues before creating new ones
- **Discussion**: Use GitHub Discussions for questions
- **Code Review**: Tag maintainers for review feedback

Thank you for contributing to Moleworks Extension! 🚀
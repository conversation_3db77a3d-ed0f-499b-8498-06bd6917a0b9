# Omniverse
**/*.dmp
**/.thumbs
**/.cache
**/.local
**/.nvidia-omniverse

# Python
.DS_Store
**/*.egg-info/
**/__pycache__/
**/.pytest_cache/
**/*.pyc
**/*.pb

# IDE
**/.idea/
# Don't ignore the top-level .vscode directory as it is
# used to configure VS Code settings
!.vscode

# Outputs
**/runs/*
**/logs/* 
**/recordings/*
**/output/*
**/outputs/*
**/videos/*
**/wandb/*
**/.neptune/*
docker/artifacts/
*.tmp

# Docker/Singularity
**/*.sif
docker/cluster/exports/
docker/.container.cfg

# Isaac-Sim packman
_isaac_sim*
_repo
_build
.lastformat

# Singularity 
docker/cluster/exports/*

# docker
# docker/.env.moleworks_ext
# docker/.env.moleworks_ext-dev
# docker/cluster/.env.cluster
# Allow template files
!docker/.env.moleworks_ext.template
!docker/.env.moleworks_ext-dev.template
!docker/cluster/.env.cluster.template
!docker/cluster/.env.cluster

# Mount configuration (user-specific)
docker/.mount.config
docker/docker-compose.override.yaml
# Allow templates
!docker/.mount.config.template
!docker/docker-compose.override.yaml.template
# play and train scripts
scripts/rl/*
# isaaclab symlink

docker-compose.override.yaml
.mount.config


# vscode env
.env
.env_ros
.env_combined
# ros 
**/ros2_ws/install/
**/ros2_ws/build/
**/ros2_ws/log/

# scripts/rl/*

# cursor
.cursorrules
.cursorignore

# ignore .csv files
**/*.csv
# data 
source/lemoleworks_ext-ros2leworks_ext/moleworks_ext/tasks/ugep/excavation_utils/data/

# Additional Python patterns
**/*.py[cod]
**/*$py.class
**/*.so
**/build/
**/dist/
**/*.egg
**/MANIFEST
**/.coverage
**/.coverage.*
**/coverage.xml
**/*.cover
**/*.py,cover
**/.hypothesis/
**/htmlcov/
**/.tox/
**/.nox/
**/.mypy_cache/
**/.dmypy.json
**/dmypy.json
**/.pyre/
**/.pytype/

# Pre-commit
**/.pre-commit-cache/

# Environment files (but keep templates)
**/.env
**/.venv
**/env/
**/venv/
**/ENV/
**/env.bak/
**/venv.bak/

# Machine Learning specific
**/wandb/
**/mlruns/
**/tensorboard_logs/
**/lightning_logs/
**/checkpoints/
**/*.ckpt
**/*.pth
**/*.pt
**/model_*.pkl
**/*.h5
**/*.hdf5
**/*.npz

# Data files
**/data/
**/datasets/
**/*.json.gz
**/*.tar.gz
**/*.zip
**/*.pkl
**/*.pickle

# Jupyter Notebooks
**/.ipynb_checkpoints

# OS specific
**/.DS_Store
**/Thumbs.db
**/*.stackdump
**/Desktop.ini
**/.directory
**/.Trash-*
**/.nfs*

# Isaac Sim / Omniverse logs
**/kit_kernel*
**/omni.*.log*
**/carb.*.log*

# Cluster computing
**/*.out
**/*.err
**/*.job
**/slurm-*.out

# Backup files
**/*~
**/*.bak
**/*.orig
**/*.rej

.PHONY: help install install-dev lint format check test clean pre-commit update-deps

help:  ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

install:  ## Install the package
	pip install -e source/moleworks_ext/

install-dev:  ## Install the package in development mode with all dev dependencies
	pip install -e source/moleworks_ext/[dev]
	pre-commit install
	pre-commit install --hook-type commit-msg

lint:  ## Run linting with ruff
	ruff check . --fix
	ruff format .

format:  ## Format code with black and ruff
	black --line-length 120 source/moleworks_ext/ scripts/ tests/
	ruff format .

check:  ## Run all code quality checks
	ruff check .
	black --check --line-length 120 source/moleworks_ext/ scripts/ tests/
	mypy source/moleworks_ext/ --ignore-missing-imports
	bandit -r source/moleworks_ext/ -c pyproject.toml
	vulture source/moleworks_ext/ --min-confidence 80

test:  ## Run tests
	pytest tests/ -v --cov=source/moleworks_ext --cov-report=html --cov-report=term-missing

test-fast:  ## Run fast tests only
	pytest tests/ -v -m "not slow"

test-integration:  ## Run integration tests only
	pytest tests/ -v -m "integration"

pre-commit:  ## Run pre-commit hooks on all files
	pre-commit run --all-files

pre-commit-update:  ## Update pre-commit hooks
	pre-commit autoupdate

update-deps:  ## Update dependencies
	pip-compile pyproject.toml
	pip-compile --extra dev pyproject.toml

clean:  ## Clean up build artifacts and cache
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".ruff_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	find . -type d -name "htmlcov" -exec rm -rf {} +
	rm -f .coverage
	rm -rf build/ dist/

security:  ## Run security checks
	bandit -r source/moleworks_ext/ -c pyproject.toml
	safety check

docs:  ## Build documentation
	cd docs && make html

docs-serve:  ## Serve documentation locally
	cd docs && make livehtml

docker-build:  ## Build Docker image
	docker-compose build

docker-run:  ## Run Docker container
	docker-compose up

setup-dev:  ## Complete development setup
	$(MAKE) install-dev
	$(MAKE) pre-commit-update
	@echo "Development environment setup complete!"
	@echo "Run 'make check' to verify everything is working."

ci:  ## Run full CI pipeline locally
	$(MAKE) check
	$(MAKE) test
	$(MAKE) security
	@echo "All CI checks passed!"